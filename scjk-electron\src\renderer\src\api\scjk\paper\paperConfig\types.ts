export interface PaperConfigVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 车型
   */
  carType: string | number;
  carTypeName: string;

  /**
   * 科目
   */
  subject: string | number;
  subjectName: string;

  /**
   * 满分
   */
  full: number;

  /**
   * 及格
   */
  pass: number;

  /**
   * 时间
   */
  time: number;

  /**
   * 规则
   */
  rule: string;
}

export interface PaperConfigForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 车型
   */
  carType?: string | number;

  /**
   * 科目
   */
  subject?: string | number;

  /**
   * 满分
   */
  full?: number;

  /**
   * 及格
   */
  pass?: number;

  /**
   * 时间
   */
  time?: number;

  /**
   * 规则
   */
  rule?: string | Array<PaperConfigRule>;
}

export interface PaperConfigQuery extends PageQuery {
  /**
   * 车型
   */
  carType?: string | number;

  /**
   * 科目
   */
  subject?: string | number;

  /**
   * 满分
   */
  full?: number;

  /**
   * 及格
   */
  pass?: number;

  /**
   * 时间
   */
  time?: number;

  /**
   * 规则
   */
  rule?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

export interface PaperConfigRule {
  type?: string;
  label?: string;
  quantity?: number;
}
