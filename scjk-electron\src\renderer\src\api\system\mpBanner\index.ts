import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MpBannerVO, MpBannerForm, MpBannerQuery } from '@/api/system/mpBanner/types';

/**
 * 查询小程序轮播图配置列表
 * @param query
 * @returns {*}
 */

export const listMpBanner = (query?: MpBannerQuery): AxiosPromise<MpBannerVO[]> => {
  return request({
    url: '/system/mpBanner/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询小程序轮播图配置详细
 * @param id
 */
export const getMpBanner = (id: string | number): AxiosPromise<MpBannerVO> => {
  return request({
    url: '/system/mpBanner/' + id,
    method: 'get'
  });
};

/**
 * 新增小程序轮播图配置
 * @param data
 */
export const addMpBanner = (data: MpBannerForm) => {
  return request({
    url: '/system/mpBanner',
    method: 'post',
    data: data
  });
};

/**
 * 修改小程序轮播图配置
 * @param data
 */
export const updateMpBanner = (data: MpBannerForm) => {
  return request({
    url: '/system/mpBanner',
    method: 'put',
    data: data
  });
};

/**
 * 删除小程序轮播图配置
 * @param id
 */
export const delMpBanner = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/mpBanner/' + id,
    method: 'delete'
  });
};
