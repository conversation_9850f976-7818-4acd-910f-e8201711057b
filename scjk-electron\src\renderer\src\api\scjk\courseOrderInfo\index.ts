import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CourseOrderInfoVO, CourseOrderInfoForm, CourseOrderInfoQuery } from '@/api/scjk/courseOrderInfo/types';

/**
 * 查询课程预约记录列表
 * @param query
 * @returns {*}
 */

export const listCourseOrderInfo = (query?: CourseOrderInfoQuery): AxiosPromise<CourseOrderInfoVO[]> => {
  return request({
    url: '/scjk/courseOrderInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询课程预约记录详细
 * @param id
 */
export const getCourseOrderInfo = (id: string | number): AxiosPromise<CourseOrderInfoVO> => {
  return request({
    url: '/scjk/courseOrderInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增课程预约记录
 * @param data
 */
export const addCourseOrderInfo = (data: CourseOrderInfoForm) => {
  return request({
    url: '/scjk/courseOrderInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改课程预约记录
 * @param data
 */
export const updateCourseOrderInfo = (data: CourseOrderInfoForm) => {
  return request({
    url: '/scjk/courseOrderInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除课程预约记录
 * @param id
 */
export const delCourseOrderInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/courseOrderInfo/' + id,
    method: 'delete'
  });
};
