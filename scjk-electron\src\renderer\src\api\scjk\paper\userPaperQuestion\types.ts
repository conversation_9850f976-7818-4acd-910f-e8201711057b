import { Option } from '@/api/scjk/questionbank/question/types';
export interface UserPaperQuestionVO {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 试卷
   */
  paper?: number;

  /**
   * 题目
   */
  question?: number;

  /**
   * 分值
   */
  val: number;

  /**
   * 得分
   */
  score: number;

  /**
   * 顺序
   */
  sort: number;

  /**
   * 题目
   */
  title?: string;

  /**
   * 图片
   */
  image?: string;

  /**
   * 题型
   */
  type?: string;

  /**
   * 解析
   */
  analysis?: string;

  /**
   * 选项
   */
  options?: string | Array<Option>;

  /**
   * 选项
   */
  optionList?: Array<Option>;

  /**
   * 答案
   */
  answer?: string | Array<string>;

  /**
   * 答案
   */
  answerFormat?: string | Array<string>;

  /**
   * 正确答案
   */
  correct?: string;
  /**
   * 正确答案
   */
  correctFormat?: string;

  /**
   * 已切换
   */
  switched: boolean;

  /**
   * 技巧
   */
  skill?: string;
}
