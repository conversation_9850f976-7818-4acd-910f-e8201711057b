<template>
  <!--  <div v-if="settingsStore.student && ShowBanner" class="banner" :style="{ 'background-color': 'rgba(0, 0, 0, 0.5) !important' }">-->
  <!--    <div class="demo-image__lazy">-->
  <!--      <el-image v-for="url in urls" :key="url" :src="url" />-->
  <!--    </div>-->
  <!--  </div>-->
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <side-bar v-if="!sidebar.hide && !pure" class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide, 'main-container-pure': pure, 'main-container': !pure }">
      <!-- <el-scrollbar>
        <div :class="{ 'fixed-header': fixedHeader }">
          <navbar ref="navbarRef" @setLayout="setLayout" />
          <tags-view v-if="needTagsView" />
        </div>
        <app-main />
        <settings ref="settingRef" />
      </el-scrollbar> -->
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar v-if="!pure" ref="navbarRef" @set-layout="setLayout" />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <settings ref="settingRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
import SideBar from './components/Sidebar/index.vue';
import { AppMain, Navbar, Settings, TagsView } from './components';
import useAppStore from '@/store/modules/app';
import useSettingsStore from '@/store/modules/settings';
import { initWebSocket } from '@/utils/websocket';

const settingsStore = useSettingsStore();
const theme = computed(() => settingsStore.theme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const needTagsView = computed(() => settingsStore.tagsView);
const fixedHeader = computed(() => settingsStore.fixedHeader);
const pure = computed(() => settingsStore.pure);

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === 'mobile'
}));

const { width } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

watchEffect(() => {
  if (device.value === 'mobile') {
    useAppStore().closeSideBar({ withoutAnimation: false });
  }
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice('mobile');
    useAppStore().closeSideBar({ withoutAnimation: true });
  } else {
    useAppStore().toggleDevice('desktop');
  }
});

const navbarRef = ref<InstanceType<typeof Navbar>>();
const settingRef = ref<InstanceType<typeof Settings>>();

onMounted(() => {
  nextTick(() => {
    navbarRef.value?.initTenantList();
  });
});

onMounted(() => {
  // 统一使用客户端逻辑，并修复开发环境 WebSocket 协议问题
  // 在开发环境下， Vite_APP_WS_DOMAIN 通常是 localhost:port, 使用 ws://
  // 在生产环境下，可能配置了 HTTPS, Vite_APP_WS_DOMAIN 可能是域名，使用 wss://
  // 这里我们假设 VITE_APP_WS_DOMAIN 已经包含了正确的协议头，或者根据环境判断
  let wsProtocol = 'ws://'; // 默认为 ws
  if (import.meta.env.PROD && import.meta.env.VITE_APP_WS_DOMAIN && !import.meta.env.VITE_APP_WS_DOMAIN.startsWith('localhost')) {
    // 生产环境且 VITE_APP_WS_DOMAIN 不是 localhost (可能配置了域名和https)
    wsProtocol = 'wss://';
  }
  // 如果 VITE_APP_WS_DOMAIN 已经包含了协议头，则直接使用
  const wsDomain = import.meta.env.VITE_APP_WS_DOMAIN;
  const wsUrl = (wsDomain.startsWith('ws://') || wsDomain.startsWith('wss://')) 
                ? wsDomain + '/resource/websocket'
                : wsProtocol + wsDomain + '/resource/websocket';
  initWebSocket(wsUrl);
});

const handleClickOutside = () => {
  useAppStore().closeSideBar({ withoutAnimation: false });
};

const setLayout = () => {
  settingRef.value?.openSetting();
};
</script>

<style lang="scss" scoped>
@use '@/assets/styles/mixin.scss';
@use '@/assets/styles/variables.module.scss' as *;

.app-wrapper {
  @include mixin.clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
  background: $fixed-header-bg;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}

.main-container-pure {
  height: 100%;
  transition: margin-left 0.28s;
  margin-left: 10px;
  position: relative;
}
</style>
