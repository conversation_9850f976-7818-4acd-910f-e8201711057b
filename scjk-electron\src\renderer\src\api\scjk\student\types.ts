export interface StudentVO {
  /**
   * 报名车型
   */
  registeredCarModel: string;

  /**
   * 学员类型
   */
  // learnerType: string;

  /**
   * 所属教练
   */
  teacherId: string | number;

  /**
   * 所属驾校
   */
  drivingSchoolId: string | number;

  lienceseLevel: string;
}

export interface StudentForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 用户Id
   */
  userId?: string | number;

  /**
   * 报名车型
   */
  registeredCarModel?: string;

  /**
   * 学员类型
   */
  // learnerType?: string;

  /**
   * 所属教练
   */
  teacherId?: string | number;

  /**
   * 所属驾校
   */
  drivingSchoolId?: string | number;

  lienceseLevel?: string;
}

export interface StudentQuery extends PageQuery {
  /**
   * 报名车型
   */
  registeredCarModel?: string;

  /**
   * 学员类型
   */
  // learnerType?: string;

  /**
   * 所属教练
   */
  teacherId?: string | number;

  /**
   * 所属驾校
   */
  drivingSchoolId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}
