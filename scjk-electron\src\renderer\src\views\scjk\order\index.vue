<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="订单编号" prop="orderNo">
            <el-input v-model="queryParams.orderNo" placeholder="请输入订单编号" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="账单项目" prop="itemName">
            <el-input v-model="queryParams.itemName" placeholder="请输入账单项目" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="支付方式" prop="payment">
            <el-select v-model="queryParams.payment" placeholder="请选择支付方式" clearable>
              <el-option v-for="dict in payment" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="订单状态" prop="orderStatus">
            <el-select v-model="queryParams.orderStatus" placeholder="请选择订单状态" clearable>
              <el-option v-for="dict in order_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="用户昵称" prop="userId">
            <el-select-v2
              v-model="queryParams.userId"
              style="width: 240px"
              filterable
              remote
              :remote-method="remoteUserMethod"
              clearable
              :options="options"
              :props="props"
              :loading="loading"
              placeholder="请输入用户昵称"
              @change="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['scjk:order:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['scjk:order:edit']">修改</el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:order:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:order:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="false" label="主键" align="center" prop="id" />
        <el-table-column label="订单编号" align="center" prop="orderNo" />
        <el-table-column label="账单项目" align="center" prop="itemName" />
        <el-table-column label="支付方式" align="center" prop="payment">
          <template #default="scope">
            <dict-tag :options="payment" :value="scope.row.payment" />
          </template>
        </el-table-column>
        <el-table-column label="描述" align="center" prop="description" />
        <el-table-column label="用户id" align="center" prop="userId" />
        <el-table-column label="用户名称" align="center" prop="userName" />
        <el-table-column label="用户昵称" align="center" prop="nickName" />
        <el-table-column label="数量" align="center" prop="unitsNumber" />
        <el-table-column label="订单金额" align="center" prop="orderAmount" />
        <el-table-column label="支付成功时间" align="center" prop="paySuccessfulTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.paySuccessfulTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" prop="orderStatus">
          <template #default="scope">
            <dict-tag :options="order_status" :value="scope.row.orderStatus" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="支付" placement="top">
              <el-button v-if="scope.row.orderStatus === 'unpaid'" v-hasPermi="['scjk:order:pay']" link type="primary" @click="paybill(scope.row)"
                >支付订单</el-button
              >
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-if="scope.row.payment !== 'offline'" v-hasPermi="['scjk:order:wxQuery']" link type="primary" @click="queryBill(scope.row)"
                >查询订单</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改订单管理对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" append-to-body>
      <el-card style="max-width: 480px">
        <WxPayQrCode :form-data="formData" />
        <template #footer><el-button @click="cancel">取 消</el-button></template>
      </el-card>
    </el-dialog>
  </div>
</template>

<script setup name="Order" lang="ts">
import { listOrder, getOrder, delOrder, updateOrder, getOrderByOrderNo } from '@/api/scjk/order';
import { OrderVO, OrderQuery, OrderForm } from '@/api/scjk/order/types';
import { queryOrder } from '@/api/scjk/wxPay';
import WxPayQrCode from '@/components/wxPayQrCode/index.vue';
import store from '@/store';
import { log } from 'console';
import { or, re } from 'mathjs';
import { options } from 'axios';
import api from '@/api/system/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { order_status, payment } = toRefs<any>(proxy?.useDict('order_status', 'payment'));
const route = useRoute();

const orderNoParam = route.query.orderNo;

const orderList = ref<OrderVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const orderFormRef = ref<ElFormInstance>();
const qrcodeUrl = ref('');
const formData = ref<any>();
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: OrderForm = {
  id: undefined,
  subscriptionPlanId: undefined,
  orderNo: undefined,
  itemName: undefined,
  payment: undefined,
  description: undefined,
  userId: undefined,
  unitsNumber: undefined,
  orderAmount: undefined,
  paySuccessfulTime: undefined,
  orderStatus: undefined,
  delFlag: '0',
  userName: undefined
};
const data = reactive<PageData<OrderForm, OrderQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    subscriptionPlanId: undefined,
    orderNo: undefined,
    itemName: undefined,
    payment: undefined,
    description: undefined,
    userId: undefined,
    unitsNumber: undefined,
    orderAmount: undefined,
    paySuccessfulTime: undefined,
    orderStatus: undefined,
    params: {}
  },
  rules: {
    itemName: [{ required: true, message: '账单项目不能为空', trigger: 'blur' }],
    payment: [{ required: true, message: '支付方式不能为空', trigger: 'blur' }],
    userId: [{ required: true, message: '用户id不能为空', trigger: 'blur' }],
    unitsNumber: [{ required: true, message: '数量不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询订单管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listOrder(queryParams.value);
  orderList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  orderFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
interface ListItem {
  value: string;
  label: string;
}
const options = ref<ListItem[]>([]);
const props = {
  label: 'nickName',
  value: 'userId'
};
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);
const remoteUserMethod = async (query: string) => {
  if (query !== '') {
    queryParams.value.nickName = query;
    loading.value = false;
    await api.listUser(proxy?.addDateRange(queryParams.value, dateRange.value)).then((res) => {
      options.value = res.rows.map((item: any) => ({
        nickName: item.nickName,
        userId: item.userId
      }));
    });
  }
};
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: OrderVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

// /** 新增按钮操作 */
// const handleAdd = () => {
//   reset();
//   dialog.visible = true;
//   dialog.title = '添加订单管理';
// };

// /** 修改按钮操作 */
// const handleUpdate = async (row?: OrderVO) => {
//   reset();
//   const _id = row?.id || ids.value[0];
//   const res = await getOrder(_id);
//   Object.assign(form.value, res.data);
//   dialog.visible = true;
//   dialog.title = '支付订单';
// };

/** 提交按钮 */
// const submitForm = async () => {
//   buttonLoading.value = true;
//   if (form.value.id) {
//     form.value.orderStatus = 'paid';
//     await updateOrder(form.value).finally(() => (buttonLoading.value = false));
//   }
//   proxy?.$modal.msgSuccess('开通成功');
//   dialog.visible = false;
//   await getList();
// };

/** 删除按钮操作 */
const handleDelete = async (row?: OrderVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除订单管理编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delOrder(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/order/export',
    {
      ...queryParams.value
    },
    `order_${new Date().getTime()}.xlsx`
  );
};
const paybill = async (row: OrderVO) => {
  if (store.state.value.userId === row.userId) {
    formData.value = row;
    dialog.visible = true;
    dialog.title = '微信支付';
  } else {
    ElNotification({
      title: '错误',
      message: '只能支付自己的订单',
      type: 'error'
    });
  }
};
const queryBill = async (row: OrderVO) => {
  queryOrder(row).then((res) => {
    ElNotification({
      title: res.tradeStateDesc,
      message: h('i', { style: 'color: teal' }, res.tradeStateDesc + ';订单号：' + res.outTradeNo),
      type: 'success'
    });
  });
  getList();
};
const getSocketData = (event: any) => {
  if (event.detail.indexOf('SUCCESS') !== -1) {
    dialog.visible = false;
    getList();
  }
};
const openWxPayDiaglog = () => {
  if (orderNoParam) {
    getOrderByOrderNo(orderNoParam).then((res) => {
      if (res.data) {
        queryOrder(res.data).then((res) => {
          if (res.tradeState === 'SUCCESS') {
            ElNotification({
              title: res.tradeStateDesc,
              message: h('i', { style: 'color: teal' }, res.tradeStateDesc + ';订单号：' + res.outTradeNo),
              type: 'success'
            });
          } else {
            paybill(res.data);
          }
        });
      }
    });
  }
};
onMounted(() => {
  getList();
  openWxPayDiaglog();
  window.addEventListener('wxPayMessage', getSocketData);
});
</script>
