export interface SubscriptionRecordVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 订阅套餐
   */
  subscriptionPlanId: string | number;

  /**
   * 激活日期
   */
  activeDate: string;

  /**
   * 开通人
   */
  userId: string | number;

  /**
   * 激活账号
   */
  userName: string;

  /**
   * 生效时间
   */
  effectiveTime: string;

  /**
   * 到期时间
   */
  expiredDate: string;

  /**
   * 下次付款日期
   */
  nextPayDate: string;

  /**
   * 订单号
   */
  orderNo: string;

  /**
   * 开通方式
   */
  activeWay: string;

  /**
   * 订阅状态
   */
  status: string;

  subscriptionPlanName: string;
}

export interface SubscriptionRecordForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 订阅套餐
   */
  subscriptionPlanId?: string | number;

  /**
   * 激活日期
   */
  activeDate?: string;

  /**
   * 开通人
   */
  userId?: string | number;

  /**
   * 激活账号
   */
  userName?: string;

  /**
   * 生效时间
   */
  effectiveTime?: string;

  /**
   * 到期时间
   */
  expiredDate?: string;

  /**
   * 下次付款日期
   */
  nextPayDate?: string;

  /**
   * 订单号
   */
  orderNo?: string;

  /**
   * 开通方式
   */
  activeWay?: string;

  /**
   * 订阅状态
   */
  status?: string;
}

export interface SubscriptionRecordQuery extends PageQuery {
  /**
   * 订阅套餐
   */
  subscriptionPlanId?: string | number;

  /**
   * 激活日期
   */
  activeDate?: string;

  /**
   * 开通人
   */
  userId?: string | number;

  /**
   * 激活账号
   */
  userName?: string;

  /**
   * 生效时间
   */
  effectiveTime?: string;

  /**
   * 到期时间
   */
  expiredDate?: string;

  /**
   * 下次付款日期
   */
  nextPayDate?: string;

  /**
   * 订单号
   */
  orderNo?: string;

  /**
   * 开通方式
   */
  activeWay?: string;

  /**
   * 订阅状态
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
