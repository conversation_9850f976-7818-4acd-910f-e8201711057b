import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { BalanceAssetVO, BalanceAssetForm, BalanceAssetQuery } from '@/api/scjk/balanceAsset/types';

/**
 * 查询用户资产列表
 * @param query
 * @returns {*}
 */

export const listBalanceAsset = (query?: BalanceAssetQuery): AxiosPromise<BalanceAssetVO[]> => {
  return request({
    url: '/scjk/balanceAsset/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询用户资产详细
 * @param id
 */
export const getBalanceAsset = (id: string | number): AxiosPromise<BalanceAssetVO> => {
  return request({
    url: '/scjk/balanceAsset/' + id,
    method: 'get'
  });
};

/**
 * 新增用户资产
 * @param data
 */
export const addBalanceAsset = (data: BalanceAssetForm) => {
  return request({
    url: '/scjk/balanceAsset',
    method: 'post',
    data: data
  });
};

/**
 * 修改用户资产
 * @param data
 */
export const updateBalanceAsset = (data: BalanceAssetForm) => {
  return request({
    url: '/scjk/balanceAsset',
    method: 'put',
    data: data
  });
};

/**
 * 申请提现状态
 * @param id
 */
export const requestWithdrawal = (id: string | number, status: number) => {
  return request({
    url: '/scjk/balanceAsset/request/' + id + '/' + status,
    method: 'put'
  });
};
/**
 * 删除用户资产
 * @param id
 */
export const delBalanceAsset = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/balanceAsset/' + id,
    method: 'delete'
  });
};
