<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="名称" prop="name">
            <el-input v-model="queryParams.name" placeholder="请输入名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="车型" prop="carType">
            <car-type v-model="queryParams.carType" clearable placeholder="请选择车型" @keyup.enter="handleQuery"></car-type>
          </el-form-item>
          <el-form-item label="科目" prop="subject">
            <subject v-model="queryParams.subject" clearable placeholder="请选择科目" @keyup.enter="handleQuery"></subject>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template v-if="isStudent === true" #header>
        <el-row>
          <el-col :span="23"> </el-col>
          <el-col :span="1">
            <el-button type="danger" size="large" style="" @click="handleExit">退出</el-button>
          </el-col>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="answerGroupList">
        <el-table-column label="试卷" align="center" prop="name" />
        <el-table-column label="车型" align="center" prop="carTypeName" />
        <el-table-column label="科目" align="center" prop="subjectName" />
        <el-table-column label="做错" align="center" prop="error" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handlePractice(scope.row)">错题练习</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="PaperLog" lang="ts">
import useUserStore from '@/store/modules/user';
import { list, answerByGroup } from '@/api/scjk/answer';
import { AnswerVO, AnswerQuery } from '@/api/scjk/answer/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const answerGroupList = ref<AnswerVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const queryParams = ref<AnswerQuery>({
  name: undefined,
  carType: undefined,
  subject: undefined,
  pageNum: 1,
  pageSize: 10
});

const isStudent = computed(() => {
  return useUserStore().isStudent;
});

/** 查询试卷记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await list(queryParams.value);
  console.log(res);
  answerGroupList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/**
 * 错题练习
 */
const handlePractice = (row) => {
  // 跳转到错题练习页面
  proxy?.$router.push({
    path: '/answerPractice',
    query: { name: row.name, carType: row.carType, subject: row.subject, carTypeName: row.carTypeName, subjectName: row.subjectName }
  });
};

/**
 * 退出
 */
const handleExit = () => {
  proxy?.$router.push({
    path: '/index'
  });
};

onMounted(() => {
  getList();
});
</script>
