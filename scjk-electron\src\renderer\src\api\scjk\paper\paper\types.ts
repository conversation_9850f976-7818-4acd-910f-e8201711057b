import { QuestionVO } from '@/api/scjk/questionbank/question/types';
import { PaperQuestionVO } from '../paperQuestion/types';

export interface PaperVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 名称
   */
  name: string;

  /**
   * 分类
   */
  category: string;

  /**
   * 车型
   */
  carType: string | number;
  /**
   * 车型
   */
  carTypeName: string | number;

  /**
   * 科目
   */
  subject: string | number;
  /**
   * 科目
   */
  subjectName: string | number;

  /**
   * 满分
   */
  score: number;

  /**
   * 及格
   */
  pass?: number;

  /**
   * 时间
   */
  time: number;

  /**
   * 状态[A-启用,D-禁用]
   */
  status?: string;

  /**
   * 排序
   */
  sort: number;

  questions?: Array<PaperQuestionVO>;
}

export interface PaperForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 名称
   */
  name?: string;

  /**
   * 分类
   */
  category?: string;

  /**
   * 车型
   */
  carType?: string | number;

  /**
   * 科目
   */
  subject?: string | number;

  /**
   * 满分
   */
  full?: number;

  /**
   * 及格
   */
  pass?: number;

  /**
   * 时间
   */
  time?: number;

  /**
   * 状态[A-启用,D-禁用]
   */
  status?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 试题列表
   */
  questions: Array<QuestionVO>;
}

export interface PaperQuery extends PageQuery {
  /**
   * 名称
   */
  name?: string;

  /**
   * 分类
   */
  category?: string;

  /**
   * 车型
   */
  carType?: string;

  /**
   * 科目
   */
  subject?: string;

  /**
   * 分数
   */
  score?: number;

  /**
   * 时间
   */
  time?: number;

  /**
   * 状态[A-启用,D-禁用]
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

export interface SubjectQuestionVO {
  type?: string;
  label?: string;
  questions?: Array<QuestionVO>;
}
