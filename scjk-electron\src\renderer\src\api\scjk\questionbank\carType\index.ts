import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CarTypeVO, CarTypeForm, CarTypeQuery } from '@/api/scjk/questionbank/carType/types';
import { SelectVO } from '@/api/common/types';

/**
 * 查询车型列表
 * @param query
 * @returns {*}
 */

export const listCarType = (query?: CarTypeQuery): AxiosPromise<CarTypeVO[]> => {
  return request({
    url: '/scjk/carType/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询车型详细
 * @param id
 */
export const getCarType = (id: string | number): AxiosPromise<CarTypeVO> => {
  return request({
    url: '/scjk/carType/' + id,
    method: 'get'
  });
};

/**
 * 新增车型
 * @param data
 */
export const addCarType = (data: CarTypeForm) => {
  return request({
    url: '/scjk/carType',
    method: 'post',
    data: data
  });
};

/**
 * 修改车型
 * @param data
 */
export const updateCarType = (data: CarTypeForm) => {
  return request({
    url: '/scjk/carType',
    method: 'put',
    data: data
  });
};

/**
 * 删除车型
 * @param id
 */
export const delCarType = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/carType/' + id,
    method: 'delete'
  });
};

/**
 * 车型select列表
 * @param query
 * @returns {*}
 */

export const listSelect = (): AxiosPromise<SelectVO[]> => {
  return request({
    url: '/scjk/carType/selectList',
    method: 'get'
  });
};
