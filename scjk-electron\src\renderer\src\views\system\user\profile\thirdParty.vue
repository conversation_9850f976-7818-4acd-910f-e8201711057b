<template>
  <div class="p-2">
    <el-card shadow="never">
      <el-table v-loading="loading" :data="balanceAssetList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="false" label="主键" align="center" prop="id" />
        <el-table-column label="用户id" align="center" prop="userId" />
        <el-table-column label="资产余额" align="center" prop="balance" />
        <el-table-column label="用户编码" align="center" prop="userCode" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip effect="dark" content="满100元可提现" placement="top">
              <el-button
                v-if="scope.row.status !== '2' || scope.row.status === null"
                v-hasPermi="['scjk:balanceAsset:edit']"
                link
                type="primary"
                @click="withdrawals(scope.row)"
                >提现</el-button
              >
            </el-tooltip>
            <el-tooltip v-if="scope.row.status === '2'" content="提现中,亲耐心等待审核" effect="dark" placement="top">
              <el-button v-if="scope.row.status === '2'" link disabled type="warning">审核中</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="BalanceAsset" lang="ts">
import { listBalanceAsset, delBalanceAsset, addBalanceAsset, updateBalanceAsset, requestWithdrawal } from '@/api/scjk/balanceAsset/index';
import { BalanceAssetVO, BalanceAssetQuery, BalanceAssetForm } from '@/api/scjk/balanceAsset/types';
import { addWithdrawalsRecords } from '@/api/scjk/withdrawalsRecords';
import { WithdrawalsRecordsForm } from '@/api/scjk/withdrawalsRecords/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const balanceAssetList = ref<BalanceAssetVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const balanceAssetFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: BalanceAssetForm = {
  userId: undefined,
  balance: undefined,
  userCode: undefined
};

const drawForm: WithdrawalsRecordsForm = {
  id: undefined,
  userId: undefined,
  withdrawalsAmount: undefined,
  approver: undefined,
  status: undefined
};
const data = reactive<PageData<BalanceAssetForm, BalanceAssetQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    params: {}
  },
  rules: {
    userId: [{ required: true, message: '用户id不能为空', trigger: 'blur' }],
    balance: [{ required: true, message: '资产余额不能为空', trigger: 'blur' }],
    userCode: [{ required: true, message: '用户编码不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户资产列表 */
const getList = async () => {
  loading.value = true;
  const res = await listBalanceAsset(queryParams.value);
  balanceAssetList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  balanceAssetFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: BalanceAssetVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 提交按钮 */
const submitForm = () => {
  balanceAssetFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateBalanceAsset(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addBalanceAsset(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: BalanceAssetVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除用户资产编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delBalanceAsset(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

const withdrawals = async (row: BalanceAssetVO) => {
  // if (row.balance >= 100) {
  //   drawForm.withdrawalsAmount = row.balance;
  //   await proxy?.$modal.confirm('是否确认提交资产编号为"' + row.id + '"的提现申请？').finally(() => (loading.value = false));
  //   await addWithdrawalsRecords(drawForm).finally(() => (buttonLoading.value = false));
  //   let status = '2';
  //   await requestWithdrawal(row.id, status);
  //   proxy?.$modal.msgSuccess('已提交申请，请等待审批');
  // await getList();
  // } else {
  proxy?.$modal.msgError('不支持PC端用户提现');
  // }
};
onMounted(() => {
  getList();
});
</script>
