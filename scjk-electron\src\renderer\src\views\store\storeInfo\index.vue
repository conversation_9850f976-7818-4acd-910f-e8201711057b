<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="门店名称" prop="storeName">
              <el-input v-model="queryParams.storeName" placeholder="请输入门店名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="省份" prop="province">
              <el-input v-model="queryParams.province" placeholder="请输入省份" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="城市" prop="city">
              <el-input v-model="queryParams.city" placeholder="请输入城市" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['store:storeInfo:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['store:storeInfo:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['store:storeInfo:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['store:storeInfo:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="storeInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="门店名称" align="center" prop="storeName" />
        <el-table-column label="联系电话" align="center" prop="contactPhone" />
        <el-table-column label="省份" align="center" prop="province" />
        <el-table-column label="城市" align="center" prop="city" />
        <el-table-column label="经度" align="center" prop="longitude" />
        <el-table-column label="纬度" align="center" prop="latitude" />
        <el-table-column label="营业时间" align="center" prop="businessHours" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="排序字段" align="center" prop="sort" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['store:storeInfo:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['store:storeInfo:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改门店管理对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="700px" append-to-body>
      <el-form ref="storeInfoFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="门店名称" prop="storeName">
          <el-input v-model="form.storeName" placeholder="请输入门店名称" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="所属区域" prop="locationAreaCode">
          <el-cascader
            ref="cascaderAddr"
            v-model="form.locationAreaCode"
            :options="areaList"
            :props="{ value: 'value', label: 'label', emitPath: false }"
            placeholder="请选择所属区域"
            clearable
            style="width: 100%"
            @change="handleAreaChange"
          />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="form.province" placeholder="省份将根据所属区域自动填充" disabled />
        </el-form-item>
        <el-form-item label="城市" prop="city">
          <el-input v-model="form.city" placeholder="城市将根据所属区域自动填充" disabled />
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" />
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item>
          <div style="color: #909399; font-size: 12px; line-height: 1.5;">
            <i class="el-icon-info"></i>
            如需获取准确的经纬度坐标，请前往
            <a href="https://lbs.amap.com/tools/picker" target="_blank" style="color: #409EFF; text-decoration: none;">
              高德地图坐标拾取器
            </a>
            ，搜索地址后点击地图获取坐标
          </div>
        </el-form-item>
        <el-form-item label="营业时间" prop="businessHours">
          <el-input v-model="form.businessHours" placeholder="请输入营业时间(如: 09:00-22:00)" />
        </el-form-item>
        <el-form-item label="门店描述" prop="description">
          <Editor v-model="form.description" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="排序字段" prop="sort">
          <el-input-number v-model="form.sort" placeholder="请输入排序字段" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="StoreInfo" lang="ts">
import { listStoreInfo, getStoreInfo, delStoreInfo, addStoreInfo, updateStoreInfo } from '@/api/store/storeInfo';
import Editor from '@/components/Scjk/Editor/index.vue';
import { listAreaCascader } from '@/api/scjk/permit/area';
import { CascaderVO } from '@/api/common/types';
import { StoreInfoVO, StoreInfoQuery, StoreInfoForm } from '@/api/store/storeInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const storeInfoList = ref<StoreInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const areaList = ref<CascaderVO[]>([]);
const cascaderAddr = ref<any>(null);

const queryFormRef = ref<ElFormInstance>();
const storeInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: StoreInfoForm = {
  id: undefined,
  storeName: undefined,
  contactPhone: undefined,
  province: undefined,
  city: undefined,
  locationAreaCode: undefined,
  address: undefined,
  longitude: undefined,
  latitude: undefined,
  businessHours: undefined,
  description: undefined,
  status: undefined,
  sort: undefined
};
const data = reactive<PageData<StoreInfoForm, StoreInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    storeName: undefined,
    province: undefined,
    city: undefined,
    locationAreaCode: undefined,
    address: undefined,
    longitude: undefined,
    latitude: undefined,
    businessHours: undefined,
    description: undefined,
    status: undefined,
    sort: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键ID不能为空', trigger: 'blur' }],
    storeName: [{ required: true, message: '门店名称不能为空', trigger: 'blur' }],
    contactPhone: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
    locationAreaCode: [{ required: true, message: '所属区域不能为空', trigger: 'change' }],
    province: [{ required: true, message: '省份不能为空', trigger: 'blur' }],
    city: [{ required: true, message: '城市不能为空', trigger: 'blur' }],
    address: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
    longitude: [{ pattern: /^-?((1[0-7]\d)|(\d{1,2}))(\.\d+)?$/, message: '请输入有效的经度(-180到180之间的数字)', trigger: 'blur' }],
    latitude: [{ pattern: /^-?([1-8]?\d(\.\d+)?|90(\.0+)?)$/, message: '请输入有效的纬度(-90到90之间的数字)', trigger: 'blur' }],
    businessHours: [{ required: true, message: '营业时间不能为空', trigger: 'blur' }],
    description: [{ required: true, message: '门店描述不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态(0:禁用 1:启用)不能为空', trigger: 'change' }],
    sort: [{ required: true, message: '排序字段不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询门店管理列表 */
/** 查询区域下拉树结构 */
const getAreaList = async () => {
  try {
    const res = await listAreaCascader();
    areaList.value = res.data || []; // 确保 res.data 是期望的树形结构
  } catch (error) {
    console.error('Failed to load area list:', error);
    areaList.value = []; // 出错时清空，避免组件使用错误数据
  }
};

const handleAreaChange = (f: any) => {
  // emitPath: false 时，value 即为选中的最后一级的 id
  // 我们需要通过 cascaderAddr 获取完整的节点信息来填充省市
  if (cascaderAddr.value) {
    const nodesInfo = cascaderAddr.value.getCheckedNodes(false); // false 表示获取所有选中的节点信息
    if (nodesInfo && nodesInfo.length > 0 && nodesInfo[0] && nodesInfo[0].pathLabels) {
      form.value.province = nodesInfo[0].pathLabels[0] || '';
      form.value.city = nodesInfo[0].pathLabels[1] || '';
      // 如果有第三级（区/县），可以这样获取：
      // form.value.county = nodesInfo[0].pathLabels[2] || '';
      form.value.address = (nodesInfo[0].pathLabels[0] || '') + (nodesInfo[0].pathLabels[1] || '') + (nodesInfo[0].pathLabels[2] || '');
      
      // 设置经纬度信息（如果数据中包含）
      const selectedNode = nodesInfo[0].data; // 获取选中节点的原始数据
      if (selectedNode && selectedNode.lng !== undefined && selectedNode.lat !== undefined) {
        form.value.longitude = selectedNode.lng.toString();
        form.value.latitude = selectedNode.lat.toString();
      } else {
        form.value.longitude = '';
        form.value.latitude = '';
      }
    } else {
      form.value.province = '';
      form.value.city = '';
      form.value.longitude = '';
      form.value.latitude = '';
    }
  } else {
    form.value.province = '';
    form.value.city = '';
    form.value.longitude = '';
    form.value.latitude = '';
  }
};

/** 查询门店管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listStoreInfo(queryParams.value);
  storeInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  storeInfoFormRef.value?.resetFields();
  // 重置省市和区域代码
  form.value.province = undefined;
  form.value.city = undefined;
  form.value.locationAreaCode = undefined;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: StoreInfoVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  getAreaList(); // 新增时加载区域数据
  dialog.visible = true;
  dialog.title = '添加门店管理';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: StoreInfoVO) => {
  reset();
  await getAreaList(); // 修改时加载区域数据
  const _id = row?.id || ids.value[0];
  const res = await getStoreInfo(_id);
  Object.assign(form.value, res.data);

  // locationAreaCode 的回显通常不需要特殊处理，因为 v-model 会自动处理
  // 确保 res.data.locationAreaCode 的值是正确的最后一级 id 即可

  dialog.visible = true;
  dialog.title = '修改门店管理';
};

/** 提交按钮 */
const submitForm = () => {
  storeInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateStoreInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addStoreInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 状态切换操作 */
const handleStatusChange = async (row: StoreInfoVO) => {
  try {
    await updateStoreInfo({
      id: row.id,
      status: row.status
    });
    await getList();
  } catch (error) {
    // 如果更新失败，恢复原状态
    row.status = row.status === 1 ? 0 : 1;
  }
};

/** 删除按钮操作 */
const handleDelete = async (row?: StoreInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除门店管理编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delStoreInfo(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'store/storeInfo/export',
    {
      ...queryParams.value
    },
    `storeInfo_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
  getAreaList(); // 页面加载时获取区域数据
});
</script>
