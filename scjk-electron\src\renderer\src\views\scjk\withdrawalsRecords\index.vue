<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="申请人" prop="userId">
            <el-input v-model="queryParams.userId" placeholder="请输入申请人" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="提现金额" prop="withdrawalsAmount">
            <el-input
              v-model="queryParams.withdrawalsAmount"
              placeholder="请输入提现金额"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="审批人" prop="approver">
            <el-input v-model="queryParams.approver" placeholder="请输入审批人" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <!--          <el-form-item label="审批状态" prop="status">-->
          <!--            <el-select v-model="queryParams.status" placeholder="请选择审批状态" clearable>-->
          <!--              <el-option v-for="dict in approval_state" :key="dict.value" :label="dict.label" :value="dict.value" />-->
          <!--            </el-select>-->
          <!--          </el-form-item>-->
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:withdrawalsRecords:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="待审核" name="first"
          ><el-table v-loading="loading" :data="withdrawalsRecordsList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column v-if="false" label="主键" align="center" prop="id" />
            <el-table-column label="申请人" align="center" prop="userName" />
            <el-table-column label="提现金额" align="center" prop="withdrawalsAmount" />
            <el-table-column label="审批人" align="center" prop="approver" />
            <el-table-column label="审批状态" align="center" prop="status">
              <template #default="scope">
                <dict-tag :options="approval_state" :value="scope.row.status" />
              </template>
            </el-table-column>
            <el-table-column label="申请时间" align="center" prop="createTime" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-tooltip content="通过" placement="top">
                  <el-button
                    v-if="scope.row.status == 1"
                    v-hasPermi="['scjk:withdrawalsRecords:approve']"
                    link
                    type="primary"
                    icon="Select"
                    @click="setStatus(scope.row, '2')"
                  ></el-button>
                </el-tooltip>
                <el-tooltip content="驳回" placement="top">
                  <el-button
                    v-if="scope.row.status == 1"
                    v-hasPermi="['scjk:withdrawalsRecords:approve']"
                    link
                    type="danger"
                    icon="CloseBold"
                    @click="setStatus(scope.row, '3')"
                  ></el-button>
                </el-tooltip>
              </template>
            </el-table-column> </el-table
        ></el-tab-pane>
        <el-tab-pane label="已完成" name="second">
          <el-table v-loading="loading" :data="withdrawalsRecordsList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column v-if="false" label="主键" align="center" prop="id" />
            <el-table-column label="申请人" align="center" prop="userName" />
            <el-table-column label="提现金额" align="center" prop="withdrawalsAmount" />
            <el-table-column label="审批人" align="center" prop="approver" />
            <el-table-column label="审批状态" align="center" prop="status">
              <template #default="scope">
                <dict-tag :options="approval_state" :value="scope.row.status" />
              </template>
            </el-table-column>
            <el-table-column label="申请时间" align="center" prop="createTime" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="已驳回" name="third"
          ><el-table v-loading="loading" :data="withdrawalsRecordsList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column v-if="false" label="主键" align="center" prop="id" />
            <el-table-column label="申请人" align="center" prop="userName" />
            <el-table-column label="提现金额" align="center" prop="withdrawalsAmount" />
            <el-table-column label="审批人" align="center" prop="approver" />
            <el-table-column label="审批状态" align="center" prop="status">
              <template #default="scope">
                <dict-tag :options="approval_state" :value="scope.row.status" />
              </template>
            </el-table-column>
            <el-table-column label="申请时间" align="center" prop="createTime" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改提现记录对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" append-to-body>
      <el-form ref="withdrawalsRecordsFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请人" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入申请人" />
        </el-form-item>
        <el-form-item label="提现金额" prop="withdrawalsAmount">
          <el-input v-model="form.withdrawalsAmount" placeholder="请输入提现金额" />
        </el-form-item>
        <el-form-item label="审批人" prop="approver">
          <el-input v-model="form.approver" placeholder="请输入审批人" />
        </el-form-item>
        <el-form-item label="审批状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择审批状态">
            <el-option v-for="dict in approval_state" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WithdrawalsRecords" lang="ts">
import {
  listWithdrawalsRecords,
  getWithdrawalsRecords,
  delWithdrawalsRecords,
  addWithdrawalsRecords,
  updateWithdrawalsRecords,
  requestApprealStatus
} from '@/api/scjk/withdrawalsRecords';
import { WithdrawalsRecordsVO, WithdrawalsRecordsQuery, WithdrawalsRecordsForm } from '@/api/scjk/withdrawalsRecords/types';
import type { TabsPaneContext } from 'element-plus';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { approval_state } = toRefs<any>(proxy?.useDict('approval_state'));

const withdrawalsRecordsList = ref<WithdrawalsRecordsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const withdrawalsRecordsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const activeName = ref('first');

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab.props.name, event);
  if (tab.props.name == 'first') {
    activeName.value = 'first';
    getList('1');
  }
  if (tab.props.name == 'second') {
    activeName.value = 'second';
    getList('2');
  }
  if (tab.props.name == 'third') {
    activeName.value = 'third';
    getList('3');
  }
};
const initFormData: WithdrawalsRecordsForm = {
  id: undefined,
  userId: undefined,
  withdrawalsAmount: undefined,
  approver: undefined,
  status: undefined
};
const data = reactive<PageData<WithdrawalsRecordsForm, WithdrawalsRecordsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    withdrawalsAmount: undefined,
    approver: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    userId: [{ required: true, message: '申请人不能为空', trigger: 'blur' }],
    withdrawalsAmount: [{ required: true, message: '提现金额不能为空', trigger: 'blur' }],
    approver: [{ required: true, message: '审批人不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '审批状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询提现记录列表 */
const getList = async (status: string) => {
  loading.value = true;
  queryParams.value.status = status;
  const res = await listWithdrawalsRecords(queryParams.value);
  withdrawalsRecordsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/**

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  withdrawalsRecordsFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  activeName.value = 'first';
  getList('1');
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();

  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WithdrawalsRecordsVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加提现记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: WithdrawalsRecordsVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getWithdrawalsRecords(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改提现记录';
};

/** 提交按钮 */
const submitForm = () => {
  withdrawalsRecordsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateWithdrawalsRecords(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWithdrawalsRecords(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList('1');
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: WithdrawalsRecordsVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除提现记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delWithdrawalsRecords(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList('1');
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/withdrawalsRecords/export',
    {
      ...queryParams.value
    },
    `withdrawalsRecords_${new Date().getTime()}.xlsx`
  );
};
const setStatus = async (row: WithdrawalsRecordsVO, status: string) => {
  try {
    buttonLoading.value = true;
    row.status = status;
    await updateWithdrawalsRecords(row);
    proxy?.$modal.msgSuccess('操作成功');
  } catch (error) {
    console.error('操作失败:', error);
  } finally {
    buttonLoading.value = false;
    activeName.value = 'first';
    await getList('1');
  }
};
onMounted(() => {
  activeName.value = 'first';
  getList('1');
});
</script>
<style>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>
