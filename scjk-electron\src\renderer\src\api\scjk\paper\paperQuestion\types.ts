import { Option } from '@/api/scjk/questionbank/question/types';
export interface PaperQuestionVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 试卷
   */
  paper: string | number;

  /**
   * 题目
   */
  question: string | number;

  /**
   * 分数
   */
  val: number;

  /**
   * 得分
   */
  score?: number;

  /**
   * 顺序
   */
  sort: number;

  /**
   * 题目
   */
  title?: string;

  /**
   * 图片
   */
  image?: string;

  /**
   * 题型
   */
  type?: string;

  /**
   * 解析
   */
  analysis?: string;

  /**
   * 选项
   */
  options?: string | Array<Option>;

  /**
   * 选项
   */
  optionList?: Array<Option>;

  /**
   * 答案
   */
  answer?: string | Array<string>;

  /**
   * 正确答案
   */
  correct?: string;

  /**
   * 已切换
   */
  switched: boolean;

  /**
   * 技巧
   */
  skill?: string;
}

export interface PaperQuestionForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 试卷
   */
  paper?: string | number;

  /**
   * 题目
   */
  question?: string | number;

  /**
   * 分数
   */
  val?: number;

  /**
   * 顺序
   */
  sort?: number;

  /**
   * 题目
   */
  title?: string;

  /**
   * 图片
   */
  image: string;

  /**
   * 选项
   */
  options: string;

  /**
   * 答案
   */
  answer: string;

  /**
   * 解析
   */
  analysis: string;

  /**
   * 解析音频
   */
  analysisAudio?: string;

  /**
   * 解析视频
   */
  analysisVideo?: string;

  /**
   * 技巧
   */
  skill: string;

  /**
   * 技巧音频
   */
  skillAudio?: string;

  /**
   * 技巧视频
   */
  skillVideo?: string;

  /**
   * 题型[单选-S,多选-M,判断-J]
   */
  type: string;
}

export interface PaperQuestionQuery extends PageQuery {
  /**
   * 试卷
   */
  paper?: string | number;

  /**
   * 题目
   */
  question?: string | number;

  /**
   * 分数
   */
  val?: number;

  /**
   * 顺序
   */
  sort?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
