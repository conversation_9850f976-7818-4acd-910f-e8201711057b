<template>
  <div class="e-page">
    <div v-if="questionVOs.length > 0" id="mnks">
      <div class="e-container">
        <div class="e-container-left"></div>
        <div class="content-main">
          <div class="content-top">
            <div class="content-top-left">
              <info-form class="exam-title" title="科目试卷">
                <div class="exam-title-content">
                  {{ infoVO.paperName ? infoVO.paperName : '模拟考试' }}
                </div>
              </info-form>
              <info-form class="user-info" title="用户信息">
                <div class="info-img">
                  <img :src="infoVO.avatar" alt="" />
                </div>
                <div class="user-info-base">
                  <div class="user-info-row">
                    <span class="info-lable">姓名：</span><span class="info-value">{{ infoVO.nickname }}</span>
                  </div>
                  <div class="user-info-row">
                    <span class="info-lable">性别：</span><span class="info-value">{{ infoVO.sex }}</span>
                  </div>
                  <div class="user-info-row">
                    <span class="info-lable">车型：</span><span class="info-value">{{ infoVO.carTypeName }}</span>
                  </div>
                  <div class="user-info-row">
                    <span class="info-lable">科目：</span><span class="info-value">{{ infoVO.subjectName }}</span>
                  </div>
                </div>
              </info-form>
              <info-form class="time-remaining" title="剩余时间">
                <div class="time-count">{{ minutes }}:{{ seconds }}</div>
              </info-form>
            </div>
            <div class="content-top-center">
              <div v-if="current" class="exam-topic info-block">
                <div class="info-block-title">考试题目</div>
                <div class="exam-topic-content">
                  <div
                    class="topic-question"
                    :class="{ 'red-font': highlightMulti === true && isMulti === true }"
                    :style="{ fontSize: userStore.fontSize + 'px', color: highlightMulti === true && isMulti === true ? '' : userStore.fontColor }"
                  >
                    {{ current.sort }}. （<dict-tag :options="question_type" :value="current.type" style="display: inline-block" />题）
                    <div>{{ formatTitle }}</div>
                  </div>
                  <div class="topic-option" :class="{ 'topic-option-dotted': !isJudge }">
                    <template v-if="isJudge"> </template>
                    <template v-else>
                      <p
                        v-for="option of current.optionList"
                        :key="option.prefix"
                        :class="{ 'red-font': highlightMulti === true && isMulti === true }"
                        :style="{
                          fontSize: userStore.fontSize + 'px',
                          color: highlightMulti === true && isMulti === true ? '' : userStore.fontColor
                        }"
                      >
                        {{ option.prefix }}：{{ option.content }}
                      </p>
                    </template>
                  </div>
                  <div class="topic-answer" v-if="current.switched === true">
                    <span class="topic-answer-title">正确答案：</span>
                    <span class="topic-answer-value">{{ current?.correctFormat }}</span
                    >&nbsp;&nbsp;&nbsp;
                    <span class="topic-answer-tip" @click="answerTipDialogVisible = true">本题解析>></span>
                  </div>
                </div>
              </div>
              <div class="exam-answer">
                <div class="exam-answer-title">您选择的答案：</div>
                <div class="exam-answer-value">{{ current.answerFormat }}</div>
                <div class="exam-answer-buttons">
                  <span class="fl"></span>
                  <li
                    v-for="option of current.optionList"
                    :key="option.prefix"
                    :anser="option.prefix"
                    class="exam-answer-buttons-button"
                    :class="{ 'exam-answer-buttons-button-active': isIncludes(current.answer, option.prefix) }"
                    @click="selectAnswer(option.prefix)"
                  >
                    <template v-if="isJudge">
                      <el-icon v-if="isOptionCorrect(option.content)"><Select /></el-icon>
                      <el-icon v-else><CloseBold /></el-icon>
                    </template>
                    <template v-else>
                      {{ option.prefix }}
                    </template>
                  </li>
                </div>
              </div>
            </div>
            <div class="content-top-right">
              <div class="answer-card">
                <li
                  v-for="(item, index) in 11"
                  :key="index"
                  class="answer-card-cell answer-card-cell-head answer-card-cell-head-top"
                  :class="{ 'answer-card-cell-head-left': index === 0 }"
                >
                  <p>{{ index === 0 ? '题号' : index + '列' }}</p>
                </li>
                <template v-for="(question, index) of questionVOs" :key="question.id">
                  <li v-if="(index + 1) % 10 === 1" class="answer-card-cell answer-card-cell-head answer-card-cell-head-left">
                    <p>{{ rowNum(index) + '行' }}</p>
                  </li>

                  <li
                    :data-order="question.sort"
                    class="answer-card-cell answer-card-cell-value"
                    :class="{
                      'answer-card-cell-none': question.switched !== true,
                      'answer-card-cell-active': currentIndex === index,
                      'answer-card-cell-answered': question.answer
                    }"
                    @click="loadQuestion(index)"
                  >
                    <p
                      v-if="question.switched === true && question.answer"
                      :class="{
                        'answer-card-cell-right': isCorrect(question.correct, question.answer),
                        'answer-card-cell-wrong': !isCorrect(question.correct, question.answer),
                        'answer-card-cell-judge': isJudgeQuestion(question.type),
                        'answer-card-cell-multiple-2': question.answer.length == 2,
                        'answer-card-cell-multiple-3': question.answer.length == 3,
                        'answer-card-cell-multiple-4': question.answer.length == 4
                      }"
                    >
                      {{ question.type === 'J' ? question.answerFormat : question.answer }}
                    </p>
                  </li>
                </template>
              </div>
            </div>
          </div>
          <div class="content-middle">
            <div v-if="current" class="operation-hint">
              <div class="operation-hint-title">
                <span>操作提示：{{ isSingle ? '单选' : isJudge ? '判断' : isMulti ? '多选' : current.type }}题</span>
              </div>
              <div class="operation-hint-value">
                <span
                  >{{ isSingle ? '单选' : isJudge ? '判断' : isMulti ? '多选' : current.type }}题，{{
                    current.type === 'J' ? '请判断对错！' : '请选择正确的答案！'
                  }}</span
                >
              </div>
            </div>
            <div v-if="skill === true" class="skill-button">
              <el-button size="large" type="success" @click="skillTipDialogVisible = true">技巧</el-button>
            </div>
            <div v-if="!mock" class="collect-button">
              <el-button
                size="large"
                :type="collected ? 'danger' : 'info'"
                :icon="Star"
                :loading="collectedBtnLoading"
                :disabled="collectedBtnLoading"
                @click="() => handleAddCollection(current.question)"
                >{{ collected ? '已收藏' : '收藏' }}</el-button
              >
            </div>
            <div class="action-buttons" :style="{ 'padding-right': review === true || error === true ? '323px' : '402px' }">
              <el-button class="action-buttons-button" :disabled="!hasPrev" size="large" @click="prevQuestion">上一题</el-button>
              <el-button class="action-buttons-button" :disabled="!hasNext" size="large" @click="nextQuestion">下一题</el-button>
              <el-button class="action-buttons-button" v-loading.fullscreen.lock="fullscreenLoading" size="large" @click="submit">交卷</el-button>
              <el-button
                class="action-buttons-button"
                v-if="review === true || error === true || collection === true"
                size="large"
                @click="handleBack"
                >退出</el-button
              >
            </div>
          </div>
          <div class="content-bottom">
            <div class="image-info info-block">
              <!-- :style="{height: (screenHeight-546)+'px'}" -->
              <div class="info-block-title">图片信息</div>
              <el-image
                v-if="current && current.image"
                :src="current.image"
                fit="contain"
                :preview-src-list="[current.image]"
                preview-teleported
                style="height: 92%"
              >
                <template #error>
                  <div class="image-slot">
                    <el-icon><picture-filled /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="image-info-noimage" v-else>&nbsp;</div>
            </div>
          </div>
        </div>
        <div class="e-container-right"></div>
      </div>
    </div>

    <!-- 技巧 -->
    <el-dialog ref="skillTipDialog" v-model="skillTipDialogVisible" title="技巧" width="700">
      <div class="dialogform">
        <div v-if="current.skill" class="paragraph">
          技巧：
          <div class="divinline" v-html="current.skill"></div>
        </div>
        <div v-if="current.analysis" class="paragraph">
          解析：
          <div class="divinline" v-html="current.analysis"></div>
        </div>
        <el-collapse v-model="skillTipDialogActiveName" accordion>
          <el-collapse-item v-if="current.skillAudio" title="技巧音频" name="skillAudio">
            <xgplayer
              ref="stSkillAudioPlayer"
              :playerId="`player-${current.skillAudio}`"
              :url="current.skillAudio"
              type="audio"
              style="display: block; max-width: 100%; margin: 0 auto"
            ></xgplayer>
          </el-collapse-item>
          <el-collapse-item v-if="current.skillVideo" title="技巧视频" name="skillVideo">
            <xgplayer
              ref="stSkillVideoPlayer"
              :playerId="`player-${current.skillVideo}`"
              :url="current.skillVideo"
              plugin="mp4"
              style="display: block; max-width: 100%; margin: 0 auto"
            ></xgplayer>
          </el-collapse-item>
          <el-collapse-item v-if="current.analysisAudio" title="解析音频" name="analysisAudio">
            <xgplayer
              ref="stAnalysisAudioPlayer"
              :playerId="`player-${current.analysisAudio}`"
              :url="current.analysisAudio"
              type="audio"
              style="display: block; max-width: 100%; margin: 0 auto"
            ></xgplayer>
          </el-collapse-item>
          <el-collapse-item v-if="current.analysisVideo" title="解析视频" name="analysisVideo">
            <xgplayer
              ref="stAnalysisVideoPlayer"
              :playerId="`player-${current.analysisVideo}`"
              :url="current.analysisVideo"
              plugin="mp4"
              style="display: block; max-width: 100%; margin: 0 auto"
            ></xgplayer>
          </el-collapse-item>
        </el-collapse>
      </div>

      <template #footer>
        <div class="dialog-footer" style="text-align: center">
          <el-button @click="closeSkillTipDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 答案解答 -->
    <el-dialog ref="answerTipDialog" v-model="answerTipDialogVisible" title="错题解答" width="700">
      <div class="dialogform">
        <div v-if="current.title" class="paragraph">
          {{ current.sort }}、
          <div class="divinline" v-html="current.title"></div>
        </div>
        <div v-if="current.image" class="paragraph paragraph-img">
          <img class="q-img" :src="current.image" alt="" />
        </div>
        <div class="paragraph">正确选项：{{ current.correctFormat }}，你的答案：{{ current.answerFormat }}</div>
        <div v-if="current.analysis" class="paragraph">
          解析：
          <div class="divinline" v-html="current.analysis"></div>
        </div>
        <div v-if="current.skill" class="paragraph">
          技巧：
          <div class="divinline" v-html="current.skill"></div>
        </div>
        <el-collapse v-model="answerTipDialogActiveName" accordion>
          <el-collapse-item v-if="current.skillAudio" title="技巧音频" name="skillAudio">
            <xgplayer
              ref="atSkillAudioPlayer"
              :playerId="`player-${current.skillAudio}`"
              :url="current.skillAudio"
              type="audio"
              style="display: block; max-width: 100%; margin: 0 auto"
            ></xgplayer>
          </el-collapse-item>
          <el-collapse-item v-if="current.skillVideo" title="技巧视频" name="skillVideo">
            <xgplayer
              ref="atSkillVideoPlayer"
              :playerId="`player-${current.skillVideo}`"
              :url="current.skillVideo"
              plugin="mp4"
              style="display: block; max-width: 100%; margin: 0 auto"
            ></xgplayer>
          </el-collapse-item>
          <el-collapse-item v-if="current.analysisAudio" title="解析音频" name="analysisAudio">
            <xgplayer
              ref="atAnalysisAudioPlayer"
              :playerId="`player-${current.analysisAudio}`"
              :url="current.analysisAudio"
              type="audio"
              style="display: block; max-width: 100%; margin: 0 auto"
            ></xgplayer>
          </el-collapse-item>
          <el-collapse-item v-if="current.analysisVideo" title="解析视频" name="analysisVideo">
            <xgplayer
              ref="atAnalysisVideoPlayer"
              :playerId="`player-${current.analysisVideo}`"
              :url="current.analysisVideo"
              plugin="mp4"
              style="display: block; max-width: 100%; margin: 0 auto"
            ></xgplayer>
          </el-collapse-item>
        </el-collapse>
      </div>

      <template #footer>
        <div class="dialog-footer" style="text-align: center">
          <el-button @click="closeAnswerTipDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!--交卷提示-->
    <el-dialog ref="confirmDialog" v-model="confirmDialogVisible" title="交卷确认" width="500">
      <div class="dialogform">
        <div class="paragraph">
          <p class="waring">您当前还有{{ unknow }}道题没做！！！</p>
          <br />
          <p>操作提示：</p>
          <p>1、点击【确认交卷】，将提交考试成绩，结束考试。</p>
          <p>2、点击【继续答题】，将继续答题,倒计时结束无法继续答题</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="confirmDialogVisible = false">继续答题</el-button>
          <el-button type="primary" @click="confirmSubmit"> 确认交卷 </el-button>
        </div>
      </template>
    </el-dialog>

    <!--考试结果-->
    <el-dialog
      ref="resultDialog"
      v-model="resultDialogVisible"
      :title="`${paper === true || mock === true ? '考试结果' : '结果'}`"
      fullscreen
      :show-close="false"
    >
      <div class="dialogform">
        <div v-if="paper === true || mock === true" class="result-score">
          <p>
            <span class="score">{{ score }}</span
            ><span class="fen">&nbsp;分</span>
          </p>
          <p class="pass">{{ isPass ? '合格' : '不及格' }}</p>
        </div>

        <div class="result-data">
          <div>
            答对：<span class="right">{{ right }}</span>
          </div>
          <div>
            答错：<span class="wrong">{{ wrong }}</span>
          </div>
          <div>
            未答：&nbsp;<span class="un">{{ unknow }}</span>
          </div>
          <div>
            用时：<span class="time">{{ spendTime }}</span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer" style="text-align: center">
          <el-button size="large" @click="handleBack">返回</el-button>
          <el-button size="large" @click="handleReview">{{ error === true ? '错题练习' : '错题查看' }}</el-button>

          <template v-if="paper === true || mock === true">
            <el-button size="large" @click="handleAgain"> 再试一次 </el-button>
          </template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Practice" lang="ts">
import { propTypes } from '@/utils/propTypes';
import * as math from 'mathjs';
import { PracticeInfoVO, PracticeQuestionVO } from '@/api/scjk/practice/types';
import useUserStore from '@/store/modules/user';
import { Star } from '@element-plus/icons-vue';
import { addCollection, isCollected } from '@/api/scjk/collection';

const userStore = useUserStore();
import { onMounted, onBeforeUnmount, ref } from 'vue';
const screenHeight = ref(0);

const setScreenHeight = () => {
  screenHeight.value = window.innerHeight;
};
onMounted(() => {
  // 获取屏幕高度
  setScreenHeight();
  // 监听窗口大小变化，更新屏幕高度
  window.addEventListener('resize', setScreenHeight);
});
onBeforeUnmount(() => {
  window.removeEventListener('resize', setScreenHeight);
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { question_type } = toRefs<any>(proxy?.useDict('question_type'));

const props = defineProps({
  // 信息
  info: {
    type: Object as PropType<PracticeInfoVO>,
    default: null
  },
  // 列表
  list: {
    type: Object as PropType<PracticeQuestionVO>,
    default: null
  },
  // 是否试卷考试
  paper: propTypes.bool.def(false),
  // 是否模拟考试
  mock: propTypes.bool.def(false),
  // 是否错题记录
  error: propTypes.bool.def(false),
  // 是否错题查看
  review: propTypes.bool.def(false),
  // 是否显示技巧按钮
  skill: propTypes.bool.def(false),
  // 是否高亮多选
  highlightMulti: propTypes.bool.def(false),
  // 是否收藏
  collection: propTypes.bool.def(false)
});
const emit = defineEmits(['submit', 'back', 'again', 'review', 'fresh']);

// 信息
const infoVO = ref<PracticeInfoVO>(null);
// 题库
const questionVOs = ref<Array<PracticeQuestionVO>>([]);
// 当前
const current = ref<PracticeQuestionVO>(null);
// 当前index
const currentIndex = ref<number>(0);
// 上一个
const hasPrev = ref<boolean>(false);
// 下一个
const hasNext = ref<boolean>(true);
// 已作答
const answer = ref<number>(0);
// 正确
const right = ref<number>(0);
// 错误
const wrong = ref<number>(0);
// 未作答
const unknow = computed(() => {
  return questionVOs.value.length - answer.value;
});
// 得分
const score = ref<number>(0);
// 是否通过
const isPass = computed(() => {
  return infoVO.value.pass <= score.value;
});
// 是否单选
const isSingle = computed(() => {
  return current.value && current.value.type === 'S';
});
// 是否判断
const isJudge = computed(() => {
  return current.value && current.value.type === 'J';
});
// 是否多选
const isMulti = computed(() => {
  return current.value && current.value.type === 'M';
});
// 是否作答
const isAnswer = computed(() => {
  return current.value.answer !== undefined && current.value.answer !== null && current.value.answer !== '' && current.value.answer !== 'null';
});
// 是否正确
const isRight = computed(() => {
  return current.value?.correct?.replaceAll(',', '') === current.value?.answer;
});
// 技巧折叠面板激活
const skillTipDialogActiveName = ref<string>();
// 解析折叠面板激活
const answerTipDialogActiveName = ref<string>();

// 格式化题目
const formatTitle = computed(() => {
  let result = current.value?.title.replace(/<.+?>/g, '');
  result = result.replace(/ /gi, '');
  result = result.replace(/\s/gi, '');
  return result;
});

// 分
const minutes = ref<string | number>('45');
// 秒
const seconds = ref<string | number>('00');
// 用时秒
const spendSeconds = ref<number>(0);
// 用时格式化
const spendTime = computed(() => {
  let m = Math.floor(spendSeconds.value / 60);
  let min = m < 10 ? '0' + m : m + '';

  let s = Math.floor(spendSeconds.value % 60);
  let sec = s < 10 ? '0' + s : s + '';

  return min + ':' + sec;
});
// interval
const interval = ref();

// 遮罩层
const fullscreenLoading = ref<boolean>(false);
// 技巧窗口
const skillTipDialogVisible = ref<boolean>(false);
// 答题解答窗口
const answerTipDialogVisible = ref<boolean>(false);
const answerTipDialogTrigger = ref<boolean>(false);
// 交卷提示窗口
const confirmDialogVisible = ref<boolean>(false);
// 考试结果窗口
const resultDialogVisible = ref<boolean>(false);

//
const stSkillAudioPlayer = ref();
const stSkillVidieoPlayer = ref();
const stAnalysisAudioPlayer = ref();
const stAnalysisVideoPlayer = ref();

const atSkillAudioPlayer = ref();
const atSkillVidieoPlayer = ref();
const atAnalysisAudioPlayer = ref();
const atAnalysisVideoPlayer = ref();

const collected = ref<boolean>(false);
const collectedBtnLoading = ref<boolean>(false);

/**
 * 倒计时
 * @param min 分钟
 */
const countDown = (min: number) => {
  let maxtime: number = math.multiply(min, 60);
  interval.value = setInterval(async () => {
    spendSeconds.value = spendSeconds.value + 1;
    if (maxtime >= 0) {
      let m = Math.floor(maxtime / 60);
      minutes.value = m < 10 ? '0' + m : m + '';

      let s = Math.floor(maxtime % 60);
      seconds.value = s < 10 ? '0' + s : s + '';

      --maxtime;
    } else {
      // 模拟考试才强制交卷
      if (props.mock === true) {
        confirmSubmit();
      }
    }
  }, 1000);
};

/**
 * 下一个题
 */
const nextQuestion = () => {
  if (!hasNext.value) {
    return;
  }

  // 已经作答且未切换过
  if (isAnswer.value && !current.value.switched) {
    answerQuestion(true);
  } else {
    switchQuestion(true);
  }
};

/**
 * 上一个题
 */
const prevQuestion = () => {
  if (!hasPrev.value) {
    return;
  }

  // 已经作答且未切换过
  if (isAnswer.value && !current.value.switched) {
    answerQuestion(false);
  } else {
    switchQuestion(false);
  }
};

/**
 * 答题
 * @param isNext 是否下一个
 * @param index 下标
 */
const answerQuestion = (isNext: boolean, index: number = undefined) => {
  // 标记已切换
  current.value.switched = true;
  // 设置计数
  setCount();

  // 提示对错
  if (!isRight.value) {
    answerTipDialogVisible.value = true;
    answerTipDialogTrigger.value = true;
  } else {
    switchQuestion(isNext, index);
  }
};

/**
 * 关闭技巧弹窗
 */
const closeSkillTipDialog = () => {
  // 关闭窗口
  skillTipDialogVisible.value = false;
  skillTipDialogActiveName.value = '';
};

/**
 * 关闭答题解答窗口
 */
const closeAnswerTipDialog = () => {
  // 关闭窗口
  answerTipDialogVisible.value = false;
  answerTipDialogActiveName.value = '';
  // 自动下一题(就算点上一题也是下一题)
  if (!answerTipDialogTrigger.value || !hasNext.value) {
    return;
  }

  answerTipDialogTrigger.value = false;
  switchQuestion(true);
};

/**
 * 切换当前试题
 * @param isNext 是否下一个
 * @param index 下表（优先）
 */
const switchQuestion = async (isNext: boolean, index: number = undefined) => {
  if (index === undefined && ((isNext && !hasNext.value) || (!isNext && !hasPrev.value))) {
    return;
  }

  currentIndex.value = index !== undefined ? index : isNext ? currentIndex.value + 1 : currentIndex.value - 1;
  current.value = questionVOs.value[currentIndex.value];
  collected.value = await checkCollected(current.value.question);
  setPrevAndNext();
};

/**
 * 设置上一题、下一题
 */
const setPrevAndNext = () => {
  hasPrev.value = currentIndex.value !== 0;
  hasNext.value = currentIndex.value !== questionVOs.value?.length - 1;
};

/**
 * 选择答案
 * @param prefix
 */
const selectAnswer = (prefix: string) => {
  // 有答案且切换过，就不能再更改
  if (current.value?.switched === true) {
    return;
  }

  if (isMulti.value) {
    // 如果已经选择了，就取消选择
    if (current.value.answer?.includes(prefix)) {
      // 加'g'，删除字符串里所有的
      let reg1 = new RegExp(prefix, 'g');
      current.value.answer = current.value.answer.replace(reg1, '');
      return;
    }

    // 排序
    let answer = (current.value.answer === null ? '' : current.value.answer) + prefix;
    let answerSort = Array.from(answer).sort().join('');
    current.value.answer = answerSort;
  } else {
    current.value.answer = prefix;
  }

  // 设置答案格式化
  let answer: string = '';
  for (const option of current.value.optionList) {
    if (option.prefix === current.value.answer) {
      answer = option.content;
    }
  }

  current.value.answerFormat = isJudge.value ? (isOptionCorrect(answer) ? '√' : '×') : current.value.answer;
};

/**
 * 设置计数
 */
const setCount = () => {
  answer.value = answer.value + 1;
  if (current.value?.correct?.replaceAll(',', '') === current.value?.answer) {
    right.value = right.value + 1;
  } else {
    wrong.value = wrong.value + 1;
  }
};

/**
 * 加载试题
 * @param index
 */
const loadQuestion = (index: number) => {
  // 已经作答且未切换过
  if (isAnswer.value && !current.value.switched) {
    answerQuestion(false, index);
  } else {
    switchQuestion(false, index);
  }
};

/**
 * 是否包含
 * @param str1
 * @param str2
 */
const isIncludes = (str1: string, str2: string) => {
  if (!str1 || !str2) {
    return false;
  }
  return str1.includes(str2);
};

/**
 * 是否正确
 * @param str1
 * @param str2
 */
const isCorrect = (str1: string, str2: string) => {
  if (!str1 || !str2) {
    return false;
  }

  return str1.replaceAll(',', '') === str2;
};

/**
 * 是否判断题
 * @param type
 */
const isJudgeQuestion = (type: string) => {
  return type === 'J';
};

/**
 * 交卷
 */
const submit = async () => {
  // 提交当前试题的答案
  if (!current.value.switched && current.value.answer) {
    // 标记已切换
    current.value.switched = true;
    // 设置计数
    setCount();
  }

  // 确认窗口
  confirmDialogVisible.value = true;
};

/**
 * 确认交卷
 */
const confirmSubmit = () => {
  try {
    clearInterval(interval.value);
  } catch (e) {}
  fullscreenLoading.value = true;
  confirmDialogVisible.value = false;
  infoVO.value.spendTime = spendSeconds.value;
  emit('submit', questionVOs.value, spendSeconds.value);

  // 不关闭遮罩层
};

/**
 * 返回
 */
const handleBack = async () => {
  emit('back');
};

/**
 * 查看
 */
const handleReview = async () => {
  if (props.error === true) {
    emit('review', []);
    return;
  }

  let errorQuestions = [];
  questionVOs.value.forEach((question) => {
    // 已答题但结果不正确的
    if (
      question.answer !== undefined &&
      question.answer !== null &&
      question.answer !== '' &&
      question.answer !== 'null' &&
      question.correct.replaceAll(',', '') !== question.answer
    ) {
      question.answer = '';
      question.answerFormat = '';
      question.switched = false;
      errorQuestions.push(question);
    }
  });

  emit('review', errorQuestions);
};

/**
 * 再试一次
 */
const handleAgain = () => {
  resultDialogVisible.value = false;
  reset();
  emit('again');
};

/**
 * 重置
 */
const reset = () => {
  infoVO.value = {};
  questionVOs.value = [];
  current.value = {
    id: undefined,
    paper: undefined,
    question: undefined,
    val: 0,
    score: 0,
    sort: 1,
    title: '',
    image: '',
    type: '',
    options: undefined,
    optionList: [],
    answer: undefined,
    correct: '',
    switched: false
  };
  currentIndex.value = 0;
  hasPrev.value = false;
  hasNext.value = true;
  answer.value = 0;
  right.value = 0;
  wrong.value = 0;
  confirmDialogVisible.value = false;
  resultDialogVisible.value = false;
  fullscreenLoading.value = false;
};

/**
 * 获取行号
 * @param index 下标
 */
const rowNum = (index: number) => {
  return index / 10 + 1;
};

/**
 * 是不是正确选项
 * @param str
 */
const isOptionCorrect = (str: string) => {
  return '对' === str || '正确' === str;
};

/**
 * 关闭遮罩层，给父级调用
 */
const closeFullscreen = (sc = 0) => {
  score.value = sc;
  fullscreenLoading.value = false;
  resultDialogVisible.value = true;
};

/**
 * 是否收藏
 * @param question
 */
const checkCollected = async (question: string | number) => {
  if (!question) {
    return false;
  }
  const res = await isCollected(question);
  return res.data;
};

/**
 * 收藏/取消收藏
 * @param question
 */
const handleAddCollection = async (question: string | number) => {
  collectedBtnLoading.value = true;
  await addCollection(question);
  collected.value = await checkCollected(question);
  collectedBtnLoading.value = false;
};

// 题库改变
watch(
  () => props.list,
  async () => {
    // 先重置
    reset();
    questionVOs.value = props.list;
    infoVO.value = props.info;
    if (!props.paper) {
      infoVO.value.paperName = props.list[0].name;
    }
    if (questionVOs.value.length < 1) {
      return;
    }

    current.value = questionVOs.value[currentIndex.value];
    collected.value = await checkCollected(current.value.question);
    setPrevAndNext();
    minutes.value = infoVO.value.time;
    countDown(minutes.value);
  }
);

// 提示自动交卷
watch(wrong, (val) => {
  // 模拟考试提示自动交卷，100题的错了超过11题或50题的错了超过6题
  if (props.mock === true && ((questionVOs.value.length === 100 && val > 10) || (questionVOs.value.length === 50 && val > 5))) {
    confirmDialogVisible.value = true;
  }
});

watch(spendSeconds, (val) => {
  // 每十分钟
  if (spendSeconds.value % 600 === 0) {
    emit('fresh');
  }
});

// 切换折叠面板时暂停播放
watch(skillTipDialogActiveName, (val) => {
  if (skillTipDialogVisible.value === true && val !== 'skillAudio') {
    if (stSkillAudioPlayer.value && stSkillAudioPlayer.value.hasOwnProperty('playerIns')) {
      stSkillAudioPlayer.value.playerIns().pause();
    }
  }

  if (skillTipDialogVisible.value === true && val !== 'skillVideo') {
    if (stSkillVidieoPlayer.value && stSkillVidieoPlayer.value.hasOwnProperty('playerIns')) {
      stSkillVidieoPlayer.value.playerIns().pause();
    }
  }

  if (skillTipDialogVisible.value === true && val !== 'analysisAudio') {
    if (stAnalysisAudioPlayer.value && stAnalysisAudioPlayer.value.hasOwnProperty('playerIns')) {
      stAnalysisAudioPlayer.value.playerIns().pause();
    }
  }

  if (skillTipDialogVisible.value === true && val !== 'analysisVideo') {
    if (stAnalysisVideoPlayer.value && stAnalysisVideoPlayer.value.hasOwnProperty('playerIns')) {
      stAnalysisVideoPlayer.value.playerIns().pause();
    }
  }
});
watch(answerTipDialogActiveName, (val) => {
  if (skillTipDialogVisible.value === true && val !== 'skillAudio') {
    if (atSkillAudioPlayer.value && atSkillAudioPlayer.value.hasOwnProperty('playerIns')) {
      atSkillAudioPlayer.value.playerIns().pause();
    }
  }

  if (skillTipDialogVisible.value === true && val !== 'skillVideo') {
    if (atSkillVidieoPlayer.value && atSkillVidieoPlayer.value.hasOwnProperty('playerIns')) {
      atSkillVidieoPlayer.value.playerIns().pause();
    }
  }

  if (skillTipDialogVisible.value === true && val !== 'analysisAudio') {
    if (atAnalysisAudioPlayer.value && atAnalysisAudioPlayer.value.hasOwnProperty('playerIns')) {
      atAnalysisAudioPlayer.value.playerIns().pause();
    }
  }

  if (skillTipDialogVisible.value === true && val !== 'analysisVideo') {
    if (atAnalysisVideoPlayer.value && atAnalysisVideoPlayer.value.hasOwnProperty('playerIns')) {
      atAnalysisVideoPlayer.value.playerIns().pause();
    }
  }
});

defineExpose({
  closeFullscreen
});
</script>

<style lang="scss" src="@/assets/styles/exam-v2.scss" scoped></style>
