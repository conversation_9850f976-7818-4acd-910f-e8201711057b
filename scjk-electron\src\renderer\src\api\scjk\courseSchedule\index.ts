import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CourseScheduleVO, CourseScheduleForm, CourseScheduleQuery } from '@/api/scjk/courseSchedule/types';

/**
 * 查询课程安排列表
 * @param query
 * @returns {*}
 */

export const listCourseSchedule = (query?: CourseScheduleQuery): AxiosPromise<CourseScheduleVO[]> => {
  return request({
    url: '/scjk/courseSchedule/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询课程安排详细
 * @param id
 */
export const getCourseSchedule = (id: string | number): AxiosPromise<CourseScheduleVO> => {
  return request({
    url: '/scjk/courseSchedule/' + id,
    method: 'get'
  });
};

/**
 * 新增课程安排
 * @param data
 */
export const addCourseSchedule = (data: CourseScheduleForm) => {
  return request({
    url: '/scjk/courseSchedule',
    method: 'post',
    data: data
  });
};

/**
 * 修改课程安排
 * @param data
 */
export const updateCourseSchedule = (data: CourseScheduleForm) => {
  return request({
    url: '/scjk/courseSchedule',
    method: 'put',
    data: data
  });
};

/**
 * 删除课程安排
 * @param id
 */
export const delCourseSchedule = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/courseSchedule/' + id,
    method: 'delete'
  });
};
