<template>
  <div class="container">
    <div class="condition">
      <el-row :gutter="20">
        <el-col v-if="!isStudent" :span="6"> 车型： <car-type v-model="carType" :clearable="false" placeholder="请选择车型"></car-type> </el-col>
        <el-col :span="6">
          科目：
          <el-select v-model="subject" placeholder="请选择科目" :clearable="false" style="width: 240px">
            <el-option v-for="item in learnSubjects" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-col>
        <el-col :span="12"></el-col>
      </el-row>
    </div>
    <el-row :gutter="50">
      <el-col :span="12">
        <el-card class="paper-category">
          <template #header>
            <div class="card-header">
              <span>精选试卷</span>
            </div>
          </template>
          <el-scrollbar height="600px">
            <el-row v-for="paper of exquisitePaperList" :key="paper.id">
              <el-col :span="24">
                <template v-if="isExquisiteStrengthen(paper)">
                  <el-button class="paper exquisite-strengthen" @click="handleExam(paper.id)">{{ paper.name }}</el-button>
                </template>
                <template v-else>
                  <el-button class="paper exquisite" @click="handleExam(paper.id)">{{ paper.name }}</el-button>
                </template>
              </el-col>
            </el-row>
          </el-scrollbar>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="paper-category">
          <template #header>
            <div class="card-header">
              <span>基础试卷</span>
            </div>
          </template>
          <el-scrollbar height="600px">
            <el-row v-for="paper of basePaperList" :key="paper.id">
              <el-col :span="24"
                ><el-button class="paper base" @click="handleExam(paper.id)">{{ paper.name }}</el-button></el-col
              >
            </el-row>
          </el-scrollbar>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script setup name="PaperExam" lang="ts">
import { all } from '@/api/scjk/paper/paper';
import { PaperVO } from '@/api/scjk/paper/paper/types';
import useUserStore from '@/store/modules/user';
import useSubjectStore from '@/store/modules/subject';
import CarType from '@/components/Scjk/CarType/index.vue';

const userStore = useUserStore();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 默认车型
const carType = ref<string>('1784750369061953537');
// 默认科目
const subject = ref<string>('1784754526804234242');

// 科目列表
const subjects = computed(() => {
  return useSubjectStore().list;
});
// 报考的科目
const learnSubjects = ref([]);

// 精选试卷列表
const exquisitePaperList = ref<Array<PaperVO>>([]);
// 常识试卷列表
const basePaperList = ref<Array<PaperVO>>([]);
// 是否是学生(默认是)
const isStudent = ref<boolean>(true);

/**
 * 加载试卷
 * @param carType
 * @param subject
 */
const loadPaper = (carType, subject) => {
  if (carType === undefined || carType === '' || subject === undefined || subject === '') {
    return;
  }

  exquisitePaperList.value = [];
  basePaperList.value = [];
  all(carType, subject).then((res) => {
    // 分组
    for (const paper of res.data) {
      if (paper.category === 'E' || paper.category === 'S') {
        exquisitePaperList.value.push(paper);
        continue;
      }

      if (paper.category === 'B') {
        basePaperList.value.push(paper);
        continue;
      }

      // 暂不考略其他分组
    }
  });
};

/**
 * 去考试
 * @param paperId
 */
const handleExam = (paperId: number) => {
  // 跳转至考试页面
  proxy?.$router.push({ path: '/exam/paperExam', query: { paper: paperId } });
};

/**
 * 是不是强化试卷
 * @package paper
 */
const isExquisiteStrengthen = (paper: PaperVO) => {
  return paper.name.includes('强化') || paper.category === 'S';
};

// 监听车型改变
watch(carType, () => {
  // 加载试卷
  loadPaper(carType.value, subject.value);
});

// 监听科目改变
watch(subject, () => {
  // 加载试卷
  loadPaper(carType.value, subject.value);
});

onMounted(() => {
  learnSubjects.value = subjects.value.map((item) => item);

  // 默认科目
  isStudent.value = userStore.roleName === '学员';
  if (isStudent.value) {
    let carModelId = userStore.leaningInfo?.carModelId;
    if (carModelId === undefined || carModelId === null || carModelId === '') {
      carType.value = '';
      proxy.$modal.msgError('未找到您报考的车型，请联系管理员！');
      return;
    }
    carType.value = carModelId;

    let studentSubjectStr = userStore.leaningInfo?.subject;
    if (studentSubjectStr === undefined || studentSubjectStr === null || studentSubjectStr === '') {
      subject.value = '';
      learnSubjects.value = [];
      proxy.$modal.msgError('未找到您报考的科目，请联系管理员！');
      return;
    }

    let studentSubjects = studentSubjectStr.split(',');
    learnSubjects.value = subjects.value.filter((item) => {
      return studentSubjects.includes(String(item.id));
    });
    if (learnSubjects.value.length < 1) {
      subject.value = '';
      proxy.$modal.msgError('未找到您报考的科目，请联系管理员！');
      return;
    }
    subject.value = learnSubjects.value[0].id;
  }

  loadPaper(carType.value, subject.value);
});
</script>
<style>
.container {
  padding: 30px;
  height: 100%;
}

.condition {
  margin-bottom: 20px;
}
.paper-category {
}

.paper {
  height: 50px;
  text-align: center;
  line-height: 50px;
  border-radius: 10px;
  color: white;
  font-size: 19px;
  width: 100%;
}

.exquisite {
  background-color: #f15a70;
}

.exquisite-strengthen {
  background-color: #fd9f2e;
}

.base {
  background-color: #4b8bf4;
}
</style>
