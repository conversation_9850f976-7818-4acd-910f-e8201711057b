import { UserPaperQuestionVO } from '../userPaperQuestion/types';

export interface UserPaperVO {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 名称
   */
  name?: string;

  /**
   * 分类
   */
  category?: string;

  /**
   * 车型
   */
  carType?: number;

  /**
   * 科目
   */
  subject?: number;

  /**
   * 分数
   */
  full?: number;

  /**
   * 及格
   */
  pass?: number;

  /**
   * 时间
   */
  time?: number;

  /**
   * 状态[A-启用,D-禁用]
   */
  status?: string;

  questions?: Array<UserPaperQuestionVO>;

  /**
   * 用时
   */
  spendTime?: number;
}

export interface UserPaperRankVO {
  name: string;
  score: number;
}
