import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CollectionVO, CollectionForm, CollectionQuery } from '@/api/scjk/collection/types';

/**
 * 查询用户收藏列表
 * @param query
 * @returns {*}
 */

export const listCollection = (query?: CollectionQuery): AxiosPromise<CollectionVO[]> => {
  return request({
    url: '/scjk/collection/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询用户收藏详细
 * @param id
 */
export const getCollection = (id: string | number): AxiosPromise<CollectionVO> => {
  return request({
    url: '/scjk/collection/' + id,
    method: 'get'
  });
};

/**
 * 新增用户收藏
 * @param data
 */
export const addCollection = (question: string | number) => {
  return request({
    url: '/scjk/collection',
    method: 'post',
    data: { question: question }
  });
};

/**
 * 删除用户收藏
 * @param id
 */
export const delCollection = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/collection/' + id,
    method: 'delete'
  });
};

/**
 * 是否
 * @param id
 */
export const isCollected = (question: string | number): AxiosPromise<boolean> => {
  return request({
    url: '/scjk/collection/isCollected/' + question,
    method: 'get'
  });
};

/**
 * 查询用户收藏列表
 * @param query
 * @returns {*}
 */

export const allCollection = (): AxiosPromise<CollectionVO[]> => {
  return request({
    url: '/scjk/collection/all',
    method: 'get'
  });
};
