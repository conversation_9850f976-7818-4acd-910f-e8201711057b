import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TeacherVO, TeacherForm, TeacherQuery } from '@/api/scjk/teacher/types';

/**
 * 查询教师管理列表
 * @param query
 * @returns {*}
 */

export const listTeacher = (query?: TeacherQuery): AxiosPromise<TeacherVO[]> => {
  return request({
    url: '/scjk/teacher/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询教师管理详细
 * @param id
 */
export const getTeacher = (id: string | number): AxiosPromise<TeacherVO> => {
  return request({
    url: '/scjk/teacher/' + id,
    method: 'get'
  });
};
/**
 * 根据UserId查询教师详细
 * @param id
 */
export const getTeacherInfo = (userId: string | number): AxiosPromise<TeacherVO> => {
  return request({
    url: '/scjk/teacher/getTechInfo/' + userId,
    method: 'get'
  });
};

/**
 * 新增教师管理
 * @param data
 */
export const addTeacher = (data: TeacherForm) => {
  return request({
    url: '/scjk/teacher',
    method: 'post',
    data: data
  });
};

/**
 * 修改教师管理
 * @param data
 */
export const updateTeacher = (data: TeacherForm) => {
  return request({
    url: '/scjk/teacher',
    method: 'put',
    data: data
  });
};

/**
 * 删除教师管理
 * @param id
 */
export const delTeacher = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/teacher/' + id,
    method: 'delete'
  });
};

export const queryTeacherList = () => {
  return request({
    url: '/scjk/teacher/queryTeacherList',
    method: 'get'
  });
};
