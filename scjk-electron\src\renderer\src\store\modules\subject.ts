import { to } from 'await-to-js';
import { subjects } from '@/api/scjk/questionbank/subject';
import store from '@/store';
import { SubjectVO } from '@/api/scjk/questionbank/subject/types';

export const useSubjectStore = defineStore('subject', () => {
  const list = ref<SubjectVO[]>([]);

  // 获取科目信息
  const getInfo = async (): Promise<void> => {
    const [err, res] = await to(subjects());
    if (res) {
      const data = res.data;
      list.value = data;
      return Promise.resolve();
    }
    return Promise.reject(err);
  };

  // 清空
  const clear = async (): Promise<void> => {
    list.value = [];
  };

  return {
    list,
    getInfo,
    clear
  };
});

export default useSubjectStore;
// 非setup
export function useSubjectStoreHook() {
  return useSubjectStore(store);
}
