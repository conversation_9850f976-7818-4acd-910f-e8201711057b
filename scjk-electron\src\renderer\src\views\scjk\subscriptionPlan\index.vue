<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="产品名称" prop="product">
            <el-input v-model="queryParams.product" placeholder="请输入产品名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="套餐名称" prop="plan">
            <el-input v-model="queryParams.plan" placeholder="请输入套餐名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="套餐类型" prop="planType">
            <el-select v-model="queryParams.planType" placeholder="请选择套餐类型" clearable>
              <el-option v-for="dict in paid_plans" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="套餐描述" prop="planDescription">
            <el-input v-model="queryParams.planDescription" placeholder="请输入套餐描述" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="套餐价格" prop="price">
            <el-input v-model="queryParams.price" placeholder="请输入套餐价格" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="套餐周期" prop="planPeriod">
            <el-input v-model="queryParams.planPeriod" placeholder="请输入套餐周期" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="套餐状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择套餐状态" clearable>
              <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:subscriptionPlan:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:subscriptionPlan:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:subscriptionPlan:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:subscriptionPlan:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="subscriptionPlanList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="false" label="主键" align="center" prop="id" />
        <el-table-column label="产品名称" align="center" prop="product" />
        <el-table-column label="套餐名称" align="center" prop="plan" />
        <el-table-column label="套餐类型" align="center" prop="planType">
          <template #default="scope">
            <dict-tag :options="paid_plans" :value="scope.row.planType" />
          </template>
        </el-table-column>
        <el-table-column label="套餐描述" align="center" prop="planDescription" />
        <el-table-column label="套餐价格" align="center" prop="price" />
        <el-table-column label="套餐周期" align="center" prop="planPeriod" />
        <el-table-column label="套餐状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['scjk:subscriptionPlan:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['scjk:subscriptionPlan:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改订阅套餐信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="680px" append-to-body>
      <el-form ref="subscriptionPlanFormRef" :model="form" :rules="rules" label-width="auto" style="max-width: 600px">
        <el-form-item label="产品名称" prop="product">
          <el-input v-model="form.product" value="速诚驾考VIP" disabled placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="套餐名称" prop="plan">
          <el-input v-model="form.plan" placeholder="请输入套餐名称" />
        </el-form-item>
        <el-form-item label="套餐类型" prop="planType">
          <el-select v-model="form.planType" placeholder="请选择套餐类型" @change="planTypeChange">
            <el-option v-for="dict in paid_plans" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="套餐周期" prop="planPeriod">
          <el-slider v-model="form.planPeriod" :marks="marks" :max="365" :min="3" :show-input="false" />
          <!-- <el-input v-model="form.planPeriod" placeholder="请输入套餐周期" :suffix-icon="Calendar" /> -->
        </el-form-item>
        <el-form-item label="套餐内容" prop="planContent">
          <editor v-model="form.planContent" />
        </el-form-item>
        <el-form-item label="套餐描述" prop="planDescription">
          <el-input v-model="form.planDescription" placeholder="请输入套餐描述" />
        </el-form-item>
        <el-form-item label="套餐价格" prop="price">
          <el-input v-model="form.price" placeholder="请输入套餐价格" :prefix-icon="Money" oninput="value=value.replace(/[^\d.]/g,'')">
            <template #prepend>¥</template>
            <template v-if="form.planType === 'trial'" #append>元/天</template>
            <template v-if="form.planType === 'paid_monthly'" #append>元/月</template>
            <template v-if="form.planType === 'paid_quarterly'" #append>元/季</template>
            <template v-if="form.planType === 'paid_half_yearly'" #append>元/半年</template>
            <template v-if="form.planType === 'paid_yearly'" #append>元/年</template>
          </el-input>
        </el-form-item>
        <el-form-item label="套餐状态" prop="status">
          <el-switch v-model="form.status" active-value="Y" inactive-value="N"></el-switch>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" maxlength="100" placeholder="请输入备注" show-word-limit type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SubscriptionPlan" lang="ts">
import {
  listSubscriptionPlan,
  getSubscriptionPlan,
  delSubscriptionPlan,
  addSubscriptionPlan,
  updateSubscriptionPlan
} from '@/api/scjk/subscriptionPlan';
import { SubscriptionPlanVO, SubscriptionPlanQuery, SubscriptionPlanForm } from '@/api/scjk/subscriptionPlan/types';
import { Money } from '@element-plus/icons-vue';
import type { CSSProperties } from 'vue';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no, paid_plans } = toRefs<any>(proxy?.useDict('sys_yes_no', 'paid_plans'));

const subscriptionPlanList = ref<SubscriptionPlanVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const subscriptionPlanFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

interface Mark {
  style: CSSProperties;
  label: string;
}
type Marks = Record<number, Mark | string>;
const marks = reactive<Marks>({
  3: {
    style: {
      marginTop: '3px'
    },
    label: '3天'
  },
  31: {
    style: {
      marginTop: '3px'
    },
    label: '31天'
  },
  90: {
    style: {
      marginTop: '3px'
    },
    label: '90天'
  },
  // 180: '180天',
  365: {
    style: {
      marginTop: '3px'
    },
    label: '365天'
  },
  180: {
    style: {
      marginTop: '3px',
      color: '#1989FA'
    },
    label: '180天'
  }
});

const initFormData: SubscriptionPlanForm = {
  id: undefined,
  product: '速诚驾考VIP',
  plan: undefined,
  planType: undefined,
  planDescription: undefined,
  price: undefined,
  planPeriod: undefined,
  status: undefined,
  remark: undefined,
  planContent: undefined
};
const data = reactive<PageData<SubscriptionPlanForm, SubscriptionPlanQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    product: undefined,
    plan: undefined,
    planType: undefined,
    planDescription: undefined,
    price: undefined,
    planPeriod: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    product: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
    plan: [{ required: true, message: '套餐名称不能为空', trigger: 'blur' }],
    planType: [{ required: true, message: '套餐类型不能为空', trigger: 'change' }],
    planDescription: [{ required: true, message: '套餐描述不能为空', trigger: 'blur' }],
    price: [{ required: true, message: '套餐价格不能为空', trigger: 'blur' }],
    planPeriod: [{ required: true, message: '套餐周期不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '套餐状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询订阅套餐信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listSubscriptionPlan(queryParams.value);
  subscriptionPlanList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  subscriptionPlanFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: SubscriptionPlanVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加订阅套餐信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: SubscriptionPlanVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getSubscriptionPlan(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改订阅套餐信息';
};

/** 提交按钮 */
const submitForm = () => {
  subscriptionPlanFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateSubscriptionPlan(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addSubscriptionPlan(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: SubscriptionPlanVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除订阅套餐信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delSubscriptionPlan(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/subscriptionPlan/export',
    {
      ...queryParams.value
    },
    `subscriptionPlan_${new Date().getTime()}.xlsx`
  );
};
const planTypeChange = (value: any) => {
  if (value === 'trial') {
    form.value.planPeriod = 3;
  }
  if (value === 'paid_monthly') {
    form.value.planPeriod = 31;
  }
  if (value === 'paid_quarterly') {
    form.value.planPeriod = 90;
  }
  if (value === 'paid_half_yearly') {
    form.value.planPeriod = 180;
  }
  if (value === 'paid_yearly') {
    form.value.planPeriod = 365;
  }
};
onMounted(() => {
  getList();
});
</script>
<style scoped>
.slider-demo-block {
  max-width: 600px;
  display: flex;
  align-items: center;
}
.el-slider__marks-text {
  margin-top: 0px !important;
}
.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}
</style>
