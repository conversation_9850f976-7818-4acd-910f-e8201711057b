import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SpecialVO, SpecialForm, SpecialQuery } from '@/api/scjk/questionbank/special/types';
import { SelectVO } from '@/api/common/types';

/**
 * 查询专项列表
 * @param query
 * @returns {*}
 */

export const listSpecial = (query?: SpecialQuery): AxiosPromise<SpecialVO[]> => {
  return request({
    url: '/scjk/special/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询专项详细
 * @param id
 */
export const getSpecial = (id: string | number): AxiosPromise<SpecialVO> => {
  return request({
    url: '/scjk/special/' + id,
    method: 'get'
  });
};

/**
 * 新增专项
 * @param data
 */
export const addSpecial = (data: SpecialForm) => {
  return request({
    url: '/scjk/special',
    method: 'post',
    data: data
  });
};

/**
 * 修改专项
 * @param data
 */
export const updateSpecial = (data: SpecialForm) => {
  return request({
    url: '/scjk/special',
    method: 'put',
    data: data
  });
};

/**
 * 删除专项
 * @param id
 */
export const delSpecial = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/special/' + id,
    method: 'delete'
  });
};

/**
 * 车型select列表
 * @param query
 * @returns {*}
 */

export const listSelect = (): AxiosPromise<SelectVO[]> => {
  return request({
    url: '/scjk/special/selectList',
    method: 'get'
  });
};
