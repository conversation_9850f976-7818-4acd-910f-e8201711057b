import { getMac } from '@lzwme/get-physical-address';
import { networkInterfaces } from 'os';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

//获得 Mac地址
export async function getMacAddress(): Promise<string> {
  try {
    // 首先尝试使用原有的库（适用于 Windows 7）
    const macAddr = await getMac();
    if (macAddr && macAddr !== '') {
      return macAddr;
    }
  } catch (error: any) {
    console.log('原有方法失败，尝试备用方案:', error.message);
  }

  try {
    // 备用方案1：使用 Node.js 内置模块（跨平台兼容）
    const interfaces = networkInterfaces();

    for (const name of Object.keys(interfaces)) {
      const iface = interfaces[name];
      if (iface) {
        for (const alias of iface) {
          if (alias.family === 'IPv4' && !alias.internal && alias.mac !== '00:00:00:00:00:00') {
            return alias.mac;
          }
        }
      }
    }
  } catch (error: any) {
    console.log('Node.js 方法失败:', error.message);
  }

  // 修改备用方案2：针对 Windows 11 使用 PowerShell
  try {
    if (process.platform === 'win32') {
      // 使用更现代的 PowerShell 命令，适用于 Windows 10/11
      const { stdout } = await execAsync(
        'powershell "Get-NetAdapter | Where-Object {$_.Status -eq \'Up\'} | Select-Object -First 1 -ExpandProperty MacAddress"'
      );
      const macAddr = stdout.trim().replace(/-/g, ':');
      if (macAddr && macAddr !== '') {
        return macAddr;
      }
    }
  } catch (error: any) {
    console.log('PowerShell 方法失败:', error.message);
  }

  console.error('所有获取 MAC 地址的方法都失败了');
  return '';
}
