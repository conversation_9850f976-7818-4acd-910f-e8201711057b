export interface CommissionConfigVO {
  id: string | number;
  /**
   * 名称
   */
  name: string;

  /**
   * 分佣产品
   */
  subscriptionPlanId: string | number;

  /**
   * 一级分佣比例
   */
  firstLevelCommissionRatio: number;

  /**
   * 一级分佣角色
   */
  firstLevelCommissionRole: string;

  /**
   * 二级分佣比例
   */
  secondLevelCommissionRatio: number;

  /**
   * 二级分佣角色
   */
  secondLevelCommissionRole: string;
}

export interface CommissionConfigForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 名称
   */
  name?: string;

  /**
   * 分佣产品
   */
  subscriptionPlanId?: string | number;

  /**
   * 一级分佣比例
   */
  firstLevelCommissionRatio?: number;

  /**
   * 一级分佣角色
   */
  firstLevelCommissionRole?: string;

  /**
   * 二级分佣比例
   */
  secondLevelCommissionRatio?: number;

  /**
   * 二级分佣角色
   */
  secondLevelCommissionRole?: string;
}

export interface CommissionConfigQuery extends PageQuery {
  /**
   * 名称
   */
  name?: string;

  /**
   * 分佣产品
   */
  subscriptionPlanId?: string | number;

  /**
   * 一级分佣比例
   */
  firstLevelCommissionRatio?: number;

  /**
   * 一级分佣角色
   */
  firstLevelCommissionRole?: string;

  /**
   * 二级分佣比例
   */
  secondLevelCommissionRatio?: number;

  /**
   * 二级分佣角色
   */
  secondLevelCommissionRole?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
