@use 'sass:map';
$light : () !default;
$light : ( // light theme
  '--color-white': #ffffff,
  '--color-black': #000000,
  '--color-primary': #0497e6,
  '--color-success': #67c23a,
  '--color-warning': #e6a23c,
  '--color-danger': #f56c6c,
  '--color-error': #f56c6c,
  '--color-info': #909399,

  '--color-bg-1': #fff,
  '--color-bg-2': #f5f5f5,
  '--color-primary-reverse': #fff,
  '--color-text-1': #000,
  '--color-text-2': #333,
  '--color-text-3': #666,
  '--color-menu-bg': #fff,
  '--color-menu-text': #7a7a7a,
  '--color-border-1': #ddd,
  //考试页面颜色配置
  '--exam-bg' : #ffffff,
  '--exam-bg-left_right' : #a9a8a8,
  '--exam-font-color':#2a2a2a,
  '--exam-answer-bg':#f6f5f5,
  '--exam-action-bg':#e7ecf1,
  '--exam-answer-card-head-bg': #2169D3,
  '--exam-answer-card-head-font': #0c0c0c,
  //考试页面信息组件
  '--infm-bg': #f8f9fa,
  '--infm-border':#d9d9da,
  '--infm-title-bg':#f1f4f6,
  '--infm-title-font':#777676
);
$dark : () !default;
// map.merge方法类似于Object.assign，用于合并两个map，如果有相同的key，后面的会覆盖前面的，但不会影响原map
// map.deep-merge方法与map.merge类似，但是如果value是map，会递归合并
$dark : map.deep-merge($light, ( // dark theme
    '--color-bg-1': #121212,
    '--color-bg-2': #000000,
    '--color-primary-reverse': #fff,
    '--color-text-1': #fff,
    '--color-text-2': #cfd3dc,
    '--color-menu-bg': #121212,
    '--color-menu-text': #7a7a7a,
    '--color-border-1': #2e2e2e,

    //考试页面颜色配置
  '--exam-bg' : #212121,
  '--exam-bg-left_right' : #070707,
  '--exam-font-color':#a8a7a7,
  '--exam-answer-bg':#343434,
  '--exam-action-bg':#161717,
  '--exam-answer-card-head-bg': #404141,
  '--exam-answer-card-head-font': #d9d7d7,
    //考试页面信息组件
    '--infm-bg': #2d2d2d,
    '--infm-border':#4f4e4e,
    '--infm-title-bg':#3d3d3d,
    '--infm-title-font':#8a8989

));





html {

  @each $key,
  $value in $light {
    #{$key}: $value;
  }
}

html.dark {
  --el-color-primary: #0497e6 !important;
  --el-color-primary-light-3: #0970a7 !important;
  --el-color-primary-light-5: #0c567d !important;
  --el-color-primary-light-7: #0f3b53 !important;
  --el-color-primary-light-8: #112e3e !important;
  --el-color-primary-light-9: #122129 !important;
  --el-color-primary-dark-2: #36aceb !important;
  @each $key,
  $value in $dark {
    #{$key}: $value;
  }
}
