import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CommissionConfigVO, CommissionConfigForm, CommissionConfigQuery } from '@/api/scjk/commissionConfig/types';

/**
 * 查询分佣配置列表
 * @param query
 * @returns {*}
 */

export const listCommissionConfig = (query?: CommissionConfigQuery): AxiosPromise<CommissionConfigVO[]> => {
  return request({
    url: '/scjk/commissionConfig/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询分佣配置详细
 * @param id
 */
export const getCommissionConfig = (id: string | number): AxiosPromise<CommissionConfigVO> => {
  return request({
    url: '/scjk/commissionConfig/' + id,
    method: 'get'
  });
};

/**
 * 新增分佣配置
 * @param data
 */
export const addCommissionConfig = (data: CommissionConfigForm) => {
  return request({
    url: '/scjk/commissionConfig',
    method: 'post',
    data: data
  });
};

/**
 * 修改分佣配置
 * @param data
 */
export const updateCommissionConfig = (data: CommissionConfigForm) => {
  return request({
    url: '/scjk/commissionConfig',
    method: 'put',
    data: data
  });
};

/**
 * 删除分佣配置
 * @param id
 */
export const delCommissionConfig = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/commissionConfig/' + id,
    method: 'delete'
  });
};
