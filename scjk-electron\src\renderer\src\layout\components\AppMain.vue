<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <transition v-if="!route.meta.noCache" :enter-active-class="animante" mode="out-in">
        <keep-alive v-if="!route.meta.noCache" :include="tagsViewStore.cachedViews">
          <component :is="Component" v-if="!route.meta.link" :key="route.path" />
        </keep-alive>
      </transition>
      <transition v-if="route.meta.noCache" :enter-active-class="animante" mode="out-in">
        <component :is="Component" v-if="!route.meta.link && route.meta.noCache" :key="route.path" />
      </transition>
    </router-view>
    <iframe-toggle />
    <div class="tags-view">
      <el-row :gutter="10">
        <el-col :xs="8" :sm="6" :md="4" :lg="3" :xl="1">
          <el-tag
            v-if="update_info.percent > 0"
            style="
              position: fixed;
              height: 34px;
              left: 0;
              right: 0;
              margin: 0 auto;
              border-radius: 4px;
              bottom: 1px;
              z-index: 9999;
              display: flex;
              align-items: center;
              justify-content: center;
            "
            type="warning"
            >检测到更新，安装包下载中...&nbsp;&nbsp;&nbsp;&nbsp; 网速：{{ update_info.speed }} kb/s &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;进度：{{
              update_info.percent
            }}%&nbsp;&nbsp; (如已下载完成，点击<el-link type="primary" style="font-size: 13px; height: 34px; bottom: 3px" @click="install"
              >安装</el-link
            >)！</el-tag
          >
        </el-col>
      </el-row>
    </div>
  </section>
</template>

<script setup name="AppMain" lang="ts">
import useSettingsStore from '@/store/modules/settings';
import useTagsViewStore from '@/store/modules/tagsView';

import IframeToggle from './IframeToggle/index.vue';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const tagsViewStore = useTagsViewStore();

// 随机动画集合
const animante = ref<string>('');
const animationEnable = ref(useSettingsStore().animationEnable);
const isDownloaded = ref(false);
const update_info = ref({ speed: 0, percent: 0 });
const updateInfo = () => {
  window.elecAPI.onUpdateAvailable((_event, info) => {
    isDownloaded.value = false;
    console.log(info, _event);
  });
  window.elecAPI.onUpdate((_event, info) => {
    update_info.value = info;
  });

  window.elecAPI.onDownloaded(() => {
    isDownloaded.value = true;
    let res = confirm('新版本已下载，是否立即安装？');
    if (res) {
      window.elecAPI.toInstall();
    }
  });
};
const install = () => {
  window.elecAPI.toInstall();
};
onMounted(() => {
  if (window.electron) {
    updateInfo();
  }
});

watch(
  () => useSettingsStore().animationEnable,
  (val: boolean) => {
    animationEnable.value = val;
    if (val) {
      animante.value = proxy?.animate.animateList[Math.round(Math.random() * proxy?.animate.animateList.length)] as string;
    } else {
      animante.value = proxy?.animate.defaultAnimate as string;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 180px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>
<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>
