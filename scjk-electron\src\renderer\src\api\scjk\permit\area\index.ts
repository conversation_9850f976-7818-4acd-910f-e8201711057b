import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { AreaVO, AreaForm, AreaQuery, CascaderVO } from '@/api/scjk/permit/area/types';

/**
 * 查询行政区划列表
 * @param query
 * @returns {*}
 */

export const listArea = (query?: AreaQuery): AxiosPromise<AreaVO[]> => {
  return request({
    url: '/scjk/area/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询行政区划详细
 * @param id
 */
export const getArea = (id: string | number): AxiosPromise<AreaVO> => {
  return request({
    url: '/scjk/area/' + id,
    method: 'get'
  });
};

/**
 * 新增行政区划
 * @param data
 */
export const addArea = (data: AreaForm) => {
  return request({
    url: '/scjk/area',
    method: 'post',
    data: data
  });
};

/**
 * 修改行政区划
 * @param data
 */
export const updateArea = (data: AreaForm) => {
  return request({
    url: '/scjk/area',
    method: 'put',
    data: data
  });
};

/**
 * 删除行政区划
 * @param id
 */
export const delArea = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/area/' + id,
    method: 'delete'
  });
};

/**
 * 行政区划级联列表
 * @returns {*}
 */

export const listAreaCascader = (): AxiosPromise<CascaderVO[]> => {
  return request({
    url: '/scjk/area/cascaderList',
    method: 'get',
    params: {}
  });
};
