import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ExamInfoVO, ExamInfoForm, ExamInfoQuery } from '@/api/scjk/examInfo/types';

/**
 * 查询考试预约信息列表
 * @param query
 * @returns {*}
 */

export const listExamInfo = (query?: ExamInfoQuery): AxiosPromise<ExamInfoVO[]> => {
  return request({
    url: '/scjk/examInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询考试预约信息详细
 * @param id
 */
export const getExamInfo = (id: string | number): AxiosPromise<ExamInfoVO> => {
  return request({
    url: '/scjk/examInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增考试预约信息
 * @param data
 */
export const addExamInfo = (data: ExamInfoForm) => {
  return request({
    url: '/scjk/examInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改考试预约信息
 * @param data
 */
export const updateExamInfo = (data: ExamInfoForm) => {
  return request({
    url: '/scjk/examInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除考试预约信息
 * @param id
 */
export const delExamInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/examInfo/' + id,
    method: 'delete'
  });
};
