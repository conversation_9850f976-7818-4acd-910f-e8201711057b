import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PaperVO, PaperForm, PaperQuery } from '@/api/scjk/paper/paper/types';
import { ca } from 'element-plus/es/locale/index.mjs';

/**
 * 查询试卷列表
 * @param query
 * @returns {*}
 */

export const listPaper = (query?: PaperQuery): AxiosPromise<PaperVO[]> => {
  return request({
    url: '/scjk/paper/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询试卷详细
 * @param id
 */
export const getPaper = (id: string | number): AxiosPromise<PaperVO> => {
  return request({
    url: '/scjk/paper/' + id,
    method: 'get'
  });
};

/**
 * 保存试卷
 * @param data
 */
export const savePaper = (data: PaperForm) => {
  return request({
    url: '/scjk/paper',
    method: 'post',
    data: data
  });
};

/**
 * 删除试卷
 * @param id
 */
export const delPaper = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/paper/' + id,
    method: 'delete'
  });
};

/**
 * 切换状态
 * @param id
 */
export const switchStatus = (id: string | number) => {
  return request({
    url: '/scjk/paper/switchStatus',
    method: 'post',
    data: { id: id }
  });
};

/**
 * 考试
 * @param paper
 */
export const exam = (paper: string | number): AxiosPromise<PaperVO> => {
  return request({
    url: '/scjk/paper/exam?paper=' + paper,
    method: 'get'
  });
};

/**
 * 交卷
 * @param paper
 * @returns
 */
export const submitPaper = (paper: PaperVO) => {
  return request({
    url: '/scjk/paper/submitPaper',
    method: 'post',
    data: paper
  });
};

/**
 * 试卷列表
 * @param carType
 * @param subject
 * @returns {*}
 */

export const all = (carType: string | number, subject: string | number): AxiosPromise<PaperVO[]> => {
  return request({
    url: '/scjk/paper/all',
    method: 'get',
    params: {
      carType: carType,
      subject: subject
    }
  });
};

/**
 * 复制试卷
 * @param id
 */
export const copy = (id: string | number) => {
  return request({
    url: '/scjk/paper/copy?id=' + id,
    method: 'get'
  });
};

/**
 * 更新试卷排序
 * @param id
 */
export const updateSort = (id: string | number, sort: number) => {
  return request({
    url: '/scjk/paper/updateSort?id=' + id + '&sort=' + sort,
    method: 'get'
  });
};

/**
 * 刷新token
 * @return
 */
export const fresh = () => {
  return request({
    url: '/scjk/paper/fresh',
    method: 'get'
  });
};
