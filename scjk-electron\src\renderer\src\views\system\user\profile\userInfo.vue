<template>
  <el-form ref="userRef" :model="userForm" :rules="rules" label-width="80px">
    <el-form-item label="用户昵称" prop="nickName">
      <el-input v-model="userForm.nickName" maxlength="30" />
    </el-form-item>
    <el-form-item label="手机号码" prop="phonenumber">
      <el-input v-model="userForm.phonenumber" maxlength="11" />
    </el-form-item>
    <el-form-item label="邮箱" prop="email">
      <el-input v-model="userForm.email" maxlength="50" />
    </el-form-item>
    <el-form-item label="性别">
      <el-radio-group v-model="userForm.sex">
        <el-radio value="0">男</el-radio>
        <el-radio value="1">女</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="邀请码">
      <el-input v-model="userForm.userCode" disabled />
      <el-button v-if="userForm.userCode === null" :loading="InitBtn" type="primary" link size="small" @click="genCode">生成邀请码</el-button>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submit">保存</el-button>
      <el-button type="danger" @click="close">关闭</el-button>
      <el-button v-if="userForm.subscriptions === null" type="danger" @click="subscribe">开通VIP</el-button>
      <el-button v-else-if="userForm.subscriptions !== null" type="danger" @click="subscribe">续费VIP</el-button>
    </el-form-item>
  </el-form>
  <el-dialog
    ref="orderDialogRef"
    v-model="subscriptionDialog.visible"
    :title="subscriptionDialog.title"
    width="800px"
    append-to-body
    @close="closeDialog"
  >
    <el-card style="max-width: 880px">
      <template #header>
        <div class="card-header">
          <span>套餐信息</span>
        </div>
      </template>
      <el-form ref="orderRef" :model="orderForm" :rules="orderRules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="套餐类型" prop="subscriptionPlanId">
              <el-radio-group v-model="orderForm.subscriptionPlanId" @change="handleSubscriptionPlanChange">
                <el-radio v-for="item in subscriptionOptions" :key="item.id" :value="item.id">{{ item.plan }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-col :span="12">
              <el-form-item label="邀请人" prop="invitationCode">
                <el-input v-model="orderForm.invitationCode" placeholder="请输入邀请码" />
              </el-form-item>
            </el-col>
          </el-col>
          <el-col :span="24">
            <el-descriptions v-if="orderForm.itemName" title="套餐详情" direction="vertical" :column="4" :size="size" :style="blockMargin">
              <el-descriptions-item label="开通套餐">{{ orderForm.itemName }}</el-descriptions-item>
              <el-descriptions-item label="价格">{{ planForm.price }}</el-descriptions-item>
              <el-descriptions-item label="单位">
                <el-tag size="small">{{ planForm.planPeriod }} 天</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="描述"> {{ planForm.planDescription }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitOrderForm">确 定</el-button>
        <el-button @click="closeDialog()">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { addOrder } from '@/api/scjk/order';
import { OrderForm } from '@/api/scjk/order/types';
import { planList } from '@/api/scjk/subscriptionPlan';
import { SubscriptionPlanVO } from '@/api/scjk/subscriptionPlan/types';
import { getInvitonCode, updateUserProfile } from '@/api/system/user';
import router from '@/router';
import { ComponentSize } from 'element-plus';
const props = defineProps({
  user: {
    type: Object as PropType<any>,
    required: true
  }
});
const userForm = computed(() => props.user);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const userRef = ref<ElFormInstance>();
const InitBtn = ref(false);
const orderRef = ref<ElFormInstance>();
const orderDialogRef = ref<ElDialogInstance>();
const orderForm = reactive<OrderForm>({
  subscriptionPlanId: '',
  orderNo: '',
  itemName: '',
  payment: '',
  description: '',
  userId: '',
  unitsNumber: 1,
  orderAmount: 0.0,
  orderStatus: '',
  paySuccessfulTime: '',
  invitationCode: ''
});

const planForm = ref({
  product: '',
  plan: '',
  planType: '',
  planDescription: '',
  planPeriod: 0,
  status: '',
  remark: '',
  price: 0.0
});
const subscriptionDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const size = ref<ComponentSize>('default');

const blockMargin = computed(() => {
  const marginMap = {
    large: '32px',
    default: '28px',
    small: '24px'
  };
  return {
    marginTop: marginMap[size.value] || marginMap.default
  };
});
const subscriptionOptions = ref<SubscriptionPlanVO[]>([]);
const closeDialog = () => {
  subscriptionDialog.visible = false;
};

const submitOrderForm = () => {
  orderRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const res = await addOrder(orderForm);
      if (res.code === 200) {
        proxy?.$modal.msgSuccess('下单成功');
        subscriptionDialog.visible = false;
        proxy?.$router.push({ path: '/subscribe/order', query: { orderNo: res.data }, replace: true });
      } else {
        proxy?.$modal.msgError('下单失败');
      }
    }
  });
};

const handleSubscriptionPlanChange = (value: any) => {
  orderForm.unitsNumber = 1;
  subscriptionOptions.value.map((item: SubscriptionPlanVO) => {
    if (item.id === value) {
      orderForm.itemName = item.product + ' - ' + item.plan;
      orderForm.payment = 'weChatPay';
      orderForm.description = item.planDescription;
      orderForm.orderAmount = item.price;
      planForm.value.planDescription = item.planDescription;
      planForm.value.planPeriod = item.planPeriod;
      planForm.value.price = item.price;
    }
  });
};

const rules = ref<ElFormRules>({
  nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
  phonenumber: [
    {
      required: true,
      message: '手机号码不能为空',
      trigger: 'blur'
    },
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
});

const orderRules = ref<ElFormRules>({
  // invitationCode: [{ required: true, message: '邀请码不能为空', trigger: 'blur' }],
  subscriptionPlanId: [{ required: true, message: '请选择套餐类型', trigger: 'blur' }]
});

/** 提交按钮 */
const submit = () => {
  userRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      await updateUserProfile(props.user);
      proxy?.$modal.msgSuccess('修改成功');
    }
  });
};

const genCode = () => {
  InitBtn.value = true;
  getInvitonCode().then((res) => {
    if (res.code === 200) {
      userForm.value.userCode = res.data;
      InitBtn.value = false;
    }
  });
};

/** 订阅按钮 */
const subscribe = async (value: any) => {
  if (await getSubscriptionPlan()) {
    subscriptionDialog.visible = true;
    subscriptionDialog.title = '选择套餐';
  }
};

/** 订阅按钮 */
const getSubscriptionPlan = async () => {
  const res = await planList();
  subscriptionOptions.value = res;
  if (subscriptionOptions.value.length === 0) {
    ElMessageBox.alert('暂无套餐，请联系管理员');
    return false;
  }
  return true;
};
/** 关闭按钮 */
const close = () => {
  proxy?.$tab.closePage();
};
</script>

<style scoped>
.el-descriptions {
  margin-top: 20px;
}
</style>
