<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="车型" prop="carType">
            <car-type v-model="queryParams.carType" clearable placeholder="请选择车型" @keyup.enter="handleQuery"></car-type>
          </el-form-item>
          <el-form-item label="科目" prop="subject">
            <subject v-model="queryParams.subject" clearable placeholder="请选择科目" @keyup.enter="handleQuery"></subject>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <!-- <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['scjk:paperConfig:add']">新增</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template> -->

      <el-table v-loading="loading" :data="paperConfigList">
        <!-- <el-table-column label="ID" align="center" prop="id" v-if="true" /> -->
        <el-table-column label="车型" align="center" prop="carTypeName" width="150" />
        <el-table-column label="科目" align="center" prop="subjectName" width="150" />
        <el-table-column label="满分" align="center" prop="full" width="100" />
        <el-table-column label="及格" align="center" prop="pass" width="100" />
        <el-table-column label="时间" align="center" prop="time" width="100" />
        <el-table-column label="规则" align="center" prop="rule">
          <template #default="scope">
            <div style="text-align: left">
              <template v-for="rule of JSON.parse(scope.row.rule)"> {{ rule.label }}：{{ rule.quantity }}；&nbsp;</template>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['scjk:paperConfig:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <!-- <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['scjk:paperConfig:remove']"></el-button>
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改试卷配置对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" append-to-body>
      <el-form ref="paperConfigFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="车型" prop="carType">
          <car-type v-model="form.carType" clearable placeholder="请选择车型" @keyup.enter="handleQuery"></car-type>
        </el-form-item>
        <el-form-item label="科目" prop="subject">
          <subject v-model="form.subject" clearable placeholder="请选择科目" @keyup.enter="handleQuery"></subject>
        </el-form-item>
        <el-form-item label="满分" prop="full">
          <el-input-number v-model="form.full" :min="0" :max="100" :step="1" placeholder="请输入满分" />
        </el-form-item>
        <el-form-item label="及格" prop="pass">
          <el-input-number v-model="form.pass" :min="0" :max="100" :step="1" placeholder="请输入及格" />
        </el-form-item>
        <el-form-item label="时间" prop="time">
          <el-input-number v-model="form.time" :min="0" :step="1" placeholder="请输入时间" />
        </el-form-item>
        <el-form-item label="规则" prop="rule">
          <el-row v-for="rule of form.rule" :key="rule.type" style="width: 100%; margin-bottom: 5px" :gutter="20">
            <el-col :span="24">{{ rule.label }}：<el-input-number v-model="rule.quantity" :min="0" :max="100" /></el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PaperConfig" lang="ts">
import { listPaperConfig, getPaperConfig, delPaperConfig, addPaperConfig, updatePaperConfig } from '@/api/scjk/paper/paperConfig';
import { PaperConfigVO, PaperConfigQuery, PaperConfigForm, PaperConfigRule } from '@/api/scjk/paper/paperConfig/types';

import CarType from '@/components/Scjk/CarType/index.vue';
import Subject from '@/components/Scjk/Subject/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const paperConfigList = ref<PaperConfigVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const paperConfigFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const defaultRule: Array<PaperConfigRule> = [
  { 'type': 'S', 'label': '单选', 'quantity': 0 },
  { 'type': 'M', 'label': '多选', 'quantity': 0 },
  { 'type': 'J', 'label': '判断', 'quantity': 0 }
];
const initFormData: PaperConfigForm = {
  id: undefined,
  carType: undefined,
  subject: undefined,
  full: 100,
  pass: 90,
  time: 45,
  rule: defaultRule
};
const data = reactive<PageData<PaperConfigForm, PaperConfigQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    carType: undefined,
    subject: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }],
    carType: [{ required: true, message: '车型不能为空', trigger: 'blur' }],
    subject: [{ required: true, message: '科目不能为空', trigger: 'blur' }],
    full: [{ required: true, message: '满分不能为空', trigger: 'blur' }],
    pass: [{ required: true, message: '及格不能为空', trigger: 'blur' }],
    time: [{ required: true, message: '时间不能为空', trigger: 'blur' }],
    rule: [{ required: true, message: '规则不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询试卷配置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPaperConfig(queryParams.value);
  paperConfigList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  paperConfigFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加试卷配置';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PaperConfigVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getPaperConfig(_id);
  form.value.rule = [];
  Object.assign(form.value, res.data, { rule: res.data.rule === '' || res.data.rule === null ? defaultRule : JSON.parse(res.data.rule) });
  dialog.visible = true;
  dialog.title = '修改试卷配置';
};

/** 提交按钮 */
const submitForm = () => {
  paperConfigFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      const formStringify = JSON.stringify(form.value);
      const saveForm: PaperConfigForm = JSON.parse(formStringify);
      saveForm.rule = JSON.stringify(saveForm.rule);

      if (saveForm.id) {
        await updatePaperConfig(saveForm).finally(() => (buttonLoading.value = false));
      } else {
        await addPaperConfig(saveForm).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: PaperConfigVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除试卷配置编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delPaperConfig(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/paperConfig/export',
    {
      ...queryParams.value
    },
    `试卷配置_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
