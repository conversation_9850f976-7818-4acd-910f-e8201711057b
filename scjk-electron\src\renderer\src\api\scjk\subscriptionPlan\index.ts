import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SubscriptionPlanVO, SubscriptionPlanForm, SubscriptionPlanQuery } from '@/api/scjk/subscriptionPlan/types';

/**
 * 查询订阅套餐信息列表
 * @param query
 * @returns {*}
 */

export const listSubscriptionPlan = (query?: SubscriptionPlanQuery): AxiosPromise<SubscriptionPlanVO[]> => {
  return request({
    url: '/scjk/subscriptionPlan/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询订阅套餐信息
 * @param query
 * @returns {*}
 */

export const planList = (): AxiosPromise<SubscriptionPlanVO[]> => {
  return request({
    url: '/scjk/subscriptionPlan/planList',
    method: 'get'
  });
};

/**
 * 查询订阅套餐信息详细
 * @param id
 */
export const getSubscriptionPlan = (id: string | number): AxiosPromise<SubscriptionPlanVO> => {
  return request({
    url: '/scjk/subscriptionPlan/' + id,
    method: 'get'
  });
};

/**
 * 新增订阅套餐信息
 * @param data
 */
export const addSubscriptionPlan = (data: SubscriptionPlanForm) => {
  return request({
    url: '/scjk/subscriptionPlan',
    method: 'post',
    data: data
  });
};

/**
 * 修改订阅套餐信息
 * @param data
 */
export const updateSubscriptionPlan = (data: SubscriptionPlanForm) => {
  return request({
    url: '/scjk/subscriptionPlan',
    method: 'put',
    data: data
  });
};

/**
 * 删除订阅套餐信息
 * @param id
 */
export const delSubscriptionPlan = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/subscriptionPlan/' + id,
    method: 'delete'
  });
};
