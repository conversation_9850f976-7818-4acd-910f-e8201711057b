<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
            <el-form-item label="用户账号" prop="userName">
              <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="用户昵称" prop="userName">
              <el-input v-model="queryParams.nickName" placeholder="请输入用户昵称" clearable style="width: 240px" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="用户来源" prop="userSource">
              <el-select v-model="queryParams.userSource" placeholder="请选择用户来源" clearable>
                <el-option v-for="dict in sys_user_source" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <!--            <el-form-item label="手机号码" prop="phonenumber">-->
            <!--              <el-input v-model="queryParams.phonenumber" placeholder="请输入手机号码" clearable style="width: 240px" @keyup.enter="handleQuery" />-->
            <!--            </el-form-item>-->
            <el-form-item label="创建时间" style="width: 308px">
              <el-date-picker
                v-model="dateRange"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button v-has-permi="['scjk:student:add']" type="success" plain :disabled="single" icon="Edit" @click="handleUpdate()">
              修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-dropdown class="mt-[1px]">
              <el-button plain type="info">
                更多
                <el-icon class="el-icon--right"><arrow-down /></el-icon
              ></el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item icon="Download" @click="importTemplate">下载模板</el-dropdown-item>
                  <el-dropdown-item icon="Download" @click="handleExport"> 导出数据</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" :columns="columns" :search="true" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange" @expand-change="handleExpandChange">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column type="expand">
          <template #default="props">
            <el-row :gutter="20">
              <el-col :span="4"><div class="grid-content ep-bg-purple" /></el-col>
              <el-col :span="16" v-if="userSubInfoMap[props.row.userId]"
                ><div class="grid-content ep-bg-purple" />
                <el-descriptions v-if="userSubInfoMap[props.row.userId].status === 'active'" title="订阅信息">
                  <el-descriptions-item label="套餐:">
                    <el-tag size="small">{{ userSubInfoMap[props.row.userId].plan }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="到期时间:">{{
                    parseTime(userSubInfoMap[props.row.userId].expiredDate, '{y}-{m}-{d}')
                  }}</el-descriptions-item>
                  <el-descriptions-item label="下次付费时间:">{{
                    parseTime(userSubInfoMap[props.row.userId].nextPayDate, '{y}-{m}-{d}')
                  }}</el-descriptions-item>
                </el-descriptions>
                <el-empty v-else-if="userSubInfoMap[props.row.userId].status !== 'active'" description="无订阅信息" />
              </el-col>
              <el-col :span="4"><div class="grid-content ep-bg-purple" /></el-col>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[0].visible" key="userId" label="用户编号" align="center" prop="userId" />
        <el-table-column v-if="columns[1].visible" key="userName" label="用户名称" align="center" prop="userName" :show-overflow-tooltip="true" />
        <el-table-column v-if="columns[2].visible" key="nickName" label="用户昵称" align="center" prop="nickName" :show-overflow-tooltip="true" />
        <el-table-column v-if="columns[3].visible" key="deptName" label="部门" align="center" prop="dept.deptName" :show-overflow-tooltip="true" />
        <el-table-column key="userSource" label="用户来源" align="center" prop="userSource" width="120">
          <template #default="scope">
            <dict-tag :options="sys_user_source" :value="scope.row.userSource" />
          </template>
        </el-table-column>
        <el-table-column v-if="columns[4].visible" key="phonenumber" label="手机号码" align="center" prop="phonenumber" width="120" />

        <el-table-column v-if="columns[6].visible" label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" width="180" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip v-if="scope.row.userId !== 1" content="修改" placement="top">
              <el-button v-hasPermi="['scjk:student:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>

            <el-tooltip v-if="scope.row.userId !== 1 && scope.row.length !== 0" content="开通VIP" placement="top">
              <el-button v-hasPermi="['scjk:student:edit']" link type="primary" icon="Open" @click="handleActive(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip v-if="scope.row.userId !== 1 && scope.row.planStatus === 'active'" content="取消试用" placement="top">
              <el-button v-hasPermi="['scjk:student:cancelTrial']" link type="danger" icon="CloseBold" @click="cancelTrial(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog ref="formDialogRef" v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body @close="closeDialog">
      <el-form ref="userFormRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入用户昵称" maxlength="30" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择归属部门"
                check-strictly
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户名称" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入用户名称" maxlength="30" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入用户密码" type="password" maxlength="20" show-password />
            </el-form-item>
          </el-col>
          <el-row>
            <el-col :span="12">
              <el-form-item label="用户性别" prop="sex">
                <el-select v-model="form.sex" style="width: 240px" placeholder="请选择">
                  <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-row>
      </el-form>
      <el-form ref="studentFormRef" :model="subform" :rules="studentRules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="报名车型" prop="registeredCarModel">
              <new-car-type v-model="subform.registeredCarModel"></new-car-type>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学习科目" prop="subject">
              <subject v-model="subform.subject" :multiple="true" clearable placeholder="请选择科目"></subject>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属教练" prop="teacherId">
              <el-select v-model="subform.teacherId" style="width: 240px" filterable>
                <el-option v-for="item in teacherList" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-row>
            <el-col :span="24">
              <el-form-item label="所属驾校" prop="drivingSchoolId">
                <el-select v-model="subform.drivingSchoolId" style="width: 240px" filterable>
                  <el-option v-for="item in schoolList" :key="item.id" :label="item.schoolFullname" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!--
              <el-form-item label="所报驾照等级" prop="licenseLevel">
                <el-select v-model="subform.licenseLevel" style="width: 240px" placeholder="请选择驾照等级">
                  <el-option v-for="dict in driving_level" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                </el-select>
              </el-form-item>

           -->
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel()">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <i-ep-upload-filled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="text-center el-upload__tip">
            <div class="el-upload__tip"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据</div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      ref="orderRef"
      v-model="subscriptionDialog.visible"
      :title="subscriptionDialog.title"
      width="500px"
      append-to-body
      @close="closeDialog"
    >
      <el-form ref="orderRef" :model="orderForm" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="套餐" prop="subscriptionPlanId">
              <el-select v-model="orderForm.subscriptionPlanId" style="width: 240px" @change="handleSubscriptionPlanChange">
                <el-option v-for="item in subscriptionOptions" :key="item.id" :label="item.plan" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="unitsNumber">
              <el-input v-model="orderForm.unitsNumber" placeholder="请输入数量" type="number" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitOrderForm">确 定</el-button>
          <el-button @click="cancel()">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User" lang="ts">
import api from '@/api/system/user';
import { UserForm, UserQuery, UserVO } from '@/api/system/user/types';
import { getMemberList } from '@/api/system/role';
import { DeptVO } from '@/api/system/dept/types';
import { RoleVO } from '@/api/system/role/types';
import { PostVO } from '@/api/system/post/types';
import { globalHeaders } from '@/utils/request';
import { addStudent, getStudentInfo, updateStudent } from '@/api/scjk/student';
import { listDrivingSchool } from '@/api/scjk/drivingSchool';
import { DrivingSchoolVO } from '@/api/scjk/drivingSchool/types';
import { listSubscriptionPlan } from '@/api/scjk/subscriptionPlan';
import { SubscriptionPlanVO } from '@/api/scjk/subscriptionPlan/types';
import { addOrder } from '@/api/scjk/order';
import { unSubscribe, getUserSubInfo } from '@/api/scjk/subscriptionRecord';
// import Subject from '@/components/Scjk/NewSubject/index.vue';
import emitter from '@/utils/emitter';
import { getCarType } from '@/api/scjk/questionbank/carType';
import { defineAsyncComponent } from 'vue';
//组件预加载
const AsyncComp = defineAsyncComponent(() => import('@/components/Scjk/NewCarType/index.vue'));
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_user_sex, sys_user_source } = toRefs<any>(proxy?.useDict('driving_level', 'sys_user_sex', 'student_type', 'sys_user_source'));
const userList = ref<UserVO[]>();
const teacherList = ref<UserVO[]>();
const schoolList = ref<DrivingSchoolVO[]>();
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<number | string>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);
const deptName = ref('');
const deptOptions = ref<DeptVO[]>([]);
const postOptions = ref<PostVO[]>([]);
const roleOptions = ref<RoleVO[]>([]);

const subscriptionOptions = ref<SubscriptionPlanVO[]>([]);

/*** 用户导入参数 */
const upload = reactive<ImportOption>({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: globalHeaders(),
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/system/user/importData'
});
// 列显隐信息
const columns = ref<FieldOption[]>([
  { key: 0, label: `用户编号`, visible: false, children: [] },
  { key: 1, label: `用户名称`, visible: true, children: [] },
  { key: 2, label: `用户昵称`, visible: true, children: [] },
  { key: 3, label: `部门`, visible: true, children: [] },
  { key: 4, label: `手机号码`, visible: true, children: [] },
  { key: 5, label: `状态`, visible: true, children: [] },
  { key: 6, label: `创建时间`, visible: true, children: [] }
]);

const deptTreeRef = ref<ElTreeInstance>();
const queryFormRef = ref<ElFormInstance>();
const userFormRef = ref<ElFormInstance>();
const uploadRef = ref<ElUploadInstance>();
const formDialogRef = ref<ElDialogInstance>();
const studentFormRef = ref<ElFormInstance>();
const orderRef = ref<ElDialogInstance>();

const subform = ref({
  registeredCarModel: undefined,
  // learnerType: undefined,
  teacherId: undefined,
  drivingSchoolId: undefined,
  // licenseLevel: undefined,
  subject: undefined,
  id: ''
});

const studentRules = {
  teacherId: [{ required: true, message: '请选择学员老师', trigger: 'blur' }],
  registeredCarModel: [{ required: true, message: '请选择报名车型', trigger: 'blur' }],
  drivingSchoolId: [{ required: true, message: '请选择报名驾校', trigger: 'blur' }],
  subject: [{ required: true, message: '请选择报名科目', trigger: 'blur' }]
};
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const subscriptionDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const orderForm = ref({
  subscriptionPlanId: undefined,
  orderNo: undefined,
  itemName: undefined,
  payment: undefined,
  description: undefined,
  userId: undefined,
  unitsNumber: undefined,
  orderAmount: undefined,
  paySuccessfulTime: undefined,
  orderStatus: undefined,
  price: undefined
});

const initFormData: UserForm = {
  userId: undefined,
  deptId: undefined,
  userName: '',
  nickName: undefined,
  password: '',
  phonenumber: undefined,
  userSource: undefined,
  email: undefined,
  sex: undefined,
  status: '0',
  remark: '',
  postIds: [],
  roleIds: []
};
const data = reactive<PageData<UserForm, UserQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: '',
    nickName: '',
    status: '',
    deptId: ''
  },
  rules: {
    userName: [
      { required: true, message: '用户名称不能为空', trigger: 'blur' },
      { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
    ],
    nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
    password: [
      { required: true, message: '用户密码不能为空', trigger: 'blur' },
      { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }
    ],
    email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
    phonenumber: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }]
  }
});
const userSubInfoMap = ref({}); // 使用一个对象来存储每个行的展开数据

const { queryParams, form, rules } = toRefs<PageData<UserForm, UserQuery>>(data);

/** 查询用户列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.roleKey = 'student';
  const res = await getMemberList(proxy?.addDateRange(queryParams.value, dateRange.value));
  loading.value = false;
  userList.value = res.rows;
  total.value = res.total;
};

const getTeacherList = async () => {
  queryParams.value.roleKey = 'teacher';
  const res = await getMemberList(proxy?.addDateRange(queryParams.value, dateRange.value));
  teacherList.value = res.rows;
};

const getSchoolList = async () => {
  queryParams.value.pageSize = 100;
  const res = await listDrivingSchool(queryParams.value);
  schoolList.value = res.rows;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = ['', ''];
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = undefined;
  deptTreeRef.value?.setCurrentKey(undefined);
  handleQuery();
};

/** 选择条数  */
const handleSelectionChange = (selection: UserVO[]) => {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/user/export',
    {
      ...queryParams.value
    },
    `user_${new Date().getTime()}.xlsx`
  );
};
/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('system/user/importTemplate', {}, `user_template_${new Date().getTime()}.xlsx`);
};

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.handleRemove(file);
  ElMessageBox.alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', {
    dangerouslyUseHTMLString: true
  });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit();
}

/** 重置操作表单 */
const reset = () => {
  form.value = { ...initFormData };
  subform.value = {
    registeredCarModel: undefined,
    // learnerType: undefined,
    teacherId: undefined,
    drivingSchoolId: undefined,
    // licenseLevel: undefined,
    subject: undefined,
    id: ''
  };
  userFormRef.value?.resetFields();
};
/** 取消按钮 */
const cancel = () => {
  dialog.visible = false;
  subscriptionDialog.visible = false;
  reset();
};

/** 修改按钮操作 */
const handleUpdate = async (row?: UserForm) => {
  reset();
  const userId = row?.userId || ids.value[0];
  const { data } = await api.getUser(userId);
  const res = await getStudentInfo(userId);
  if (res.data !== null) {
    subform.value.registeredCarModel = res.data.registeredCarModel;
    // subform.value.learnerType = res.data.learnerType;
    subform.value.teacherId = res.data.teacherId;
    subform.value.drivingSchoolId = res.data.drivingSchoolId;
    // subform.value.licenseLevel = res.data.licenseLevel;
    subform.value.subject = res.data.subject?.split(',');
    subform.value.id = res.data.id;
    if (res.data.registeredCarModel !== null) {
      const carTypeInfo = await getCarType(res.data.registeredCarModel);
      await nextTick(() => {
        emitter.emit('send-toy', carTypeInfo.data.name);
      });
    }
  }
  Object.assign(form.value, data.user);
  postOptions.value = data.posts;
  roleOptions.value = data.roles;
  form.value.postIds = data.postIds;
  form.value.roleIds = data.roleIds;
  form.value.password = '';
  dialog.visible = true;
  dialog.title = '修改用户';
  await getTeacherList();
};

/** 提交按钮 */
const submitForm = () => {
  studentFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      if (form.value.userId) {
        subform.value.userId = form.value.userId;
        subform.value.subject = subform.value.subject?.join();
        if (subform.value.id === '' || subform.value.id === undefined) {
          await addStudent(subform.value).finally(() => dialog.visible.false);
        } else {
          await updateStudent(subform.value).finally(() => dialog.visible.false);
        }
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/**
 * 关闭用户弹窗
 */
const closeDialog = () => {
  dialog.visible = false;
  resetForm();
};

/**
 * 重置表单
 */
const resetForm = () => {
  userFormRef.value?.resetFields();
  userFormRef.value?.clearValidate();

  form.value.id = undefined;
  form.value.status = '1';
};

const handleSubscriptionPlanChange = async (value: any) => {
  orderForm.value.unitsNumber = 1;
  subscriptionOptions.value.map((item: { id: any; product: string; plan: string }) => {
    if (item.id === value) {
      orderForm.value.itemName = item.product + ' - ' + item.plan;
      orderForm.value.payment = 'offline';
      orderForm.value.price = item.price;
    }
  });
};

/** 订阅按钮 */
const handleActive = async (value: any) => {
  if (await getSubscriptionPlan()) {
    subscriptionDialog.visible = true;
    subscriptionDialog.title = '选择套餐';
    orderForm.value.userId = value.userId;
  }
  // orderForm.value.payment = value.payment;
  // orderForm.value.description = value.description;
  // orderForm.value.unitsNumber = value.unitsNumber;
  // orderForm.value.orderAmount = value.orderAmount;
  // orderForm.value.orderStatus = value.orderStatus;
  // orderForm.value.paySuccessfulTime = value.paySuccessfulTime;
  // }
};
const submitOrderForm = async () => {
  const res = await addOrder(orderForm.value);
  if (res.code === 200) {
    proxy?.$modal.msgSuccess('开通成功');
    subscriptionDialog.visible = false;
    await getList();
  } else {
    proxy?.$modal.msgSuccess('开通失败');
  }
};

/** 订阅按钮 */
const getSubscriptionPlan = async () => {
  const res = await listSubscriptionPlan();
  subscriptionOptions.value = res.rows;
  if (subscriptionOptions.value.length === 0) {
    ElMessageBox.alert('暂无套餐，请联系管理员');
    return false;
  }
  return true;
};
const cancelTrial = async (row: any) => {
  if (row.activeWay === '1') {
    await proxy?.$modal.msgError('该用户已付费开通,无法取消试用');
    return;
  }

  await proxy?.$modal.confirm('是否确认取消账号"' + row.userName + '"的订阅吗?');
  const unSubForm = { subscriptionPlanId: undefined, userId: undefined, userName: undefined };
  unSubForm.subscriptionPlanId = row.subscriptionPlanId;
  unSubForm.userId = row.userId;
  unSubForm.userName = row.userName;
  const res = await unSubscribe(unSubForm);
  if (res.code === 200) {
    proxy?.$modal.msgSuccess('取消成功');
    await getList();
  } else {
    proxy?.$modal.msgSuccess('取消失败');
  }
};

const handleExpandChange = async (row: any, expandedRows: any[]) => {
  const isExpanded = expandedRows.length > 0;
  if (isExpanded) {
    // 获取当前行的数据
    const currentRowData = await getUserSubInfo(row.userId).then((res) => {
      return res.data !== null ? res.data : {};
    });
    // 将当前行的数据存储到 userSubInfoMap 中
    userSubInfoMap.value[row.userId] = currentRowData;
  } else {
    // 如果行收起，删除对应的数据
    delete userSubInfoMap.value[row.userId];
  }
  console.log(userSubInfoMap.value);
};

onMounted(async () => {
  await getList(); // 初始化列表数据

  await getSchoolList();
});
</script>

<style lang="scss" scoped></style>
