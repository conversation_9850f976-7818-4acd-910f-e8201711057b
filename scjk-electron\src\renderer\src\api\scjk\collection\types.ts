export interface CollectionVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 用户ID
   */
  userId: string | number;

  /**
   * 试题ID
   */
  questionId: string | number;

  /**
   * 收藏时间
   */
  time: string;
}

export interface CollectionForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 试题ID
   */
  questionId?: string | number;

  /**
   * 收藏时间
   */
  time?: string;
}

export interface CollectionQuery extends PageQuery {
  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 试题ID
   */
  questionId?: string | number;

  /**
   * 收藏时间
   */
  time?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
