import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DrivingSchoolVO, DrivingSchoolForm, DrivingSchoolQuery } from '@/api/scjk/drivingSchool/types';

/**
 * 查询驾校信息列表
 * @param query
 * @returns {*}
 */

export const listDrivingSchool = (query?: DrivingSchoolQuery): AxiosPromise<DrivingSchoolVO[]> => {
  return request({
    url: '/scjk/drivingSchool/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询驾校信息详细
 * @param id
 */
export const getDrivingSchool = (id: string | number): AxiosPromise<DrivingSchoolVO> => {
  return request({
    url: '/scjk/drivingSchool/' + id,
    method: 'get'
  });
};

/**
 * 新增驾校信息
 * @param data
 */
export const addDrivingSchool = (data: DrivingSchoolForm) => {
  return request({
    url: '/scjk/drivingSchool',
    method: 'post',
    data: data
  });
};

/**
 * 修改驾校信息
 * @param data
 */
export const updateDrivingSchool = (data: DrivingSchoolForm) => {
  return request({
    url: '/scjk/drivingSchool',
    method: 'put',
    data: data
  });
};

/**
 * 删除驾校信息
 * @param id
 */
export const delDrivingSchool = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/drivingSchool/' + id,
    method: 'delete'
  });
};
