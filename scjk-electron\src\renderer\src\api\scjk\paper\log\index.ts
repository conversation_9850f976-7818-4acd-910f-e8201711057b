import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PaperLogVO, PaperLogQuery } from '@/api/scjk/paper/log/types';

/**
 * 查询试卷记录列表
 * @param query
 * @returns {*}
 */

export const list = (query?: PaperLogQuery): AxiosPromise<PaperLogVO[]> => {
  return request({
    url: '/scjk/paperLog/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询自己的试卷记录列表
 * @param query
 * @returns {*}
 */

export const listOnlyMine = (query?: PaperLogQuery): AxiosPromise<PaperLogVO[]> => {
  return request({
    url: '/scjk/paperLog/listOnlyMine',
    method: 'get',
    params: query
  });
};
