import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { WithdrawalsRecordsVO, WithdrawalsRecordsForm, WithdrawalsRecordsQuery } from '@/api/scjk/withdrawalsRecords/types';

/**
 * 查询提现记录列表
 * @param query
 * @returns {*}
 */

export const listWithdrawalsRecords = (query?: WithdrawalsRecordsQuery): AxiosPromise<WithdrawalsRecordsVO[]> => {
  return request({
    url: '/scjk/withdrawalsRecords/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询提现记录详细
 * @param id
 */
export const getWithdrawalsRecords = (id: string | number): AxiosPromise<WithdrawalsRecordsVO> => {
  return request({
    url: '/scjk/withdrawalsRecords/' + id,
    method: 'get'
  });
};

/**
 * 新增提现记录
 * @param data
 */
export const addWithdrawalsRecords = (data: WithdrawalsRecordsForm) => {
  return request({
    url: '/scjk/withdrawalsRecords',
    method: 'post',
    data: data
  });
};

/**
 * 修改提现记录
 * @param data
 */
export const updateWithdrawalsRecords = (data: WithdrawalsRecordsForm) => {
  return request({
    url: '/scjk/withdrawalsRecords',
    method: 'put',
    data: data
  });
};

/**
 * 删除提现记录
 * @param id
 */
export const delWithdrawalsRecords = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/withdrawalsRecords/' + id,
    method: 'delete'
  });
};
/**
 * 申请提现状态
 * @param id
 */
export const requestApprealStatus = (id: string | number, status: number, userId: string | number) => {
  return request({
    url: '/scjk/withdrawalsRecords/request/' + id + '/' + status + '/' + userId,
    method: 'put'
  });
};
