<template>
  <div class="p-2">
    <practice
      ref="practiceRef"
      :info="info"
      :list="list"
      skill
      collection
      :highlight-multi="highlightMulti"
      @submit="submitAnswer"
      @back="goBack"
      @review="doWrong"
      @again="tryAgain"
    ></practice>
  </div>
</template>

<script setup name="CollectionPractice" lang="ts">
import useUserStore from '@/store/modules/user';
import { allCollection } from '@/api/scjk/collection';
import { addBatch } from '@/api/scjk/answer';
import Practice from '@/components/Scjk/Practice/index.vue';
import { PracticeInfoVO, PracticeQuestionVO } from '@/api/scjk/practice/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const highlightMulti = ref<boolean>(false);
const practiceRef = ref();

const info = computed(() => {
  return {
    avatar: useUserStore().avatar,
    nickname: useUserStore().nickname,
    sex: useUserStore().sex,
    time: 45
  } as PracticeInfoVO;
});
const list = ref<Array<PracticeQuestionVO>>([]);

/**
 * 加载收藏
 */
const loadQuestions = () => {
  allCollection().then((res) => {
    // 如果没有收藏，回到列表页
    if (res.data.length < 1) {
      proxy?.$modal.msgError('没有收藏!');
      proxy?.$router.push({
        path: '/collection/index'
      });
      return;
    }

    list.value = res.data;
  });
};

/**
 * 提交
 * @param questionVOs
 */
const submitAnswer = (questionVOs: Array<PracticeQuestionVO>) => {
  practiceRef.value.closeFullscreen();
  addBatch(questionVOs);
};

/**
 * 返回
 */
const goBack = () => {
  proxy?.$tab.closePage(proxy?.$route);
  proxy?.$router.push({ path: '/collection/index' });
};

/**
 * 错题
 */
const doWrong = (qVos) => {
  // 如果没有错题，回到收藏列表
  if (qVOs.length < 1) {
    proxy?.$modal.msgError('没有错题!');
    proxy?.$router.push({
      path: '/collection/index'
    });
    return;
  }

  list.value = qVOs;
};

/**
 * 重置
 */
const reset = () => {
  list.value = [];
};

/**
 * 再试一次
 */
const tryAgain = () => {
  reset();
  loadQuestions();
};

onMounted(() => {
  loadQuestions();
});
</script>
<style lang="scss" scoped>
.exam-container {
  padding: 10px;
  min-height: calc(100vh - 300px);
  width: 100%;
  position: relative;
  overflow: hidden;
}
</style>
