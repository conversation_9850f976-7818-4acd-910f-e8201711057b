<template>
  <div class="exam-container">
    <div v-if="notice" class="notice">
      <el-row class="banner">
        <el-col :span="24">
          <el-button type="danger" style="margin: 10px 0px 0px 10px" @click="exist">退出</el-button>
        </el-col>
      </el-row>
      <el-row class="exam-notice" :gutter="8">
        <el-col :span="3"></el-col>
        <el-col :span="9">
          <el-card style="height: 80%">
            <p class="notice-content" style="font-weight: 700">考试须知：</p>
            <p class="notice-content">1.遵守考场纪律，服从监考人员指挥。</p>
            <p class="notice-content">2.进入考场、手机关机。禁止吸烟，禁止吃零食。</p>
            <p class="notice-content">3.未经工作人员允许，考生禁止随意出入考场。</p>
            <p class="notice-content">4.考场内禁止大声喧哗，禁止随意走动。</p>
            <p class="notice-content">5.考试中认真答题，不准交头接耳。</p>
            <p class="notice-content">6.考试中不准冒名顶替，不准弄虚作假。</p>
            <p class="notice-content">7.注意考场卫生，禁止随地吐赛，禁止乱扔统局。</p>
            <p class="notice-content">8.爱护公物及考试设备。</p>
          </el-card>
        </el-col>
        <el-col :span="9">
          <el-card style="height: 80%">
            <p class="notice-content" style="font-weight: 700">驾校理论考试01号考台：</p>
            <p class="red-text">身份证号：{{ userStore.idCard }}</p>
            <p class="red-text">考生姓名：{{ userStore.nickname }}</p>
            <div style="margin-top: 20px; text-align: center">
              <p><el-button type="primary" size="large" @click="beginExam">确定</el-button></p>
              <p class="red-text">点击“确定”按钮开始考试</p>
            </div>
            <p class="blue-text" style="padding-bottom: 30px">
              操作提示:每题考试答案确定后，点击[下一题]。电脑立即判定所选答案，如选择错误，采统将提示正确等案。提示后不允许修改答案。
            </p>
          </el-card></el-col
        >
        <el-col :span="3"></el-col>
      </el-row>
    </div>
    <div v-else>
      <practice
        ref="practiceRef"
        :info="info"
        :list="list"
        :mock="isMock"
        :review="isReview"
        :highlight-multi="highlightMulti"
        @submit="confirmSubmit"
        @back="goMockExam"
        @review="doWrong"
        @again="tryAgain"
        @fresh="fresh"
      ></practice>
    </div>
  </div>
</template>

<script setup name="UserPaperExam" lang="ts">
import useSettingsStore from '@/store/modules/settings';
import usePermissionStore from '@/store/modules/permission';
import useUserStore from '@/store/modules/user';
import { mockExam, submitPaper, fresh } from '@/api/scjk/paper/userPaper/';
import { UserPaperVO } from '@/api/scjk/paper/userPaper/types';
import Practice from '@/components/Scjk/Practice/index.vue';
import { PracticeInfoVO, PracticeQuestionVO } from '@/api/scjk/practice/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();
const userStore = useUserStore();

const notice = ref<boolean>(false);
const practiceRef = ref();
const paper = ref<UserPaperVO>();
const info = ref<PracticeInfoVO>({
  avatar: userStore.avatar,
  nickname: userStore.nickname,
  sex: userStore.sex,
  carTypeName: undefined,
  subjectName: undefined,
  score: 0,
  pass: 0,
  time: 0
});
const highlightMulti = ref<boolean>(false);
const list = ref<Array<PracticeQuestionVO>>([]);
const carType = ref<string>('');
const subject = ref<string>('');
const isMock = ref<boolean>(true);
const isReview = ref<boolean>(false);
const isStudent = computed(() => useUserStore().isStudent);

/**
 * 设置纯净模式
 * @param val
 */
const setPureMode = (val: boolean) => {
  permissionStore.setSidebarRouters(permissionStore.defaultRoutes as any);
  settingsStore.pure = val;
  if (isStudent.value === false) {
    settingsStore.tagsView = !val;
  }
  if (!val) {
    settingsStore.pure = val;
    if (isStudent.value === false) {
      settingsStore.tagsView = !val;
    }
  }
};

/**
 * 开始考试
 */
const beginExam = () => {
  // 关闭提示
  notice.value = false;
  mockExam(carType.value, subject.value)
    .then((res) => {
      console.log('考试----', res);
      if (res.code === 200) {
        isMock.value = true;
        isReview.value = false;

        paper.value = res.data;
        info.value.carTypeName = paper.value.carTypeName;
        info.value.subjectName = paper.value.subjectName;
        info.value.score = paper.value.score;
        info.value.pass = paper.value.pass;
        info.value.time = paper.value.time;
        list.value = paper.value.questions;
        highlightMulti.value = info.value.subjectName.includes('科目三');
      } else {
        goMockExam();
      }
    })
    .catch((e) => {
      // 遇到错误时返回
      goMockExam();
    });
};

/**
 * 确认交卷
 */
const confirmSubmit = (questionsVOs: Array<PracticeQuestionVO>, spendTime: number) => {
  // 如果是错题查看，就不管
  if (isReview.value === true) {
    practiceRef.value.closeFullscreen();
    return;
  }

  paper.value.questions = questionsVOs;
  paper.value.spendTime = spendTime;

  // 保存考试记录
  submitPaper(paper.value).then((res) => {
    if (res.code === 200) {
      // 用后台计算的得分替换
      practiceRef.value.closeFullscreen(res.data);
    }
  });
};

/**
 * 返回模拟考试
 */
const goMockExam = async () => {
  // 关闭纯净模式
  await setPureMode(false);

  proxy?.$tab.closePage(proxy?.$route);
  proxy?.$router.push({ path: isStudent.value === true ? '/index' : '/exam/userPaper' });
};

/**
 * 查看错题
 */
const doWrong = async (qVOs) => {
  // 如果没有错题，回到试卷
  if (qVOs.length < 1) {
    proxy?.$modal.msgError('没有错题!');
    goMockExam();
    return;
  }

  isMock.value = false;
  isReview.value = true;
  notice.value = false;
  list.value = qVOs;
};

/**
 * 再试一次
 */
const tryAgain = () => {
  reset();
  beginExam();
};

/**
 * 重置
 */
const reset = () => {
  paper.value = {};
  info.value = {
    avatar: useUserStore().avatar,
    nickname: useUserStore().nickname,
    sex: useUserStore().sex,
    carTypeName: undefined,
    subjectName: undefined,
    score: 0,
    pass: 0,
    time: 0
  };
  list.value = [];
};

/**
 * 退出
 */
const exist = () => {
  proxy.$router.back();
  setPureMode(false);
};

onMounted(() => {
  setPureMode(true);
  notice.value = true;

  carType.value = proxy?.$route.query.carType;
  subject.value = proxy?.$route.query.subject;
});
</script>
<style lang="scss" src="@/assets/styles/notice.scss" scoped></style>
