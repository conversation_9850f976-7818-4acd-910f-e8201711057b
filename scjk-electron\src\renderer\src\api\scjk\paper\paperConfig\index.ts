import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PaperConfigVO, PaperConfigForm, PaperConfigQuery } from '@/api/scjk/paper/paperConfig/types';
import { SelectVO } from '@/api/common/types';

/**
 * 查询试卷配置列表
 * @param query
 * @returns {*}
 */

export const listPaperConfig = (query?: PaperConfigQuery): AxiosPromise<PaperConfigVO[]> => {
  return request({
    url: '/scjk/paperConfig/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询试卷配置详细
 * @param id
 */
export const getPaperConfig = (id: string | number): AxiosPromise<PaperConfigVO> => {
  return request({
    url: '/scjk/paperConfig/' + id,
    method: 'get'
  });
};

/**
 * 新增试卷配置
 * @param data
 */
export const addPaperConfig = (data: PaperConfigForm) => {
  return request({
    url: '/scjk/paperConfig',
    method: 'post',
    data: data
  });
};

/**
 * 修改试卷配置
 * @param data
 */
export const updatePaperConfig = (data: PaperConfigForm) => {
  return request({
    url: '/scjk/paperConfig',
    method: 'put',
    data: data
  });
};

/**
 * 删除试卷配置
 * @param id
 */
export const delPaperConfig = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/paperConfig/' + id,
    method: 'delete'
  });
};

/**
 * 配置select列表
 * @param query
 * @returns {*}
 */

export const listSelect = (): AxiosPromise<SelectVO[]> => {
  return request({
    url: '/scjk/paperConfig/selectList',
    method: 'get'
  });
};
