<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="课程名称" prop="courseName">
            <el-input v-model="queryParams.courseName" placeholder="请输入课程名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="课程类型" prop="courseType">
            <el-select v-model="queryParams.courseType" placeholder="请选择课程类型" clearable>
              <el-option v-for="dict in course_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker v-model="queryParams.startDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择开始日期" />
          </el-form-item>
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker v-model="queryParams.endDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择结束日期" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:courseSchedule:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:courseSchedule:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:courseSchedule:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:courseSchedule:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="courseScheduleList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="false" label="主键" align="center" prop="id" />
        <el-table-column label="课程名称" align="center" prop="courseName" />
        <el-table-column label="课程类型" align="center" prop="courseType">
          <template #default="scope">
            <dict-tag :options="course_type" :value="scope.row.courseType" />
          </template>
        </el-table-column>
        <el-table-column label="讲师" align="center" prop="teacherName" />
        <el-table-column label="开始日期" align="center" prop="startDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束日期" align="center" prop="endDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['scjk:courseSchedule:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['scjk:courseSchedule:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改课程安排对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="960px" append-to-body>
      <el-form ref="courseScheduleFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课程名称" prop="courseName">
          <el-input v-model="form.courseName" placeholder="请输入课程名称" />
        </el-form-item>
        <el-form-item label="教师" prop="teacherId">
          <el-select v-model="form.teacherId">
            <el-option v-for="item in teacherVoList" :key="item.userId" :label="item.nickName" :value="item.userId" />
          </el-select>
        </el-form-item>
        <el-form-item label="课程类型" prop="courseType">
          <el-select v-model="form.courseType" placeholder="请选择课程类型">
            <el-option v-for="dict in course_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="课程简介">
          <editor v-model="form.courseIntro" :min-height="192" />
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker v-model="form.startDate" clearable type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择开始日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker v-model="form.endDate" clearable type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" :rows="2" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CourseSchedule" lang="ts">
import { listCourseSchedule, getCourseSchedule, delCourseSchedule, addCourseSchedule, updateCourseSchedule } from '@/api/scjk/courseSchedule';
import { CourseScheduleVO, CourseScheduleQuery, CourseScheduleForm } from '@/api/scjk/courseSchedule/types';
import { queryTeacherList } from '@/api/scjk/teacher';
import { t } from 'vxe-table';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { course_type } = toRefs<any>(proxy?.useDict('course_type'));
const courseScheduleList = ref<CourseScheduleVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const courseScheduleFormRef = ref<ElFormInstance>();
let teacherVoList: any[] = [];

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CourseScheduleForm = {
  id: undefined,
  courseName: undefined,
  teacherId: undefined,
  courseIntro: undefined,
  startDate: undefined,
  endDate: undefined,
  remark: undefined
};
const data = reactive<PageData<CourseScheduleForm, CourseScheduleQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    courseName: undefined,
    teacherId: undefined,
    courseIntro: undefined,
    startDate: undefined,
    endDate: undefined,
    params: {},
    courseType: ''
  },
  rules: {
    courseName: [{ required: true, message: '课程名称不能为空', trigger: 'blur' }],
    teacherId: [{ required: true, message: '讲师不能为空', trigger: 'change' }],
    courseIntro: [{ required: true, message: '课程简介不能为空', trigger: 'blur' }],
    startDate: [{ required: true, message: '开始日期不能为空', trigger: 'blur' }],
    endDate: [{ required: true, message: '结束日期不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询课程安排列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCourseSchedule(queryParams.value);
  courseScheduleList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  courseScheduleFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: CourseScheduleVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加课程安排';
  queryTeacherList().then((res) => {
    teacherVoList = res.data;
  });
};

/** 修改按钮操作 */
const handleUpdate = async (row?: CourseScheduleVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getCourseSchedule(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改课程安排';
};

/** 提交按钮 */
const submitForm = () => {
  courseScheduleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      console.log(form);
      if (form.value.id) {
        await updateCourseSchedule(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addCourseSchedule(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CourseScheduleVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除课程安排编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delCourseSchedule(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/courseSchedule/export',
    {
      ...queryParams.value
    },
    `courseSchedule_${new Date().getTime()}.xlsx`
  );
};
onMounted(() => {
  getList();
});
</script>
