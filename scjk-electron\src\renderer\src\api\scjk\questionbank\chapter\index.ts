import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ChapterVO, ChapterForm, ChapterQuery } from '@/api/scjk/questionbank/chapter/types';
import { SelectVO } from '@/api/common/types';

/**
 * 查询章节列表
 * @param query
 * @returns {*}
 */

export const listChapter = (query?: ChapterQuery): AxiosPromise<ChapterVO[]> => {
  return request({
    url: '/scjk/chapter/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询章节详细
 * @param ID
 */
export const getChapter = (ID: string | number): AxiosPromise<ChapterVO> => {
  return request({
    url: '/scjk/chapter/' + ID,
    method: 'get'
  });
};

/**
 * 新增章节
 * @param data
 */
export const addChapter = (data: ChapterForm) => {
  return request({
    url: '/scjk/chapter',
    method: 'post',
    data: data
  });
};

/**
 * 修改章节
 * @param data
 */
export const updateChapter = (data: ChapterForm) => {
  return request({
    url: '/scjk/chapter',
    method: 'put',
    data: data
  });
};

/**
 * 删除章节
 * @param ID
 */
export const delChapter = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/chapter/' + id,
    method: 'delete'
  });
};

/**
 * 车型select列表
 * @param query
 * @returns {*}
 */

export const listSelect = (): AxiosPromise<SelectVO[]> => {
  return request({
    url: '/scjk/chapter/selectList',
    method: 'get'
  });
};
