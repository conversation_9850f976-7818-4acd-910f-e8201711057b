<template>
  <div style="margin-top: 50px">
    <el-row :gutter="70">
      <el-col :span="4">
        <el-card shadow="always">
          <template #header>
            <div class="card-header">
              <span>通知公告</span>
            </div>
          </template>

          <!-- <el-empty description="暂无数据" /> -->
          <systemNotice />
        </el-card>
      </el-col>
      <el-col :span="5">
        <el-card class="paper-category">
          <template #header>
            <div class="card-header">
              <span style="color: #f15a70">精选试卷</span>
            </div>
          </template>
          <el-scrollbar height="500px">
            <el-row v-for="paper of exquisitePaperList" :key="paper.id">
              <el-col :span="24">
                <template v-if="isExquisiteStrengthen(paper.name)">
                  <el-button class="paper exquisite-strengthen" @click="handleExam(paper.id)">{{ paper.name }}</el-button>
                </template>
                <template v-else>
                  <el-button class="paper exquisite" @click="handleExam(paper.id)">{{ paper.name }}</el-button>
                </template>
              </el-col>
            </el-row>
          </el-scrollbar>
        </el-card>
      </el-col>
      <el-col :span="5">
        <el-card class="paper-category">
          <template #header>
            <div class="card-header">
              <span style="color: #fd9f2e">强化试卷</span>
            </div>
          </template>
          <el-scrollbar height="500px">
            <el-row v-for="paper of strengthenPaperList" :key="paper.id">
              <el-col :span="24">
                <el-button class="paper exquisite-strengthen" @click="handleExam(paper.id)">{{ paper.name }}</el-button>
              </el-col>
            </el-row>
          </el-scrollbar>
        </el-card>
      </el-col>
      <el-col :span="5">
        <el-card class="paper-category">
          <template #header>
            <div class="card-header">
              <span style="color: #4b8bf4">基础试卷</span>
            </div>
          </template>
          <el-scrollbar height="500px">
            <el-row v-for="paper of basePaperList" :key="paper.id">
              <el-col :span="24"
                ><el-button class="paper base" @click="handleExam(paper.id)">{{ paper.name }}</el-button></el-col
              >
            </el-row>
          </el-scrollbar>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="paper-category">
          <template #header>
            <div class="card-header">
              <span>操作</span>
            </div>
          </template>
          <el-row>
            <el-col :span="24">
              <div>
                <el-button class="mock-button" @click="handleMockExam">模拟考试</el-button>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div>
                <el-button class="base-button" @click="handlePaperLog">考试记录</el-button>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div>
                <el-button class="base-button" @click="handleAnswerError">错题练习</el-button>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div>
                <el-button class="base-button" @click="handleCollection">我的收藏</el-button>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div>
                <el-button class="font-button">
                  字号：
                  <el-dropdown placement="top">
                    <el-button
                      ><el-icon><CaretTop /></el-icon> {{ fontSize }}号
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="handleFontSize(12)">12</el-dropdown-item>
                        <el-dropdown-item @click="handleFontSize(14)">14</el-dropdown-item>
                        <el-dropdown-item @click="handleFontSize(16)">16</el-dropdown-item>
                        <el-dropdown-item @click="handleFontSize(18)">18</el-dropdown-item>
                        <el-dropdown-item @click="handleFontSize(20)">20</el-dropdown-item>
                        <el-dropdown-item @click="handleFontSize(22)">22</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </el-button>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div>
                <el-button class="font-button">
                  颜色：
                  <el-color-picker v-model="fontColor" :predefine="predefineColors" @change="handleColorChange"></el-color-picker>
                </el-button>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div>
                <el-button class="warning-button" @click="handleLogOut">退出</el-button>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script setup name="Student" lang="ts">
import { editFontSize, editUserSettings } from '@/api/system/user';
import { all } from '@/api/scjk/paper/paper';
import { PaperVO } from '@/api/scjk/paper/paper/types';
import useUserStore from '@/store/modules/user';
import useSubjectStore from '@/store/modules/subject';
import systemNotice from '@/layout/components/SystemNotice/index.vue';
import { CaretTop } from '@element-plus/icons-vue';

const userStore = useUserStore();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 车型
const carType = computed(() => userStore.carType);
// 科目
const subject = computed(() => userStore.subject);

// 考前密卷列表
const secretPaperList = ref<Array<PaperVO>>([]);
// 精选试卷列表
const exquisitePaperList = ref<Array<PaperVO>>([]);
// 强化试卷列表
const strengthenPaperList = ref<Array<PaperVO>>([]);
// 常识试卷列表
const basePaperList = ref<Array<PaperVO>>([]);
// 字体大小
const fontSize = ref<number>(16);
// 字体颜色
const fontColor = ref<string>('#6c6c6c');
const predefineColors = ref([
  '#6c6c6c',
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)'
]);

/**
 * 校验
 */
const check = () => {
  if (carType.value === undefined || carType.value === null || carType.value === '') {
    proxy.$modal.msgError('未找到您报考的车型，请联系管理员！');
    return false;
  }

  if (subject.value === undefined || subject.value === null || subject.value === '') {
    proxy.$modal.msgError('未找到您报考的科目，请联系管理员！');
    return false;
  }

  return true;
};
/**
 * 加载试卷
 */
const loadPaper = () => {
  if (!check()) {
    return;
  }

  all(carType.value, subject.value).then((res) => {
    exquisitePaperList.value = [];
    basePaperList.value = [];
    strengthenPaperList.value = [];
    secretPaperList.value = [];
    // 分组
    for (const paper of res.data) {
      if (paper.category === 'E') {
        if (isExquisiteStrengthen(paper.name)) {
          strengthenPaperList.value.push(paper);
          continue;
        }

        exquisitePaperList.value.push(paper);
        continue;
      }

      if (paper.category === 'B') {
        basePaperList.value.push(paper);
        continue;
      }

      if (paper.category === 'S') {
        strengthenPaperList.value.push(paper);
        continue;
      }

      // 暂不考略其他分组
    }
  });
};

/**
 * 是不是强化试卷
 * @package paper
 */
const isExquisiteStrengthen = (name: string) => {
  return name.includes('强化');
};

/**
 * 考试
 * @param paperId
 */
const handleExam = (paperId: number) => {
  proxy?.$router.push({ path: '/exam/paperExam', query: { paper: paperId } });
};

/**
 * 模拟考试
 */
const handleMockExam = () => {
  if (!check()) {
    return;
  }

  proxy?.$router.push({ path: '/exam/userPaperExam', query: { carType: carType.value, subject: subject.value } });
};

/**
 * 考试记录
 */
const handlePaperLog = () => {
  proxy?.$router.push({ path: '/paperUser' });
};
/**
 * 错题练习
 */
const handleAnswerError = () => {
  proxy?.$router.push({ path: '/answerLog' });
};
/**
 * 我的收藏
 */
const handleCollection = () => {
  proxy?.$router.push({ path: '/collection/index' });
};
/**
 * 试题字号
 */
const handleFontSize = (val: number) => {
  fontSize.value = val;
  userStore.fontSize = val;
  editFontSize(val).then(() => {});
};

/**
 * 处理自定义颜色变化
 */
const handleColorChange = (val: string) => {
  fontColor.value = val;
  userStore.fontColor = val;
  const settings = { fontColor: val };
  editUserSettings(JSON.stringify(settings)).then(() => {});
};

/**
 * 退出
 */
const handleLogOut = async () => {
  await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  });

  await userStore.logout();
  await useSubjectStore().clear();
  if (window.electron) {
    location.href = import.meta.env.VITE_APP_CONTEXT_PATH + 'index.html';
  } else {
    location.href = import.meta.env.VITE_APP_CONTEXT_PATH + 'index';
  }
};

// 监听车型改变
watch(carType, () => {
  // 加载试卷
  // loadPaper();
});

// 监听科目改变
watch(subject, () => {
  // 加载试卷
  loadPaper();
});

onMounted(() => {
  fontSize.value = userStore.fontSize;
  fontColor.value = userStore.fontColor;
  console.log('color', fontColor.value);

  loadPaper();
});
</script>
<style scoped>
.card-header {
  text-align: center;
  line-height: 50px;
  height: 50px;
  background-color: #dddddd57;
}
.card-header span {
  font-size: 20px;
  font-weight: 700;
}
.paper-category {
}

.paper {
  height: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 5px;
  color: white;
  width: 100%;
  margin-top: 3px;
}

.exquisite {
  background-color: #f15a70;
  font-size: 16px;
}

.exquisite-strengthen {
  background-color: #fd9f2e;
  font-size: 16px;
}

.base {
  background-color: #4b8bf4;
  font-size: 16px;
}

.mock-button {
  width: 100%;
  height: 50px;
  margin: 10px 10px 0 0;
  background: url('@/assets/scjk/btn-default.png') no-repeat 0 0;
  background-size: 100% 100%;
  position: relative;
  transition: all ease 0.4s;
  border-radius: 10px;
  color: white;
  font-size: 16px;
}
.base-button {
  width: 100%;
  height: 50px;
  margin: 10px 10px 0 0;
  position: relative;
  transition: all ease 0.4s;
  border-radius: 10px;
  background-color: #f5ae5e;
  color: white;
  font-size: 16px;
}
.font-button {
  width: 100%;
  height: 50px;
  margin: 10px 10px 0 0;
  position: relative;
  transition: all ease 0.4s;
  border-radius: 10px;
  background-color: #f66949;
  color: white;
  font-size: 16px;
}
.warning-button {
  width: 100%;
  height: 50px;
  margin: 10px 10px 0 0;
  position: relative;
  transition: all ease 0.4s;
  border-radius: 10px;
  background-color: #f66949;
  color: white;
  font-size: 16px;
}
</style>
