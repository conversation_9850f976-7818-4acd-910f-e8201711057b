export interface StoreInfoVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 门店名称
   */
  storeName: string;

  /**
   * 联系电话
   */
  contactPhone: string;

  /**
   * 省份
   */
  province: string;

  /**
   * 城市
   */
  city: string;

  /**
   * 详细地址
   */
  address: string;

  /**
   * 经度
   */
  longitude: number;

  /**
   * 纬度
   */
  latitude: number;

  /**
   * 营业时间(如: 09:00-22:00)
   */
  businessHours: string;

  /**
   * 门店描述
   */
  description: string;

  /**
   * 门店图片URL，多个用逗号分隔
   */
  images: string;

  /**
   * 状态(0:禁用 1:启用)
   */
  status: number;

  /**
   * 排序字段
   */
  sort: number;

}

export interface StoreInfoForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 门店名称
   */
  storeName?: string;

  /**
   * 联系电话
   */
  contactPhone?: string;

  /**
   * 省份
   */
  province?: string;

  /**
   * 城市
   */
  city?: string;

  /**
   * 详细地址
   */
  address?: string;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 营业时间(如: 09:00-22:00)
   */
  businessHours?: string;

  /**
   * 门店描述
   */
  description?: string;

  /**
   * 门店图片URL，多个用逗号分隔
   */
  images?: string;

  /**
   * 状态(0:禁用 1:启用)
   */
  status?: number;

  /**
   * 排序字段
   */
  sort?: number;

}

export interface StoreInfoQuery extends PageQuery {

  /**
   * 门店名称
   */
  storeName?: string;

  /**
   * 省份
   */
  province?: string;

  /**
   * 城市
   */
  city?: string;

  /**
   * 详细地址
   */
  address?: string;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 纬度
   */
  latitude?: number;

  /**
   * 营业时间(如: 09:00-22:00)
   */
  businessHours?: string;

  /**
   * 门店描述
   */
  description?: string;

  /**
   * 门店图片URL，多个用逗号分隔
   */
  images?: string;

  /**
   * 状态(0:禁用 1:启用)
   */
  status?: number;

  /**
   * 排序字段
   */
  sort?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



