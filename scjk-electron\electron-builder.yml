appId: cn.yntdrj.scjk
productName: 速诚驾考教学管理系统
directories:
  buildResources: build,
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
asarUnpack:
  - resources/**
win:
  executableName: 速诚驾考教学管理系统
  icon: build/icon.ico
  target:
    - target: nsis
      arch: ia32
nsis:
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
  oneClick: false
  language: 2052
  allowToChangeInstallationDirectory: true
  perMachine: false
appImage:
  artifactName: ${name}-${version}.${ext}
npmRebuild: false
publish:
  provider: generic
  url: https://ks.scjk.site:9000/app-release/win32/
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
