export interface AreaVO {
  /**
   *
   */
  id: string | number;

  /**
   * 层级
   */
  level: string;

  /**
   * 父级行政代码
   */
  parentCode: string;

  /**
   * 行政代码
   */
  areaCode: string;

  /**
   * 邮政编码
   */
  zipCode: number;

  /**
   * 区号
   */
  cityCode: string;

  /**
   * 名称
   */
  name: string;

  /**
   * 简称
   */
  shortName: string;

  /**
   * 组合名
   */
  mergerName: string;

  /**
   * 拼音
   */
  pinyin: string;

  /**
   * 经度
   */
  lng: number;

  /**
   * 纬度
   */
  lat: number;
}

export interface AreaForm extends BaseEntity {
  /**
   *
   */
  id?: string | number;

  /**
   * 层级
   */
  level?: string;

  /**
   * 父级行政代码
   */
  parentCode?: string;

  /**
   * 行政代码
   */
  areaCode?: string;

  /**
   * 邮政编码
   */
  zipCode?: number;

  /**
   * 区号
   */
  cityCode?: string;

  /**
   * 名称
   */
  name?: string;

  /**
   * 简称
   */
  shortName?: string;

  /**
   * 组合名
   */
  mergerName?: string;

  /**
   * 拼音
   */
  pinyin?: string;

  /**
   * 经度
   */
  lng?: number;

  /**
   * 纬度
   */
  lat?: number;
}

export interface AreaQuery extends PageQuery {
  /**
   * 层级
   */
  level?: string;

  /**
   * 父级行政代码
   */
  parentCode?: string;

  /**
   * 行政代码
   */
  areaCode?: string;

  /**
   * 邮政编码
   */
  zipCode?: number;

  /**
   * 区号
   */
  cityCode?: string;

  /**
   * 名称
   */
  name?: string;

  /**
   * 简称
   */
  shortName?: string;

  /**
   * 组合名
   */
  mergerName?: string;

  /**
   * 拼音
   */
  pinyin?: string;

  /**
   * 经度
   */
  lng?: number;

  /**
   * 纬度
   */
  lat?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
