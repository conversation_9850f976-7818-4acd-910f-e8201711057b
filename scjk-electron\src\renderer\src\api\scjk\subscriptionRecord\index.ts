import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SubscriptionRecordVO, SubscriptionRecordForm, SubscriptionRecordQuery } from '@/api/scjk/subscriptionRecord/types';
import user from '@/api/system/user';

/**
 * 查询订阅信息列表
 * @param query
 * @returns {*}
 */

export const listSubscriptionRecord = (query?: SubscriptionRecordQuery): AxiosPromise<SubscriptionRecordVO[]> => {
  return request({
    url: '/scjk/subscriptionRecord/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询订阅信息详细
 * @param id
 */
export const getSubscriptionRecord = (id: string | number): AxiosPromise<SubscriptionRecordVO> => {
  return request({
    url: '/scjk/subscriptionRecord/' + id,
    method: 'get'
  });
};

/**
 * 新增订阅信息
 * @param data
 */
export const addSubscriptionRecord = (data: SubscriptionRecordForm) => {
  return request({
    url: '/scjk/subscriptionRecord',
    method: 'post',
    data: data
  });
};

/**
 * 修改订阅信息
 * @param data
 */
export const updateSubscriptionRecord = (data: SubscriptionRecordForm) => {
  return request({
    url: '/scjk/subscriptionRecord',
    method: 'put',
    data: data
  });
};

/**
 * 删除订阅信息
 * @param id
 */
export const delSubscriptionRecord = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/subscriptionRecord/' + id,
    method: 'delete'
  });
};

/**
 * 取消订阅套餐
 * @param data
 */
export const unSubscribe = (data: SubscriptionRecordForm) => {
  return request({
    url: '/scjk/subscriptionRecord/unsubscribe',
    method: 'put',
    data: data
  });
};

/**
 * 查询用户套餐信息
 * @param data
 */
export const getUserSubInfo = (userId: string | number) => {
  return request({
    url: '/scjk/subscriptionRecord/getSubInfo/' + userId,
    method: 'get'
  });
};
