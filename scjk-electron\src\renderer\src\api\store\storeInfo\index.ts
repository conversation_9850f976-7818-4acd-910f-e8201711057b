import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { StoreInfoVO, StoreInfoForm, StoreInfoQuery } from '@/api/store/storeInfo/types';

/**
 * 查询门店管理列表
 * @param query
 * @returns {*}
 */

export const listStoreInfo = (query?: StoreInfoQuery): AxiosPromise<StoreInfoVO[]> => {
  return request({
    url: '/store/storeInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询门店管理详细
 * @param id
 */
export const getStoreInfo = (id: string | number): AxiosPromise<StoreInfoVO> => {
  return request({
    url: '/store/storeInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增门店管理
 * @param data
 */
export const addStoreInfo = (data: StoreInfoForm) => {
  return request({
    url: '/store/storeInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改门店管理
 * @param data
 */
export const updateStoreInfo = (data: StoreInfoForm) => {
  return request({
    url: '/store/storeInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除门店管理
 * @param id
 */
export const delStoreInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/store/storeInfo/' + id,
    method: 'delete'
  });
};
