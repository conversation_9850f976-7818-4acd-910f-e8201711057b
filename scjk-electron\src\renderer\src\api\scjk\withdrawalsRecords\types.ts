export interface WithdrawalsRecordsVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 申请人
   */
  userId: string | number;

  /**
   * 提现金额
   */
  withdrawalsAmount: number;

  /**
   * 审批人
   */
  approver: number;

  /**
   * 审批状态（1：审批中；2:已通过;3:未通过）
   */
  status: string;

  /**
   * 创建部门
   */
  createDept: number;

  userName: string;
}

export interface WithdrawalsRecordsForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 申请人
   */
  userId?: string | number;

  /**
   * 提现金额
   */
  withdrawalsAmount?: number;

  /**
   * 审批人
   */
  approver?: number;

  /**
   * 审批状态（1：审批中；2:已通过;3:未通过）
   */
  status?: string;

  userName?: string;
}

export interface WithdrawalsRecordsQuery extends PageQuery {
  /**
   * 申请人
   */
  userId?: string | number;

  /**
   * 提现金额
   */
  withdrawalsAmount?: number;

  /**
   * 审批人
   */
  approver?: number;

  /**
   * 审批状态（1：审批中；2:已通过;3:未通过）
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
