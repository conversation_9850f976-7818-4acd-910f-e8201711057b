export interface PaperLogVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 试卷id(多)
   */
  paperId: string;

  /**
   * 名称
   */
  name: string;

  /**
   * 类型
   */
  type: string;

  /**
   * 车型
   */
  carType: string | number;

  /**
   * 车型
   */
  carTypeName: string | number;

  /**
   * 科目
   */
  subject: string | number;

  /**
   * 科目
   */
  subjectName: string | number;

  /**
   * 得分
   */
  score: number;

  /**
   * 时间
   */
  spendTime: number;

  /**
   * 学员
   */
  student: string | number;

  /**
   * 考试时间
   */
  time: Date;

  /**
   * 正确
   */
  correct: number;

  /**
   * 错误
   */
  error: number;
}

export interface PaperLogQuery extends PageQuery {
  /**
   * ID
   */
  id: string | number;

  /**
   * 名称
   */
  name: string;

  /**
   * 类型
   */
  type: string;

  /**
   * 车型
   */
  carType: string | number;

  /**
   * 科目
   */
  subject: string | number;

  /**
   * 得分
   */
  score: number;

  /**
   * 学员
   */
  student: string | number;
  studentName: string;

  /**
   * 考试时间
   */
  time: Date;

  /**
   * 日期范围参数
   */
  params?: any;
}
