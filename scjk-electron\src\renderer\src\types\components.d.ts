/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ApprovalRecord: typeof import('./../components/Process/approvalRecord.vue')['default']
    AreaCascader: typeof import('./../components/Scjk/AreaCascader/index.vue')['default']
    AudioUpload: typeof import('./../components/Scjk/AudioUpload/index.vue')['default']
    BpmnDesign: typeof import('./../components/BpmnDesign/index.vue')['default']
    BpmnView: typeof import('./../components/BpmnView/index.vue')['default']
    Breadcrumb: typeof import('./../components/Breadcrumb/index.vue')['default']
    BuildCode: typeof import('./../components/BuildCode/index.vue')['default']
    CarType: typeof import('./../components/Scjk/CarType/index.vue')['default']
    Chapter: typeof import('./../components/Scjk/Chapter/index.vue')['default']
    DictTag: typeof import('./../components/DictTag/index.vue')['default']
    Editor: typeof import('./../components/Editor/index.vue')['default']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSelectV2: typeof import('element-plus/es')['ElSelectV2']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FileUpload: typeof import('./../components/FileUpload/index.vue')['default']
    Hamburger: typeof import('./../components/Hamburger/index.vue')['default']
    HeaderSearch: typeof import('./../components/HeaderSearch/index.vue')['default']
    IconSelect: typeof import('./../components/IconSelect/index.vue')['default']
    IEpCaretBottom: typeof import('~icons/ep/caret-bottom')['default']
    IEpCaretTop: typeof import('~icons/ep/caret-top')['default']
    IEpUploadFilled: typeof import('~icons/ep/upload-filled')['default']
    IFrame: typeof import('./../components/iFrame/index.vue')['default']
    ImagePreview: typeof import('./../components/ImagePreview/index.vue')['default']
    ImageUpload: typeof import('./../components/ImageUpload/index.vue')['default']
    InfoForm: typeof import('./../components/Scjk/InfoForm/index.vue')['default']
    LangSelect: typeof import('./../components/LangSelect/index.vue')['default']
    LottieWeb: typeof import('./../components/LottieWeb/LottieWeb.vue')['default']
    MultiInstanceUser: typeof import('./../components/Process/multiInstanceUser.vue')['default']
    NewCarType: typeof import('./../components/Scjk/NewCarType/index.vue')['default']
    NewSubject: typeof import('./../components/Scjk/NewSubject/index.vue')['default']
    Pagination: typeof import('./../components/Pagination/index.vue')['default']
    PaperConfig: typeof import('./../components/Scjk/PaperConfig/index.vue')['default']
    ParentView: typeof import('./../components/ParentView/index.vue')['default']
    Practice: typeof import('./../components/Scjk/Practice/index.vue')['default']
    Pure: typeof import('./../components/Pure/index.vue')['default']
    Question: typeof import('./../components/Scjk/Question/index.vue')['default']
    Render: typeof import('./../components/BuildCode/render.vue')['default']
    RightToolbar: typeof import('./../components/RightToolbar/index.vue')['default']
    RoleSelect: typeof import('./../components/RoleSelect/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Screenfull: typeof import('./../components/Screenfull/index.vue')['default']
    SizeSelect: typeof import('./../components/SizeSelect/index.vue')['default']
    Special: typeof import('./../components/Scjk/Special/index.vue')['default']
    Student: typeof import('./../components/Scjk/Student/index.vue')['default']
    Subject: typeof import('./../components/Scjk/Subject/index.vue')['default']
    SubmitVerify: typeof import('./../components/Process/submitVerify.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    TopNav: typeof import('./../components/TopNav/index.vue')['default']
    TreeSelect: typeof import('./../components/TreeSelect/index.vue')['default']
    UserSelect: typeof import('./../components/UserSelect/index.vue')['default']
    VideoUpload: typeof import('./../components/Scjk/VideoUpload/index.vue')['default']
    WxPayQrCode: typeof import('./../components/wxPayQrCode/index.vue')['default']
    Xgplayer: typeof import('./../components/Scjk/Xgplayer/index.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
