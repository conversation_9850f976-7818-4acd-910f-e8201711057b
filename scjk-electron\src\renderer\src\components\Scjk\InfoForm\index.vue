

<template>
<div class="im-main">
  <div class="im-title">{{ title }}</div>
  <div class="im-content"><slot /></div>
</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps({
  title: String
});
</script>

<style scoped lang="scss">
.im-main {
  background-color: var(--infm-bg);
  border: 1px solid var(--infm-border);
  display: flex;
  flex-direction: column;

  .im-title {
    height: 3vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--infm-title-bg);
    border-bottom: 1px solid var(--infm-border);
    color: var(--infm-title-font);
    font-size: smaller;

  }
  .im-content {
    flex:1;
  }
}

</style>
