.content-body {
  width: 1200px;
  margin: 0 auto;
  background: #fff;
  box-sizing: border-box;
  padding-top: 54px;
  overflow: hidden;
}
.pos {
  position: relative;
}
.pos .tit {
  position: absolute;
  top: -17px;
  left: 32px;
  background: #fff;
  padding: 5px;
  font-size: 18px;
  color: #333;
  line-height: 25px;
}
.left {
  margin-right: 20px;
}
.left .kt {
  height: 86px;
  text-align: center;
  line-height: 86px;
  color: #4a4a4a;
  font-size: 16px;
  margin-bottom: 20px;
}
.left .kt,
.left .user-info {
  width: 162px;
  border: 1px solid #ddd;
  box-sizing: border-box;
}
.left .user-info {
  height: 290px;
  padding: 20px 16px;
  margin-bottom: 22px;
}
.left .user-info img {
  display: block;
  width: 100px;
  height: 100px;
  margin: 0 auto 15px;
}
.left .user-info p {
  margin-bottom: 10px;
  font-size: 0;
  color: #4a4a4a;
}
.left .user-info p label {
  font-size: 16px;
}
.left .user-info p label.mr {
  margin-right: 32px;
}
.left .user-info p span {
  font-size: 16px;
}
.left .waste-time {
  width: 162px;
  height: 70px;
  border: 1px solid #ddd;
  box-sizing: border-box;
  text-align: center;
  line-height: 70px;
  color: #24c27d;
  font-size: 18px;
}
.center {
  margin-right: 20px;
}
.center .kstm {
  width: 556px;
  height: 396px;
  border: 1px solid #ddd;
  box-sizing: border-box;
  padding: 25px 20px;
  margin-bottom: 22px;
}
.center .kstm .name {
  line-height: 25px;
}
.center .kstm .name,
.center .kstm .option p {
  color: #4a4a4a;
  font-size: 18px;
  margin-bottom: 15px;
}
.center .kstm .option p {
  line-height: 21px;
}
.center .kstm .tm-answer {
  width: 100%;
  overflow: hidden;
  position: absolute;
  bottom: 20px;
  left: 0;
  padding: 0 20px;
  box-sizing: border-box;
}
.center .kstm .tm-answer .sec-aswer {
  font-size: 18px;
  line-height: 25px;
  color: #4a4a4a;
}
.center .kstm .tm-answer .aswer {
  overflow: hidden;
}
.center .kstm .tm-answer .aswer span {
  font-size: 18px;
  line-height: 25px;
  color: #4a4a4a;
}
.center .kstm .tm-answer .aswer li {
  float: left;
  width: 30px;
  height: 24px;
  line-height: 24px;
  font-size: 16px;
  border: 1px solid grey;
  margin: 0 5px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
}
.center .kstm .tm-answer .aswer li.active {
  background: #24c27d;
  border-color: #24c27d;
  color: #fff;
}
.center .tsxx {
  width: 318px;
  height: 70px;
  padding: 16px 10px 10px;
  border: 1px solid #ddd;
  box-sizing: border-box;
  color: #4a4a4a;
  font-size: 16px;
  line-height: 22px;
}

.right {
  width: 442px;
}
.right .Qn-wrap {
  overflow: hidden;
}
.right .Qn-wrap li {
  width: 40px;
  height: 40px;
  color: #4a4a4a;
  font-size: 13px;
  border: 1px solid #000;
  text-align: center;
  float: left;
  box-sizing: border-box;
  cursor: pointer;
}
.right .Qn-wrap li.active {
  background: #e6e6e6;
}
.right .Qn-wrap li p.rightA {
  // color: #24c27d;
  line-height: 40px;
}
.right .Qn-wrap li p.wrong {
  color: #fb6e52;
  line-height: 40px;
}
.right .Qn-wrap li p.judge {
  font-size: 28px;
}
.right .Qn-wrap li p.rightJ {
  color: #24c27d;
}
.right .Qn-wrap li p.wrongJ {
  color: #fb6e52;
}
.right .Qn-wrap li.noSel {
  line-height: 40px;
}
.right .Qn-wrap li.selected {
  line-height: 20px;
}
.q-detail {
  width: 100%;
  height: 230px;
  border: 1px solid #ddd;
  box-sizing: border-box;
  margin-top: 20px;
  padding: 25px 0;
}
.q-detail img {
  height: 180px;
  display: block;
  margin: 0 auto;
}
body,
dd,
dl,
form,
h1,
h2,
h3,
h4,
h5,
h6,
input,
ol,
p,
select,
td,
textarea,
th,
ul {
  margin: 0;
  padding: 0;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    \\5FAE\8F6F\96C5\9ED1,
    Arial,
    sans-serif !important;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400;
}
li,
ol,
ul {
  list-style: none;
}
img {
  border: none;
  vertical-align: top;
}
input,
select {
  vertical-align: middle;
}
select {
  font-size: 14px;
}
textarea {
  resize: none;
  overflow: auto;
}
input[type='button'],
input[type='password'],
input[type='submit'],
input[type='text'],
textarea {
  outline-style: none;
  -webkit-appearance: none;
}
a {
  text-decoration: none;
  outline: none;
  cursor: pointer;
}
a,
button,
dd,
div,
dl,
input,
table,
td {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
button {
  outline: none;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
}
.jj-wrap {
  display: none;
  width: 100%;
  min-height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1;
}
.jj-con,
.jj-wrap {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
}
.jj-con {
  width: 560px;
  height: 400px;
  margin: auto;
  right: 0;
  background: #fff;
  z-index: 2;
  border-radius: 5px;
}
.jj-header {
  width: 100%;
  height: 55px;
  background: #24c27d;
  line-height: 55px;
  text-align: center;
  font-size: 26px;
  color: #fff;
  border-radius: 5px 5px 0 0;
}
.jj-content {
  width: 100%;
  padding: 28px 43px;
  box-sizing: border-box;
}
.jj-content p {
  font-size: 22px;
  margin-bottom: 25px;
  line-height: 28px;
  color: #333;
}
.jj-btn-wrap {
  width: 100%;
  padding: 0 43px;
  box-sizing: border-box;
  position: absolute;
  bottom: 28px;
  left: 0;
}
.jj-btn-wrap .jj-btn {
  display: block;
  width: 145px;
  height: 46px;
  text-align: center;
  line-height: 46px;
  border-radius: 100px;
  font-size: 18px;
}
.jj-btn-wrap .jxdt {
  background: #e6e6e6;
  color: #4a4a4a;
  float: left;
}
.jj-btn-wrap .xzjj {
  background: #24c27d;
  color: #fff;
  float: right;
}
a:hover {
  text-decoration: none;
}
.img-wrap {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  display: none;
}
.img-wrap img {
  width: 30%;
  position: absolute;
  margin: auto;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 55px;
  border-radius: 8px;
}

.confirm {
  .warn {
    font-size: 24px;
    font-weight: 700;
    color: red;
  }

  .text {
    font-size: 18px;
  }
}

.result-data {
  margin-top: 50px;
  margin-bottom: 50px;
  display: flex;
  justify-content: space-around;

  .right {
    font-size: 20px;
    font-weight: 600;
    color: #00c356;
  }

  .wrong {
    font-size: 20px;
    font-weight: 600;
    color: #ff2b3c;
  }

  .un {
    font-size: 20px;
    font-weight: 600;
    color: #0a2433;
  }

  .time {
    font-size: 20px;
    font-weight: 600;
    color: #0a2433;
  }
}

.result-score {
  text-align: center;

  .score {
    font-weight: 700;
    font-size: 50px;
  }

  .fen {
    font-size: 22px;
    font-weight: 700;
  }

  .pass {
    font-weight: 700;
    font-size: 22px;
  }
}

.answer-tip {
  text-decoration: underline;
  cursor: pointer;
}

.answer-text {
  margin-bottom: 20px;
}

.rich-text {
  display: inline;
}

.rich-text > p {
  display: inline;
}

// .qeditor {
//   display: inline-flex;
//   width: 94%;
// }

// .ql-container.ql-snow {
//   border: none !important;

//   .ql-editor.ql-blank > p {
//     font-size: 18px !important;
//   }
// }

.bottom {
  width: 100%;
  height: 80px;
  border: 1px solid #ddd;
  box-sizing: border-box;
  margin-top: 20px;
}

.bottom .btn-wrap {
  float: right;
  padding-right: 402px;
  padding-top: 20px;
}

.tsxx .tit {
  background: #fff;
  padding: 5px;
  padding-left: 20px;
  font-size: 18px;
  color: #ff0000;
  line-height: 25px;
}

.tsxx .tsxxt {
  background: #fff;
  padding: 5px;
  padding-left: 20px;
  line-height: 25px;
}

.your-answer {
  color: #8ca5f4;
  font-size: 18px;
  padding-left: 20px;
}

.your-answer .your-answer-text {
  text-align: right;
  padding-right: 20px;
}

.center .tm-answer .sec-aswer {
  font-size: 18px;
  line-height: 25px;
  color: #4a4a4a;
}
.center .tm-answer .aswer {
  overflow: hidden;
}
.center .tm-answer .aswer span {
  font-size: 18px;
  line-height: 25px;
  color: #4a4a4a;
}
.center .tm-answer .aswer li {
  float: left;
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  border: 1px solid grey;
  margin: 0 5px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
}
.center .tm-answer .aswer li.active {
  background: #24c27d;
  border-color: #24c27d;
  color: #fff;
}

.right .Qn-wrap li.colrow {
  line-height: 40px;
  background: #2169d3;
  color: black;
}

.skill {
  padding-top: 20px;
  padding-left: 130px;
}
