<template>
  <el-select
    :model-value="value"
    :placeholder="placeholder"
    :multiple="multiple"
    :clearable="clearable"
    :disabled="disabled"
    style="width: 240px"
    @change="handleChange"
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>

<script name="NewSubject" lang="ts" setup>
import { listSelect } from '@/api/scjk/questionbank/subject';
import { SelectVO } from '@/api/common/types';
import emitter from '@/utils/emitter';
import { onUnmounted } from 'vue';

const props = defineProps({
  value: {
    type: String || Array,
    default: undefined
  },
  // 	是否多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否可以清空选项
  clearable: {
    type: Boolean,
    default: true
  },
  // 占位符，默认为“Select”
  placeholder: {
    type: String,
    default: '请选择'
  }
});

const emit = defineEmits(['update:modelValue']);
const handleChange = (value: string) => {
  emit('update:modelValue', value);
};

const options = ref<Array<SelectVO>>([]);
emitter.on('send-toy', (value: string) => {
  console.log('监听send-toy事件');
  listSelect().then((res) => {
    if (res.code == 200 && res.data.length > 0) {
      options.value = [...res.data];
      if (value !== '小车') {
        options.value.pop();
      } else {
        options.value = [...res.data];
      }
    }
  });
  console.log('send-toy事件被触发', value);
});
onMounted(() => {});

onUnmounted(() => {
  emitter.off('send-toy');
});
</script>
