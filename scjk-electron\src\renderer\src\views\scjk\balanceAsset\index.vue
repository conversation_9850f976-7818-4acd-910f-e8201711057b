<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
          <el-form-item label="用户编码" prop="userCode">
            <el-input v-model="queryParams.userCode" placeholder="请输入用户编码" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['scjk:balanceAsset:add']">新增</el-button>-->
          <!--          </el-col>-->
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['scjk:balanceAsset:edit']"-->
          <!--              >修改</el-button-->
          <!--            >-->
          <!--          </el-col>-->
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['scjk:balanceAsset:remove']"-->
          <!--              >删除</el-button-->
          <!--            >-->
          <!--          </el-col>-->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['scjk:balanceAsset:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="balanceAssetList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" prop="id" v-if="false" />
        <el-table-column label="用户昵称" align="center" prop="nickName" />
        <el-table-column label="资产余额" align="center" prop="balance" />
        <el-table-column label="用户编码" align="center" prop="userCode" />
        <el-table-column label="变动时间" align="center" prop="updateTime" width="180" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['scjk:balanceAsset:edit']"></el-button>
            </el-tooltip>
            <!--            <el-tooltip content="删除" placement="top">-->
            <!--              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['scjk:balanceAsset:remove']"></el-button>-->
            <!--            </el-tooltip>-->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改用户资产对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="balanceAssetFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户id" prop="userId">
          <el-input v-model="form.userId" disabled placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="用户昵称" prop="nickName">
          <el-input v-model="form.nickName" disabled placeholder="请输入用户昵称" />
        </el-form-item>
        <el-form-item label="资产余额" prop="balance">
          <el-input type="number" v-model="form.balance" placeholder="请输入资产余额" />
        </el-form-item>
        <el-form-item label="用户编码" prop="userCode">
          <el-input v-model="form.userCode" disabled placeholder="请输入用户编码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BalanceAsset" lang="ts">
import { listBalanceAsset, getBalanceAsset, delBalanceAsset, addBalanceAsset, updateBalanceAsset } from '@/api/scjk/balanceAsset';
import { BalanceAssetVO, BalanceAssetQuery, BalanceAssetForm } from '@/api/scjk/balanceAsset/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const balanceAssetList = ref<BalanceAssetVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const balanceAssetFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: BalanceAssetForm = {
  id: undefined,
  userId: undefined,
  balance: undefined,
  userCode: undefined,
  nickName: undefined
};
const data = reactive<PageData<BalanceAssetForm, BalanceAssetQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    balance: undefined,
    userCode: undefined,
    params: {}
  },
  rules: {
    // userId: [{ required: true, message: '用户id不能为空', trigger: 'blur' }],
    balance: [{ required: true, message: '资产余额不能为空', trigger: 'blur' }]
    // userCode: [{ required: true, message: '用户编码不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户资产列表 */
const getList = async () => {
  loading.value = true;
  const res = await listBalanceAsset(queryParams.value);
  balanceAssetList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  balanceAssetFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: BalanceAssetVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加用户资产';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: BalanceAssetVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getBalanceAsset(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改用户资产';
};

/** 提交按钮 */
const submitForm = () => {
  balanceAssetFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateBalanceAsset(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addBalanceAsset(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: BalanceAssetVO) => {
  return proxy?.$modal.msgError('不允许删除用户资产数据');
  // const _ids = row?.id || ids.value;
  // await proxy?.$modal.confirm('是否确认删除用户资产编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  // await delBalanceAsset(_ids);
  // proxy?.$modal.msgSuccess('删除成功');
  // await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/balanceAsset/export',
    {
      ...queryParams.value
    },
    `balanceAsset_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
