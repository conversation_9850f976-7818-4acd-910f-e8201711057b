import { type UserConfig, defineConfig, externalizeDepsPlugin, loadEnv } from 'electron-vite';
import createPlugins from './src/renderer/vite/plugins';
import AutoImport from 'unplugin-auto-import/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';
import IconsResolver from 'unplugin-icons/resolver';
import autoprefixer from 'autoprefixer'; // css自动添加兼容性前缀
import path, { resolve } from 'path';

export default defineConfig(({ mode, command }): UserConfig => {
  const env = loadEnv(mode);
  return <UserConfig>{
    main: {
      plugins: [externalizeDepsPlugin()]
    },
    preload: {
      plugins: [externalizeDepsPlugin()]
    },
    renderer: {
      // 部署生产环境和开发环境下的URL。
      // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
      base: env.VITE_APP_CONTEXT_PATH,
      resolve: {
        alias: {
          '@renderer': resolve('src/renderer/src'),
          '~': path.resolve(__dirname, './src/renderer/'),
          '@': path.resolve(__dirname, './src/renderer/src/')
        },
        extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
      },
      build: {
        reportCompressedSize: false
      },
      plugins: [
        createPlugins(env, command === 'build'),
        AutoImport({
          resolvers: [
            ElementPlusResolver({
              importStyle: 'sass'
            })
          ],
          imports: ['vue', 'vue-router']
        }),
        Components({
          resolvers: [
            ElementPlusResolver({
              importStyle: 'sass'
            }),
            // 自动注册图标组件
            IconsResolver({
              enabledCollections: ['ep']
            })
          ]
        })
      ],
      server: {
        host: '0.0.0.0',
        port: Number(env.VITE_APP_PORT),
        open: false,
        proxy: {
          [env.VITE_APP_BASE_API]: {
            target: 'https://ks.scjk.site:4025',
            // target: 'http://localhost:4025',
            changeOrigin: true,
            ws: true,
            rewrite: (path) => path.replace(new RegExp('^' + env.VITE_APP_BASE_API), '')
          }
        }
      },
      css: {
        preprocessorOptions: {
          scss: {
            additionalData: (content, filename) => {
              // Temporarily return content without any injection for debugging
              // console.log(`[SCSS AdditionalData] Processing: ${filename}`);
              return content;
            },
            // javascriptEnabled: true
            api: 'modern-compiler'
          }
        },
        postcss: {
          plugins: [
            // 浏览器兼容性
            autoprefixer(),
            {
              postcssPlugin: 'internal:charset-removal',
              AtRule: {
                charset: (atRule) => {
                  if (atRule.name === 'charset') {
                    atRule.remove();
                  }
                }
              }
            }
          ]
        }
      },
      // 预编译
      optimizeDeps: {
        include: [
          'vue',
          'vue-router',
          'pinia',
          'axios',
          '@vueuse/core',
          'path-to-regexp',
          'echarts',
          'vue-i18n',
          '@vueup/vue-quill',
          'image-conversion',
          'mathjs',
          'fingerprintjs2',
          '@taoqf/quill-image-resize-module',
          'quill-image-drop-module',
          'mitt',
          'xgplayer',
          'xgplayer-flv',
          'xgplayer-hls',
          'xgplayer-mp4',
          'xgplayer-music',
          // Element Plus 样式优化（使用通配符模式）
          'qrcode',
          'element-plus/es/components/**/css'
        ]
      }
    }
  };
});
