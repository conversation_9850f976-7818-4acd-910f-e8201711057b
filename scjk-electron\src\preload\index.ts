import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { electronAPI } from '@electron-toolkit/preload';

// Custom APIs for renderer
const api = {};

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI);
    contextBridge.exposeInMainWorld('api', api);
    contextBridge.exposeInMainWorld('elecAPI', {
      toInstall: () => ipcRenderer.invoke('install'),
      onUpdate: (callback) => ipcRenderer.on('update', callback),
      onDownloaded: (callback) => ipcRenderer.on('downloaded', callback),
      onUpdateAvailable: (callback) => ipcRenderer.on('update-available', callback)
    });
  } catch (error) {
    console.error(error);
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI;
  // @ts-ignore (define in dts)
  window.api = api;
}
