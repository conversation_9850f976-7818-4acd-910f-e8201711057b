export interface SubscriptionPlanVO {
  id: string | number;
  /**
   * 产品名称
   */
  product: string;

  /**
   * 套餐名称
   */
  plan: string;

  /**
   * 套餐类型
   */
  planType: string;

  /**
   * 套餐描述
   */
  planDescription: string;

  /**
   * 套餐价格
   */
  price: number;

  /**
   * 套餐周期
   */
  planPeriod: number;

  /**
   * 套餐状态
   */
  status: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 套餐内容
   */
  planContent: string;
}

export interface SubscriptionPlanForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 产品名称
   */
  product?: string;

  /**
   * 套餐名称
   */
  plan?: string;

  /**
   * 套餐类型
   */
  planType?: string;

  /**
   * 套餐描述
   */
  planDescription?: string;

  /**
   * 套餐价格
   */
  price?: number;

  /**
   * 套餐周期
   */
  planPeriod?: number;

  /**
   * 套餐状态
   */
  status?: string;

  /**
   * 备注
   */
  remark?: string;
  /**
   * 套餐内容
   */
  planContent?: string;
}

export interface SubscriptionPlanQuery extends PageQuery {
  /**
   * 产品名称
   */
  product?: string;

  /**
   * 套餐名称
   */
  plan?: string;

  /**
   * 套餐类型
   */
  planType?: string;

  /**
   * 套餐描述
   */
  planDescription?: string;

  /**
   * 套餐价格
   */
  price?: number;

  /**
   * 套餐周期
   */
  planPeriod?: number;

  /**
   * 套餐状态
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
