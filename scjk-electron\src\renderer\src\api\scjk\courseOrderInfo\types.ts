export interface CourseOrderInfoVO {
  /**
   * 主键
   */
  id: string | number;
  /**
   * 课程id
   */
  courseId: string | number;
  /**
   * 课程名称
   */
  courseName: string;

  /**
   * 教练id
   */
  teacherId: string | number;

  /**
   * 教练名称
   */
  teacherName: string;

  /**
   * 开始日期
   */
  startDate: string;

  /**
   * 结束日期
   */
  endDate: string;

  /**
   * 预约人
   */
  bookingPeople: string;

  /**
   * 预约状态
   */
  bookState: string;

  /**
   * 备注
   */
  remark: string;
}

export interface CourseOrderInfoForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 课程id
   */
  courseId?: string | number;

  /**
   * 课程名称
   */
  courseName?: string;

  /**
   * 教练id
   */
  teacherId?: string | number;

  /**
   * 教练名称
   */
  teacherName?: string;

  /**
   * 开始日期
   */
  startDate?: string;

  /**
   * 结束日期
   */
  endDate?: string;

  /**
   * 预约人
   */
  bookingPeople?: string;

  /**
   * 预约状态
   */
  bookState?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface CourseOrderInfoQuery extends PageQuery {
  /**
   * 课程名称
   */
  courseName?: string;

  /**
   * 教练名称
   */
  teacherName?: string;

  /**
   * 开始日期
   */
  startDate?: string;

  /**
   * 结束日期
   */
  endDate?: string;

  /**
   * 预约人
   */
  bookingPeople?: string;

  /**
   * 预约状态
   */
  bookState?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
