export interface DrivingSchoolVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 驾校全称
   */
  schoolFullname: string;

  /**
   * 驾校简称
   */
  schoolShortname: string;

  /**
   * 联系人
   */
  contact: string;

  /**
   * 联系人电话
   */
  phoneNumber: number;

  /**
   * 所属区域名称
   */
  locationAreaName: string;

  /**
   * 区划代码
   */
  locationAreaCode?: Array<string>;
}

export interface DrivingSchoolForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 驾校全称
   */
  schoolFullname?: string;

  /**
   * 驾校简称
   */
  schoolShortname?: string;

  /**
   * 驾校资质
   */
  schoolQuality?: string;

  /**
   * 教学经验
   */
  teachingExperience?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 联系人
   */
  contact?: string;

  /**
   * 联系人电话
   */
  phoneNumber?: number;

  /**
   * 所属区域名称
   */
  locationAreaName?: string;

  /**
   * 省
   */
  locationProvince?: string;

  /**
   * 市
   */
  locationCity?: string;

  /**
   * 县
   */
  locationCountry?: string;

  /**
   * 县
   */
  locationAreaCode?: Array<string>;
}

export interface DrivingSchoolQuery extends PageQuery {
  /**
   * 驾校全称
   */
  schoolFullname?: string;

  /**
   * 驾校简称
   */
  schoolShortname?: string;

  /**
   * 联系人
   */
  contact?: string;

  /**
   * 联系人电话
   */
  phoneNumber?: number;

  /**
   * 所属区域名称
   */
  locationAreaName?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
