<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="ID" prop="id">
            <el-input v-model="queryParams.id" placeholder="请输入ID" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="车型" prop="carType">
            <car-type v-model="queryParams.carType" clearable placeholder="请选择车型" @keyup.enter="handleQuery"></car-type>
          </el-form-item>
          <el-form-item label="科目" prop="subject">
            <subject v-model="queryParams.subject" clearable placeholder="请选择科目" @keyup.enter="handleQuery"></subject>
          </el-form-item>
          <el-form-item label="章节" prop="chapter">
            <chapter v-model="queryParams.chapter" clearable placeholder="请选择章节" @keyup.enter="handleQuery"></chapter>
          </el-form-item>
          <el-form-item label="专题" prop="special">
            <special v-model="queryParams.special" clearable placeholder="请选择专题" @keyup.enter="handleQuery"></special>
          </el-form-item>
          <el-form-item label="题目" prop="question">
            <el-input v-model="queryParams.question" placeholder="请输入题目" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="题型" prop="type">
            <el-select v-model="queryParams.type" style="width: 240px" placeholder="请选择题型" clearable>
              <el-option v-for="dict in question_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="答案" prop="answer">
            <el-input v-model="queryParams.answer" placeholder="请输入答案" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:question:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:question:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:question:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:question:export']" type="warning" plain icon="Upload" @click="handleImport">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:question:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="questionList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="true" label="ID" align="center" prop="id" />
        <el-table-column label="车型" align="center" prop="carType" />
        <el-table-column label="科目" align="center" prop="subject" />
        <el-table-column label="章节" align="center" prop="chapter" />
        <el-table-column label="专题" align="center" prop="special" />
        <el-table-column label="题型" align="center" prop="type">
          <template #default="scope">
            <dict-tag :options="question_type" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="题目" align="center" prop="question" width="300">
          <template #default="scope">
            <editor v-model="scope.row.question" :read-only="true" :min-height="50" :height="100"></editor>
          </template>
        </el-table-column>
        <el-table-column label="图片" align="center" prop="image" width="300">
          <template #default="scope">
            <ImagePreview v-if="scope.row.image" :width="100" :height="100" :src="scope.row.image" :preview-src="[scope.row.image]" />
          </template>
        </el-table-column>
        <el-table-column label="选项" align="center" prop="optionsFormat" />
        <el-table-column label="答案" align="center" prop="answerFormat" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['scjk:question:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['scjk:question:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <i-ep-upload-filled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="text-center el-upload__tip">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改题库对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="700px" append-to-body>
      <el-form ref="questionFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="车型" prop="carType">
          <car-type v-model="form.carType" :multiple="true" clearable placeholder="请选择车型"></car-type>
        </el-form-item>
        <el-form-item label="科目" prop="subject">
          <subject v-model="form.subject" :multiple="true" clearable placeholder="请选择科目"></subject>
        </el-form-item>
        <el-form-item label="章节" prop="chapter">
          <chapter v-model="form.chapter" :multiple="true" clearable placeholder="请选择章节"></chapter>
        </el-form-item>
        <el-form-item label="专题" prop="special">
          <special v-model="form.special" :multiple="true" clearable placeholder="请选择专题"></special>
        </el-form-item>
        <el-form-item label="题型" prop="type">
          <el-radio-group v-model="form.type" @change="questionTypeOnChange">
            <el-radio v-for="qsType in question_type" :key="qsType.value" :value="qsType.value">{{ qsType.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="题目" prop="question">
          <div class="editor">
            <quill-editor ref="questionQuillEditorRef" v-model:content="form.question" content-type="html" :options="options" style="height: 200px" />
          </div>
        </el-form-item>
        <el-form-item label="图片" prop="image">
          <image-upload v-model="form.image" :limit="1"></image-upload>
        </el-form-item>
        <el-form-item label="选项" prop="options">
          <el-form-item
            v-for="(item, index) in form.options"
            :key="item.prefix"
            :label="item.prefix"
            label-width="50px"
            class="question-option-label"
          >
            <el-input v-model="item.prefix" style="width: 50px" />
            <el-input v-model="item.content" class="question-option-content-input" />
            <el-button type="danger" class="question-option-remove" :icon="Delete" @click="removeQustionOption(index)"></el-button>
          </el-form-item>
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button type="primary" @click="addQuestionOption">添加</el-button>
        </el-form-item>
        <el-form-item label="答案" prop="answer">
          <!-- 单选||判断 -->
          <template v-if="form.type === 'S' || form.type === 'J'">
            <el-radio-group v-model="form.answer">
              <el-radio v-for="item in form.options" :key="item.prefix" :value="item.prefix">{{ item.prefix }}</el-radio>
            </el-radio-group>
          </template>
          <!-- 多选 -->
          <template v-else-if="form.type === 'M'">
            <el-checkbox-group v-model="form.answer">
              <el-checkbox v-for="item in form.options" :key="item.prefix" :label="item.prefix" :value="item.prefix"></el-checkbox>
            </el-checkbox-group>
          </template>
        </el-form-item>
        <el-form-item label="解析" prop="analysis">
          <div class="editor">
            <quill-editor ref="analysisQuillEditorRef" v-model:content="form.analysis" content-type="html" :options="options" style="height: 200px" />
          </div>
        </el-form-item>
        <el-form-item label="解析音频" prop="analysisAudio">
          <audio-upload v-model="form.analysisAudio" :limit="1"></audio-upload>
        </el-form-item>
        <el-form-item label="解析视频" prop="analysisVideo">
          <video-upload v-model="form.analysisVideo" :limit="1"></video-upload>
        </el-form-item>
        <el-form-item label="技巧" prop="skill">
          <div class="editor">
            <quill-editor ref="skillQuillEditorRef" v-model:content="form.skill" content-type="html" :options="options" style="height: 200px" />
          </div>
        </el-form-item>
        <el-form-item label="技巧音频" prop="skillAudio">
          <audio-upload v-model="form.skillAudio" :limit="1"></audio-upload>
        </el-form-item>
        <el-form-item label="技巧视频" prop="skillVideo">
          <video-upload v-model="form.skillVideo" :limit="1"></video-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Question" lang="ts">
import { listQuestion, delQuestion, getQuestion, addQuestion, updateQuestion } from '@/api/scjk/questionbank/question';
import { QuestionVO, QuestionQuery, QuestionForm } from '@/api/scjk/questionbank/question/types';
import CarType from '@/components/Scjk/CarType/index.vue';
import Chapter from '@/components/Scjk/Chapter/index.vue';
import Special from '@/components/Scjk/Special/index.vue';
import Subject from '@/components/Scjk/Subject/index.vue';
import ImageUpload from '@/components/ImageUpload';
import AudioUpload from '@/components/Scjk/AudioUpload';
import VideoUpload from '@/components/Scjk/VideoUpload';
import { Delete } from '@element-plus/icons-vue';

import { QuillEditor } from '@vueup/vue-quill';
// import '@vueup/vue-quill/dist/vue-quill.snow.css';

import Editor from '@/components/Scjk/Editor/index.vue';
import ImagePreview from '@/components/Scjk/ImagePreview/index.vue';

import { globalHeaders } from '@/utils/request';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { question_type } = toRefs<any>(proxy?.useDict('question_type'));

const questionList = ref<QuestionVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const questionFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const queryParams = ref<QuestionQuery>({
  pageNum: 1,
  pageSize: 10,
  id: undefined,
  carType: undefined,
  subject: undefined,
  chapter: undefined,
  special: undefined,
  question: undefined,
  answer: undefined,
  type: undefined,
  params: {}
});

const initFormData: QuestionForm = {
  id: undefined,
  carType: undefined,
  subject: undefined,
  chapter: undefined,
  special: undefined,
  question: undefined,
  image: undefined,
  options: [
    { prefix: 'A', content: '' },
    { prefix: 'B', content: '' },
    { prefix: 'C', content: '' },
    { prefix: 'D', content: '' }
  ],
  answer: undefined,
  analysis: undefined,
  analysisAudio: undefined,
  analysisVideo: undefined,
  skill: undefined,
  skillAudio: undefined,
  skillVideo: undefined,
  type: 'S' // 默认单选
};

const form = ref<QuestionForm>({ ...initFormData });
const rules = ref<ElFormRules>({
  id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }],
  carType: [{ required: true, message: '车型不能为空', trigger: 'change', type: 'array' }],
  subject: [{ required: true, message: '科目不能为空', trigger: 'change', type: 'array' }],
  question: [{ required: true, message: '题目不能为空', trigger: 'blur' }],
  options: [{ required: true, message: '选项不能为空', trigger: 'blur' }],
  answer: [{ required: true, message: '答案不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '题型不能为空', trigger: 'blur' }]
});

const questionQuillEditorRef = ref();
const analysisQuillEditorRef = ref();
const skillQuillEditorRef = ref();

const uploadRef = ref<ElUploadInstance>();

/*** 用户导入参数 */
const upload = reactive<ImportOption>({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: globalHeaders(),
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/Scjk/question/importData'
});

/** 查询题库列表 */
const getList = async () => {
  loading.value = true;
  const res = await listQuestion(queryParams.value);
  questionList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: QuestionVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加题库';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: QuestionVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getQuestion(_id);
  // 格式化
  const tempForm: QuestionForm = { ...res.data };
  tempForm.carType = tempForm.carType?.split(',');
  tempForm.subject = tempForm.subject?.split(',');
  tempForm.chapter = tempForm.chapter?.split(',');
  tempForm.special = tempForm.special?.split(',');
  tempForm.options = JSON.parse(tempForm.options);

  // 多选时才转数组
  tempForm.answer = tempForm.type === 'M' ? tempForm.answer?.split(',') : tempForm.answer;

  form.value = { ...tempForm };
  dialog.visible = true;
  dialog.title = '修改题库';
};

/** 删除按钮操作 */
const handleDelete = async (row?: QuestionVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除题库编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delQuestion(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/question/export',
    {
      ...queryParams.value
    },
    `题库_${new Date().getTime()}.xlsx`
  );
};

/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('scjk/question/importTemplate', {}, `题库_模板_${new Date().getTime()}.xlsx`);
};

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = '题库导入';
  upload.open = true;
};

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.handleRemove(file);
  ElMessageBox.alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', {
    dangerouslyUseHTMLString: true
  });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit();
}

// 富文本编辑器配置
const options = ref({
  placeholder: '',
  readOnly: false,
  // theme: 'snow',
  bounds: document.body,
  debug: 'warn',
  modules: {
    // 工具栏配置
    toolbar: {
      container: [
        ['bold', 'italic', 'underline'], // 加粗 斜体 下划线 删除线
        // ['blockquote', 'code-block'], // 引用  代码块
        // [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
        // [{ indent: '-1' }, { indent: '+1' }], // 缩进
        [{ size: ['small', false, 'large', 'huge'] }], // 字体大小
        [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
        [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
        // [{ align: [] }], // 对齐方式
        ['clean'] // 清除文本格式
      ]
    }
  }
});

/**
 * 选择题型
 */
const questionTypeOnChange = (value: string) => {
  if (value === 'M') {
    form.value.answer = [];
  } else {
    form.value.answer = '';
  }
};

/**
 * 添加选项
 */
const addQuestionOption = () => {
  form.value.options.push({ prefix: '', content: '' });
};

/**
 * 删除选项
 * @param index
 */
const removeQustionOption = (index: number) => {
  form.value.options.splice(index, 1);
};

/** 取消按钮 */
const cancel = () => {
  reset();
  clearRichEditor();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  questionFormRef.value?.resetFields();
};

/**
 * 清除富文本数据
 */
const clearRichEditor = () => {
  questionQuillEditorRef.value.setText('');
  analysisQuillEditorRef.value.setText('');
  skillQuillEditorRef.value.setText('');
};

/** 提交按钮 */
const submitForm = async () => {
  questionFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 格式化处理
      const formStringify = JSON.stringify(form.value);
      const saveForm: QuestionForm = JSON.parse(formStringify);

      saveForm.carType = saveForm.carType?.join();
      saveForm.subject = saveForm.subject?.join();
      saveForm.chapter = saveForm.chapter?.join();
      saveForm.special = saveForm.special?.join();
      saveForm.options = JSON.stringify(saveForm.options);
      // 多选时逗号分隔
      saveForm.answer = saveForm.type === 'M' ? saveForm.answer?.join() : saveForm.answer;

      if (saveForm.id) {
        await updateQuestion(saveForm).finally(() => (buttonLoading.value = false));
      } else {
        await addQuestion(saveForm).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      clearRichEditor();
      await getList();
    }
  });
};

onMounted(() => {
  getList();
});
</script>

<style>
.question-option-label {
  margin-top: 10px;
  margin-bottom: 10px !important;
  width: 90%;
}

.question-option-content-input {
  margin-left: 8px;
  width: 60%;
}

.question-option-remove {
  margin-left: 20px;
}

.editor-img-uploader {
  display: none;
}
.editor,
.ql-toolbar {
  white-space: pre-wrap !important;
  line-height: normal !important;
}
.quill-img {
  display: none;
}
.ql-snow .ql-tooltip[data-mode='link']::before {
  content: '请输入链接地址:';
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0;
  content: '保存';
  padding-right: 0;
}
.ql-snow .ql-tooltip[data-mode='video']::before {
  content: '请输入视频地址:';
}
.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: '14px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='small']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='small']::before {
  content: '10px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='large']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='large']::before {
  content: '18px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='huge']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='huge']::before {
  content: '32px';
}
.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: '文本';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='1']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {
  content: '标题1';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='2']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before {
  content: '标题2';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='3']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before {
  content: '标题3';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='4']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before {
  content: '标题4';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='5']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before {
  content: '标题5';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='6']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before {
  content: '标题6';
}
.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: '标准字体';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='serif']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='serif']::before {
  content: '衬线字体';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='monospace']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='monospace']::before {
  content: '等宽字体';
}
</style>
