{"name": "scjk-win", "version": "5.3.2", "description": "速诚驾考管理系统", "author": "云南天度软件", "main": "./out/main/index.js", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/utils": "^3.0.0", "@element-plus/icons-vue": "2.3.1", "@highlightjs/vue-plugin": "2.1.0", "@kjgl77/datav-vue3": "^1.7.3", "@lzwme/get-physical-address": "^1.1.0", "@taoqf/quill-image-resize-module": "^3.0.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "12.7.0", "animate.css": "4.1.1", "await-to-js": "3.0.0", "axios": "1.7.8", "bpmn-js": "16.4.0", "crypto-js": "4.2.0", "diagram-js": "12.3.0", "didi": "9.0.2", "echarts": "5.5.0", "element-plus": "2.8.8", "electron-updater": "^6.2.1", "file-saver": "2.0.5", "fingerprintjs2": "^2.1.4", "fuse.js": "7.0.0", "highlight.js": "11.9.0", "image-conversion": "2.1.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "mathjs": "^12.4.2", "mitt": "^3.0.1", "nprogress": "0.2.0", "pinia": "2.2.6", "qrcode": "^1.5.3", "quill-image-drop-module": "^1.0.3", "screenfull": "6.0.2", "vue": "3.5.13", "vue-cropper": "1.1.1", "vue-draggable-plus": "^0.4.0", "vue-i18n": "10.0.5", "vue-json-pretty": "2.4.0", "vue-router": "4.4.5", "vue-types": "5.1.3", "vxe-table": "4.5.22", "xgplayer": "^3.0.17", "xgplayer-flv": "^3.0.17", "xgplayer-hls": "^3.0.17", "xgplayer-mp4": "^3.0.17", "xgplayer-music": "^3.0.17"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-ts": "^2.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@iconify/json": "2.2.276", "@intlify/unplugin-vue-i18n": "3.0.1", "@rushstack/eslint-patch": "^1.10.3", "@types/crypto-js": "4.2.2", "@types/file-saver": "2.0.7", "@types/fingerprintjs2": "^2.0.0", "@types/js-cookie": "3.0.6", "@types/nprogress": "0.2.3", "@unocss/preset-attributify": "66.0.0", "@unocss/preset-icons": "66.0.0", "@unocss/preset-uno": "66.0.0", "@vitejs/plugin-vue": "5.2.1", "@vue/compiler-sfc": "3.4.23", "@vue/eslint-config-prettier": "10.2.0", "@vue/eslint-config-typescript": "14.4.0", "autoprefixer": "10.4.20", "electron": "^22.1.4", "electron-builder": "^24.13.3", "electron-updater": "^6.2.1", "electron-vite": "^2.3.0", "eslint": "9.21.0", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-vue": "9.32.0", "fast-glob": "3.3.2", "globals": "16.0.0", "postcss": "8.4.36", "prettier": "3.5.2", "sass": "1.84.0", "typescript": "5.7.3", "unocss": "66.0.0", "unplugin-auto-import": "0.17.5", "unplugin-element-plus": "^0.8.0", "unplugin-icons": "0.18.5", "unplugin-vue-components": "28.0.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.4.11", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.7.1", "vitest": "3.0.5", "vue": "^3.4.30", "vue-tsc": "2.2.0"}}