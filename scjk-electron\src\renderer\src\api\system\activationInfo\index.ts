import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ActivationInfoVO, ActivationInfoForm, ActivationInfoQuery } from '@/api/system/activationInfo/types';

/**
 * 查询激活信息列表
 * @param query
 * @returns {*}
 */

export const listActivationInfo = (query?: ActivationInfoQuery): AxiosPromise<ActivationInfoVO[]> => {
  return request({
    url: '/system/activationInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询激活信息详细
 * @param serialNum
 */
export const getActivationInfo = (serialNum: string | number): AxiosPromise<ActivationInfoVO> => {
  return request({
    url: '/system/activationInfo/' + serialNum,
    method: 'get'
  });
};

/**
 * 新增激活信息
 * @param data
 */
export const addActivationInfo = (data: ActivationInfoForm) => {
  return request({
    url: '/system/activationInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改激活信息
 * @param data
 */
export const updateActivationInfo = (data: ActivationInfoForm) => {
  return request({
    url: '/system/activationInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除激活信息
 * @param serialNum
 */
export const delActivationInfo = (serialNum: string | number | Array<string | number>) => {
  return request({
    url: '/system/activationInfo/' + serialNum,
    method: 'delete'
  });
};
export const getCertInfo = () => {
  return request({
    url: '/system/certificateAuthInfo/getCertInfo',
    method: 'get'
  });
};
export const getDeviceInfo = () => {
  return request({
    url: '/system/activationInfo/getDeviceInfo',
    method: 'get'
  });
};
export const getActivation = (macAddress: string) => {
  return request({
    url: '/system/activationInfo/getActivation/' + macAddress,
    method: 'get'
  });
};
