<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="授权编号" prop="certificateCode">
              <el-input v-model="queryParams.certificateCode" placeholder="请输入授权编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:certificateAuthInfo:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:certificateAuthInfo:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:certificateAuthInfo:remove']"
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:certificateAuthInfo:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="certificateAuthInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="false" label="主键" align="center" prop="id" />
        <el-table-column label="授权编号" align="center" prop="certificateCode" />
        <el-table-column label="被授权人账号" align="center" prop="authorizedAccount" />
        <el-table-column label="被授权人" align="center" prop="authorizedNickName" />
        <el-table-column label="授权数量" align="center" prop="authorizedNum" />
        <el-table-column label="绑定数量" align="center" prop="bindingNum" />
        <el-table-column label="激活数量" align="center" prop="activedNum" />
        <el-table-column label="有效期" align="center" prop="period">
          <template #default="scope">
            <span>{{ parseTime(scope.row.period, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button
                v-hasPermi="['system:certificateAuthInfo:edit']"
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                v-hasPermi="['system:certificateAuthInfo:remove']"
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改证书授权信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" append-to-body>
      <el-form ref="certificateAuthInfoFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="授权编号" prop="certificateCode">
          <el-input v-model="form.certificateCode" disabled placeholder="请输入授权编号" />
        </el-form-item>
        <el-form-item>
          <el-button v-if="isShow" type="primary" icon="Key" :loading="buttonLoading" @click="genCertificateCode">生成</el-button>
        </el-form-item>
        <el-form-item label="授权数量" prop="authorizedNum">
          <el-input-number v-model="form.authorizedNum" :min="1" placeholder="请输入授权数量" />
        </el-form-item>
        <el-form-item label="授权人" prop="authorizedId">
          <el-select v-model="form.authorizedId" placeholder="请选择授权人" clearable filterable>
            <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="有效期" prop="period">
          <el-date-picker v-model="form.period" placeholder="请选择有效期" clearable type="date" value-format="YYYY-MM-DD" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CertificateAuthInfo" lang="ts">
import {
  listCertificateAuthInfo,
  getCertificateAuthInfo,
  delCertificateAuthInfo,
  addCertificateAuthInfo,
  updateCertificateAuthInfo,
  genCertCode
} from '@/api/system/certificateAuthInfo';
import { CertificateAuthInfoVO, CertificateAuthInfoQuery, CertificateAuthInfoForm } from '@/api/system/certificateAuthInfo/types';
import { getMemberList } from '@/api/system/role';
import { UserVO } from '@/api/system/user/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const isShow = ref(true);
const certificateAuthInfoList = ref<CertificateAuthInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const certificateAuthInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CertificateAuthInfoForm = {
  id: undefined,
  certificateCode: undefined,
  authorizedAccount: undefined,
  authorizedId: undefined,
  authorizedNum: undefined,
  bindingNum: undefined,
  activedNum: undefined,
  period: undefined
};

const dateRange = ref<[DateModelType, DateModelType]>(['', '']);
const userList = ref<UserVO[]>([]);

const data = reactive<PageData<CertificateAuthInfoForm, CertificateAuthInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    certificateCode: undefined,
    authorizedAccount: undefined,
    params: {}
  },
  rules: {
    certificateCode: [{ required: true, message: '授权编号不能为空', trigger: 'blur' }],
    authorizedId: [{ required: true, message: '授权人不能为空', trigger: 'change' }],
    authorizedNum: [{ required: true, message: '授权数量不能为空', trigger: 'blur' }],
    period: [{ required: true, message: '有效期不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询证书授权信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCertificateAuthInfo(queryParams.value);
  certificateAuthInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  certificateAuthInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: CertificateAuthInfoVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = async () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加证书授权信息';
  const query = {
    roleKey: 'teacher'
  };
  const res = await getMemberList(proxy?.addDateRange(query, dateRange.value));
  userList.value = res.rows;
  isShow.value = true;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: CertificateAuthInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getCertificateAuthInfo(_id);
  Object.assign(form.value, res.data);
  const query = {
    roleKey: 'teacher'
  };
  const userRes = await getMemberList(proxy?.addDateRange(query, dateRange.value));
  userList.value = userRes.rows;
  isShow.value = false;
  dialog.visible = true;
  dialog.title = '修改证书授权信息';
};

/** 提交按钮 */
const submitForm = () => {
  certificateAuthInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateCertificateAuthInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addCertificateAuthInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CertificateAuthInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('删除授权的同时也会删除相应设备的授权信息，是否确认删除？').finally(() => (loading.value = false));
  await delCertificateAuthInfo(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/certificateAuthInfo/export',
    {
      ...queryParams.value
    },
    `certificateAuthInfo_${new Date().getTime()}.xlsx`
  );
};
const genCertificateCode = async () => {
  buttonLoading.value = true;
  await genCertCode().then((res) => (form.value.certificateCode = res));
  buttonLoading.value = false;
};
onMounted(() => {
  getList();
});
</script>
