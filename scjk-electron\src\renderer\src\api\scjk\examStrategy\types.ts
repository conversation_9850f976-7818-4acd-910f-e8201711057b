export interface ExamStrategyVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 攻略标题
   */
  title: string;

  /**
   * 图标URL
   */
  iconUrl: string;

  /**
   * 背景颜色(十六进制色值)
   */
  bgColor: string;

  /**
   * 攻略内容(富文本)
   */
  content: string;

  /**
   * 排序号(升序)
   */
  sortNum: number;

  /**
   * 状态(0-启用 1-停用)
   */
  status: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 删除标志(0-未删除 1-已删除)
   */
  deleted: string;

}

export interface ExamStrategyForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 攻略标题
   */
  title?: string;

  /**
   * 图标URL
   */
  iconUrl?: string;

  /**
   * 背景颜色(十六进制色值)
   */
  bgColor?: string;

  /**
   * 攻略内容(富文本)
   */
  content?: string;

  /**
   * 排序号(升序)
   */
  sortNum?: number;

  /**
   * 状态(0-启用 1-停用)
   */
  status?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 删除标志(0-未删除 1-已删除)
   */
  deleted?: string;

}

export interface ExamStrategyQuery extends PageQuery {

  /**
   * 删除标志(0-未删除 1-已删除)
   */
  deleted?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



