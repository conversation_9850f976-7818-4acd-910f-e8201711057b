export interface ExamInfoVO {
  id: string;
  /**
   * 考试科目
   */
  examSubject: string;

  /**
   * 考试车型
   */
  carType: number;

  /**
   * 学员姓名
   */
  studentName: string;

  /**
   * 考试原因
   */
  examReason: string;

  /**
   * 预约场次
   */
  examTimes: string;

  /**
   * 考试开始时间
   */
  // startDate: string;

  /**
   * 考试结束时间
   */
  // endDate: string;

  /**
   * 考试场地
   */
  examLocation: string;

  /**
   * 考试日期
   */
  examDate: string;

  remark: string;
}

export interface ExamInfoForm extends BaseEntity {
  /**
   * 考试科目
   */
  examSubject?: string;

  /**
   * 考试车型
   */
  carType?: number;

  /**
   * 学员姓名
   */
  studentName?: string;

  /**
   * 考试原因
   */
  examReason?: string;

  /**
   * 预约场次
   */
  examTimes?: string;

  /**
   * 考试开始时间
   */
  // startDate?: string;

  /**
   * 考试结束时间
   */
  // endDate?: string;

  /**
   * 考试场地
   */
  examLocation?: string;

  /**
   * 考试日期
   */
  examDate?: string;

  remark?: string;
}

export interface ExamInfoQuery extends PageQuery {
  /**
   * 考试科目
   */
  examSubject?: string;

  /**
   * 考试车型
   */
  carType?: number;

  /**
   * 学员姓名
   */
  studentName?: string;

  /**
   * 考试原因
   */
  examReason?: string;

  /**
   * 预约场次
   */
  examTimes?: string;

  /**
   * 考试开始时间
   */
  // startDate?: string;

  /**
   * 考试结束时间
   */
  // endDate?: string;

  /**
   * 考试场地
   */
  examLocation?: string;

  /**
   * 考试日期
   */
  examDate?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
