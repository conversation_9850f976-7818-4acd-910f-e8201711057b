<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="用户姓名" prop="nickName">
              <el-input v-model="queryParams.nickName" placeholder="请输入用户姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="登录时间" style="width: 308px">
              <el-date-picker
                v-model="dateRange"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="12" :offset="6">
            <el-text class="mx-1" type="warning">
              请注意，这个查询假设所有用户都在同一个时间退出，这并不符合实际情况。但在没有实际退出记录的情况下，这可以作为一个合理的近似值。当然，大部分用户并不会主动退出登录。所以，如果你需要更精确的在线时间统计，需要单独查询用户相应的在线时间，但该查询仍然只能作为参考。</el-text
            >
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        ref="loginInfoTableRef"
        v-loading="loading"
        :data="loginInfoList"
        :default-sort="defaultSort"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <!--        <el-table-column-->
        <!--          label="用户名称"-->
        <!--          align="center"-->
        <!--          prop="userName"-->
        <!--          :show-overflow-tooltip="true"-->
        <!--          sortable="custom"-->
        <!--          :sort-orders="['descending', 'ascending']"-->
        <!--        />-->
        <el-table-column
          label="用户姓名"
          align="center"
          prop="nickName"
          :show-overflow-tooltip="true"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        />
        <el-table-column
          label="用户在线时长（分）"
          align="center"
          prop="totalOnlineMinutes"
          :show-overflow-tooltip="true"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        />
        <el-table-column label="操作" fixed="right" width="180" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="精确查询" placement="top">
              <el-button link type="primary" icon="View" @click="queryOnLineTime(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
  </div>
  <el-dialog v-model="dialog.visible" title="用户在线时长统计" width="800" append-to-body>
    <el-descriptions class="margin-top" title="在线时长统计" :column="2" :size="size" border>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            <el-icon :style="iconStyle">
              <user />
            </el-icon>
            用户账号
          </div>
        </template>
        {{ userOnlineInfo.nickName }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            <el-icon :style="iconStyle">
              <Timer />
            </el-icon>
            累计在线时间
          </div>
        </template>
        {{ userOnlineInfo.realTime }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            <el-icon :style="iconStyle">
              <Timer />
            </el-icon>
            累计在线时间(分)
          </div>
        </template>
        {{ userOnlineInfo.totalOnlineMinutes }}分钟
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            <el-icon :style="iconStyle">
              <Timer />
            </el-icon>
            累计在线时间(秒)
          </div>
        </template>
        {{ userOnlineInfo.totalOnlineSeconds }}秒
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<script setup name="Logininfor" lang="ts">
import { onlineTime, queryUserOnlineTime } from '@/api/monitor/loginInfo';
import { LoginInfoQuery, LoginInfoVO } from '@/api/monitor/loginInfo/types';
import type { ComponentSize } from 'element-plus';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const loginInfoList = ref<LoginInfoVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<number | string>>([]);
const single = ref(true);
const multiple = ref(true);
const selectName = ref<Array<string>>([]);
const total = ref(0);
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);
const defaultSort = ref<any>({ prop: 'loginTime', order: 'descending' });

const queryFormRef = ref<ElFormInstance>();
const loginInfoTableRef = ref<ElTableInstance>();
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const iconStyle = computed(() => {
  const marginMap = {
    large: '8px',
    default: '6px',
    small: '4px'
  };
  return {
    marginRight: marginMap[size.value] || marginMap.default
  };
});
const blockMargin = computed(() => {
  const marginMap = {
    large: '32px',
    default: '28px',
    small: '24px'
  };
  return {
    marginTop: marginMap[size.value] || marginMap.default
  };
});

const userOnlineInfo = ref({});
const size = ref<ComponentSize>('default');
// 查询参数
const queryParams = ref<LoginInfoQuery>({
  pageNum: 1,
  pageSize: 10,
  ipaddr: '',
  userName: '',
  status: ''
  // orderByColumn: defaultSort.value.prop,
  // isAsc: defaultSort.value.order
});

/** 查询登录日志列表 */
const getList = async () => {
  loading.value = true;
  const res = await onlineTime(proxy?.addDateRange(queryParams.value, dateRange.value));
  console.log(res);
  loginInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = ['', ''];
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  loginInfoTableRef.value?.sort(defaultSort.value.prop, defaultSort.value.order);
};
/** 多选框选中数据 */
const handleSelectionChange = (selection: LoginInfoVO[]) => {
  ids.value = selection.map((item) => item.infoId);
  multiple.value = !selection.length;
  single.value = selection.length != 1;
  selectName.value = selection.map((item) => item.userName);
};
/** 排序触发事件 */
const handleSortChange = (column: any) => {
  queryParams.value.orderByColumn = column.prop;
  queryParams.value.isAsc = column.order;
  getList();
};
const queryOnLineTime = async (row: LoginInfoVO) => {
  dialog.visible = true;
  await queryUserOnlineTime(row.nickName).then((res) => {
    userOnlineInfo.value = res;
  });
};

onMounted(() => {
  getList();
});
</script>
