export interface TeacherVO {
  id: string;
  userId: string;
  /**
   * 教师姓名
   */
  name: string;

  /**
   * 年龄
   */
  age: number;

  /**
   * 性别
   */
  gender: string;

  /**
   * 驾照等级
   */
  licenseLevel: string;

  /**
   * 驾龄
   */
  drivingExperience: number;

  /**
   * 教师照片
   */
  teacherPhoto: string;
}

export interface TeacherForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  userId: string;

  /**
   * 教师姓名
   */
  name?: string;

  /**
   * 年龄
   */
  age?: number;

  /**
   * 性别
   */
  gender?: string;

  /**
   * 驾照等级
   */
  licenseLevel?: string;

  /**
   * 驾龄
   */
  drivingExperience?: number;

  /**
   * 教师照片
   */
  teacherPhoto?: string;

  /**
   * 教师资质
   */
  teacherCredentials?: string;

  /**
   * 教学经验
   */
  teachingExperience?: string;
}

export interface TeacherQuery extends PageQuery {
  /**
   * 教师姓名
   */
  name?: string;

  userId: string;

  /**
   * 年龄
   */
  age?: number;

  /**
   * 性别
   */
  gender?: string;

  /**
   * 驾照等级
   */
  licenseLevel?: string;

  /**
   * 驾龄
   */
  drivingExperience?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
