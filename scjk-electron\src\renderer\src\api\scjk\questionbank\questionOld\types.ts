export interface QuestionOldVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 车型
   */
  carType: string;

  /**
   * 科目
   */
  subject: string;

  /**
   * 章节
   */
  chapter: string;

  /**
   * 专题
   */
  special: string;

  /**
   * 题型[单选-S,多选-M,判断-J]
   */
  type: string;

  /**
   * 题目
   */
  question: string;

  /**
   * 题目图片
   */
  questionImage: string;

  /**
   * 题目关键字
   */
  questionKeyword: string;

  /**
   * 选项
   */
  options: string;

  /**
   * 答案
   */
  answer: string;

  /**
   * 答案关键字
   */
  answerKeyword: string;

  /**
   * 解析
   */
  analysis: string;

  /**
   * 技巧
   */
  skill: string;

  /**
   * 技巧关键字
   */
  skillKeyword: string;

  /**
   * 德旭
   */
  dexu: string;

  /**
   * 强化
   */
  strengthen: string;
}

export interface QuestionOldForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 车型
   */
  carType?: string | Array<string>;

  /**
   * 科目
   */
  subject?: string | Array<string>;

  /**
   * 章节
   */
  chapter?: string | Array<string>;

  /**
   * 专题
   */
  special?: string | Array<string>;

  /**
   * 题型[单选-S,多选-M,判断-J]
   */
  type?: string;

  /**
   * 题目
   */
  question?: string;

  /**
   * 题目图片
   */
  questionImage?: string;

  /**
   * 题目关键字
   */
  questionKeyword?: string;

  /**
   * 选项
   */
  options?: string | Array<Option>;

  /**
   * 答案
   */
  answer?: string | Array<string>;

  /**
   * 答案关键字
   */
  answerKeyword?: string;

  /**
   * 解析
   */
  analysis?: string;

  /**
   * 技巧
   */
  skill?: string;

  /**
   * 技巧关键字
   */
  skillKeyword?: string;

  /**
   * 德旭
   */
  dexu?: string;

  /**
   * 强化
   */
  strengthen?: string;
}

export interface QuestionOldQuery extends PageQuery {
  /**
   * 车型
   */
  carType?: string;

  /**
   * 科目
   */
  subject?: string;

  /**
   * 章节
   */
  chapter?: string;

  /**
   * 专题
   */
  special?: string;

  /**
   * 题型[单选-S,多选-M,判断-J]
   */
  type?: string;

  /**
   * 原ID
   */
  oldId?: string;

  /**
   * 题目
   */
  question?: string;

  /**
   * 题目图片
   */
  questionImage?: string;

  /**
   * 题目关键字
   */
  questionKeyword?: string;

  /**
   * 选项
   */
  options?: string;

  /**
   * 答案
   */
  answer?: string;

  /**
   * 答案关键字
   */
  answerKeyword?: string;

  /**
   * 解析
   */
  analysis?: string;

  /**
   * 技巧
   */
  skill?: string;

  /**
   * 技巧关键字
   */
  skillKeyword?: string;

  /**
   * 德旭
   */
  dexu?: string;

  /**
   * 强化
   */
  strengthen?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 选项
 */
export interface Option {
  prefix?: string;
  content?: string;
}
