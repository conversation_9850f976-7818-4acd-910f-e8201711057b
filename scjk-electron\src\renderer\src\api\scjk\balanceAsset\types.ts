export interface BalanceAssetVO {
  id: string | number;
  /**
   * 用户id
   */
  userId: string | number;

  /**
   * 资产余额
   */
  balance: number;

  /**
   * 用户编码
   */
  userCode: string;

  status: number;
}

export interface BalanceAssetForm extends BaseEntity {
  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 资产余额
   */
  balance?: number;

  /**
   * 用户编码
   */
  userCode?: string;

  status?: number;
}

export interface BalanceAssetQuery extends PageQuery {
  /**
   * 日期范围参数
   */
  params?: any;
}
