<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="学员" prop="studentName">
              <el-input v-model="queryParams.studentName" placeholder="请输入学员" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="试卷" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入试卷" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-select v-model="queryParams.type" placeholder="请选择类型" size="large" style="width: 240px">
                <el-option label="试卷" value="P" />
                <el-option label="模拟" value="M" />
              </el-select>
            </el-form-item>
            <el-form-item label="车型" prop="carType">
              <car-type v-model="queryParams.carType" clearable placeholder="请选择车型" @keyup.enter="handleQuery"></car-type>
            </el-form-item>
            <el-form-item label="科目" prop="subject">
              <subject v-model="queryParams.subject" clearable placeholder="请选择科目" @keyup.enter="handleQuery"></subject>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template v-if="isStudent === true" #header>
        <el-row>
          <el-col :span="23"> </el-col>
          <el-col :span="1">
            <el-button type="danger" size="large" style="" @click="handleExit">退出</el-button>
          </el-col>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="paperLogList">
        <!-- <el-table-column label="ID" align="center" prop="id" /> -->
        <el-table-column label="学员" align="center" prop="studentName" fixed />
        <el-table-column label="试卷" align="center" prop="name" />
        <el-table-column label="类型" align="center" prop="type">
          <template #default="scope">
            {{ scope.row.type === 'P' ? '试卷' : '模拟' }}
          </template>
        </el-table-column>
        <el-table-column label="车型" align="center" prop="carTypeName" />
        <el-table-column label="科目" align="center" prop="subjectName" />
        <el-table-column label="得分" align="center" prop="score" />
        <el-table-column label="做对" align="center" prop="correct" />
        <el-table-column label="做错" align="center" prop="error" />
        <el-table-column label="用时(秒)" align="center" prop="spendTime" />
        <el-table-column label="考试时间" align="center" prop="time" />
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="PaperLog" lang="ts">
import useUserStore from '@/store/modules/user';
import { list } from '@/api/scjk/paper/log';
import { PaperLogVO, PaperLogQuery } from '@/api/scjk/paper/log/types';
import CarType from '@/components/Scjk/CarType/index.vue';
import Subject from '@/components/Scjk/Subject/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const paperLogList = ref<PaperLogVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const queryParams = ref<PaperLogQuery>({
  pageNum: 1,
  pageSize: 10,
  id: undefined,
  name: undefined,
  type: undefined,
  carType: undefined,
  subject: undefined,
  score: undefined,
  studentName: undefined,
  time: undefined,
  params: {}
});

const isStudent = computed(() => {
  return useUserStore().isStudent;
});

/** 查询试卷记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await list(queryParams.value);
  paperLogList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/paperLog/export',
    {
      ...queryParams.value
    },
    `考试记录_${new Date().getTime()}.xlsx`
  );
};

/**
 * 退出
 */
const handleExit = () => {
  proxy?.$router.push({
    path: '/index'
  });
};

onMounted(() => {
  getList();
});
</script>
