export interface OrderVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 套餐id
   */
  subscriptionPlanId: string | number;

  /**
   * 订单编号
   */
  orderNo: string;

  /**
   * 账单项目
   */
  itemName: string;

  /**
   * 支付方式
   */
  payment: string;

  /**
   * 描述
   */
  description: string;

  /**
   * 用户id
   */
  userId: string | number;

  /**
   * 数量
   */
  unitsNumber: number;

  /**
   * 订单金额
   */
  orderAmount: number;

  /**
   * 支付成功时间
   */
  paySuccessfulTime: string;

  /**
   * 订单状态
   */
  orderStatus: string;

  delFlag?: string;

  userName?: string;
}

export interface OrderForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 套餐id
   */
  subscriptionPlanId?: string | number;

  /**
   * 订单编号
   */
  orderNo?: string;

  /**
   * 账单项目
   */
  itemName?: string;

  /**
   * 支付方式
   */
  payment?: string;

  /**
   * 描述
   */
  description?: string;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 数量
   */
  unitsNumber?: number;

  /**
   * 订单金额
   */
  orderAmount?: number;

  /**
   * 支付成功时间
   */
  paySuccessfulTime?: string;

  /**
   * 订单状态
   */
  orderStatus?: string;

  delFlag?: string;

  invitationCode?: string;
}

export interface OrderQuery extends PageQuery {
  /**
   * 套餐id
   */
  subscriptionPlanId?: string | number;

  /**
   * 订单编号
   */
  orderNo?: string;

  /**
   * 账单项目
   */
  itemName?: string;

  /**
   * 支付方式
   */
  payment?: string;

  /**
   * 描述
   */
  description?: string;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 数量
   */
  unitsNumber?: number;

  /**
   * 订单金额
   */
  orderAmount?: number;

  /**
   * 支付成功时间
   */
  paySuccessfulTime?: string;

  /**
   * 订单状态
   */
  orderStatus?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
