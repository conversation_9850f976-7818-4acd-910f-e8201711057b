.e-page {
  .e-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--exam-bg-left_right);
    .e-container-left {
      width: 1px;
      background-color: var(--exam-bg-left_right);
    }
    .content-main {
      flex: 1;
      width: 100vw;
      height: 100vh;
      max-width: 1400px;
      display: flex;
      flex-direction: column;
      background-color: var(--exam-bg);
      color: var(--exam-font-color);

      .content-top {
        height: 58vh;
        display: flex;

        .content-top-left {
          width: 160px;
          margin-left: 10px;
          display: flex;
          flex-direction: column;

          .exam-title {
            margin-top: 10px;
            height: 100px;

            .exam-title-content {
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              color: #555556;
            }
          }

          .user-info {
            margin-top: 5px;
            height: 250px;

            .info-img {
              margin-top: 10px;
              text-align: center;

              img {
                width: 70px;
              }
            }

            .user-info-base {
              display: flex;
              flex-direction: column;
              padding-left: 25px;
              padding-right: 5px;

              .user-info-row {
                color: #555556;
                margin-top: 5px;

                .info-lable {
                  color: #a2a2a2;
                }

                .info-value {
                }
              }
            }
          }

          .time-remaining {
            flex: 1;
            margin-top: 5px;

            .time-count {
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 35px;
              color: #1f8d58;
            }
          }
        }

        .content-top-center {
          flex: 1;
          margin-left: 5px;
          display: flex;
          flex-direction: column;

          .exam-topic {
            flex: 1;
            border: 5px solid #a1d2f1;

            .exam-topic-content {
              height: 95%;
              display: flex;
              flex-direction: column;

              .topic-question {
                padding: 0px 10px 0px 10px;
                color: #6c6c6c;
                line-height: 25px;

                div {
                  display: inline;
                }
              }

              .red-font {
                color: #e03e3e;
              }

              .topic-option {
                padding: 10px 10px 0px 10px;
                flex: 1;
                margin-top: 10px;
                color: #6c6c6c;
                line-height: 30px;
              }

              .topic-option-dotted {
                border-top: 2px dotted #eeefef;
              }

              .topic-answer {
                height: 45px;
                padding-left: 10px;
                display: flex;
                align-items: end;

                .topic-answer-title {
                  font-size: 18px;
                }

                .topic-answer-value {
                  font-size: 20px;
                }

                .topic-answer-tip {
                  text-decoration: underline;
                  cursor: pointer;
                  color: #2c7ecf;
                }
              }
            }
          }

          .exam-answer {
            display: flex;
            margin-top: 5px;
            height: 45px;
            background-color: var(--exam-answer-bg);

            .exam-answer-title {
              padding-left: 20px;
              font-size: 18px;
              display: flex;
              align-items: center;
            }

            .exam-answer-value {
              font-size: 22px;
              font-weight: bold;
              display: flex;
              align-items: center;
            }

            .exam-answer-buttons {
              flex: 1;
              display: flex;
              justify-content: flex-end;
              align-items: center;

              .exam-answer-buttons-button {
                font-size: larger;
                color: #1c1c1c;
                width: 40px;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                border: 1px solid #a6a5a5;
                background-color: #ffffff;
                margin-left: 5px;
                border-radius: 3px;
                cursor: pointer;
              }

              .exam-answer-buttons-button:hover {
                background: #24c27d;
                border-color: #14794d;
                color: #fff;
              }

              .exam-answer-buttons-button-active {
                background: #24c27d;
                border-color: #14794d;
                color: #fff;
              }
            }
          }
        }

        .content-top-right {
          width: 397px;
          background-color: #e3e3e3;
          margin: 10px 5px 0px 5px;

          .answer-card {
            display: flex;
            flex-wrap: wrap;

            .answer-card-cell {
              width: 36px;
              height: 36px;
              text-size-adjust: 100%;
              font-weight: bold;
              border-right: 1.5px solid #151515;
              border-bottom: 1.5px solid #151515;
              background-color: #fefeff;
              display: flex;
              justify-content: center;
              align-items: center;
              cursor: pointer;
            }

            .answer-card-cell-head {
              background-color: var(--exam-answer-card-head-bg);
              color: var(--exam-answer-card-head-font);
              font-size: 15px;
            }

            .answer-card-cell-head-top {
              border-top: 1px solid #151515;
            }

            .answer-card-cell-head-left {
              border-left: 1px solid #151515;
            }

            .answer-card-cell-value {
              font-size: 22px;
            }

            .answer-card-cell-value:hover {
              background-color: #2169d3;
            }

            .answer-card-cell-none {
            }

            .answer-card-cell-answered {
              background-color: #eceeed;
            }

            .answer-card-cell-active {
              background-color: #2169d3;
            }

            .answer-card-cell-right {
              color: #2a2929;
            }

            .answer-card-cell-wrong {
              color: red;
            }

            .answer-card-cell-judge {
              font-size: 30px;
            }
            .answer-card-cell-multiple-2 {
              font-size: 20px;
            }

            .answer-card-cell-multiple-3 {
              font-size: 17px;
            }

            .answer-card-cell-multiple-4 {
              font-size: 13px;
            }
          }
        }
      }

      .content-middle {
        height: 60px;
        display: flex;
        margin: 10px 10px 0px 10px;
        align-items: center;
        background-color: var(--exam-action-bg);

        .operation-hint {
          width: 250px;
          padding-left: 10px;

          .operation-hint-title {
            color: #fa2f2f;
          }

          .operation-hint-value {
            color: #464545;
          }
        }

        .skill-button {
          width: 100px;
        }

        .collect-button {
          width: 100px;
        }

        .action-buttons {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          .action-buttons-button {
            font-size: 18px;
            border: 1px solid black;
          }
        }
      }

      .content-bottom {
        flex: 1;
        min-height: 32vh;
        margin: 0px 10px 0px 10px;

        .image-info {
          height: 95%;
          text-align: center;
          .image-info-noimage {
            display: flex;
            justify-content: center;
            align-items: center;
            color: #3182ea;
            font-size: 3rem;
          }
        }
      }
    }
    .e-container-right {
      width: 1px;
      background-color: var(--exam-bg-left_right);
    }
  }
}

.info-block {
  border: 1px solid #c9c8c8;
  margin-top: 10px;
  .info-block-title {
    position: relative;
    top: -1.2vh;
    left: 2.8vw;
    font-size: small;
    background: var(--exam-bg);
    color: #807f7f;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
  }
}

.dialogform {
  .paragraph {
    margin-bottom: 20px;
    font-size: 18px;
    .waring {
      font-size: 25px;
      color: red;
    }
  }
  .paragraph-img {
    text-align: center;
    img {
      max-width: 400px;
      max-height: 400px;
    }
  }
  .divinline {
    display: inline;
  }
  .result-score {
    text-align: center;

    .score {
      font-weight: 700;
      font-size: 50px;
    }

    .fen {
      font-size: 22px;
      font-weight: 700;
    }

    .pass {
      font-weight: 700;
      font-size: 22px;
    }
  }
  .result-data {
    margin-top: 50px;
    margin-bottom: 50px;
    display: flex;
    justify-content: space-around;
    .right {
      font-size: 20px;
      font-weight: 600;
      color: #00c356;
    }

    .wrong {
      font-size: 20px;
      font-weight: 600;
      color: #ff2b3c;
    }

    .un {
      font-size: 20px;
      font-weight: 600;
      color: #0a2433;
    }

    .time {
      font-size: 20px;
      font-weight: 600;
      color: #0a2433;
    }
  }
}
