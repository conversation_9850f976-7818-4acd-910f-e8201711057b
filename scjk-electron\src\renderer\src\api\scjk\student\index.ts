import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { StudentVO, StudentForm, StudentQuery } from '@/api/scjk/student/types';

/**
 * 查询学员信息列表
 * @param query
 * @returns {*}
 */

export const listStudent = (query?: StudentQuery): AxiosPromise<StudentVO[]> => {
  return request({
    url: '/scjk/student/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询学员信息详细
 * @param id
 */
export const getStudent = (id: string | number): AxiosPromise<StudentVO> => {
  return request({
    url: '/scjk/student/' + id,
    method: 'get'
  });
};

/**
 * 根据UserId查询学员详细
 * @param id
 */
export const getStudentInfo = (userId: string | number): AxiosPromise<StudentVO> => {
  return request({
    url: '/scjk/student/getStudent/' + userId,
    method: 'get'
  });
};

/**
 * 新增学员信息
 * @param data
 */
export const addStudent = (data: StudentForm) => {
  return request({
    url: '/scjk/student',
    method: 'post',
    data: data
  });
};

/**
 * 修改学员信息
 * @param data
 */
export const updateStudent = (data: StudentForm) => {
  return request({
    url: '/scjk/student',
    method: 'put',
    data: data
  });
};

/**
 * 删除学员信息
 * @param id
 */
export const delStudent = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/student/' + id,
    method: 'delete'
  });
};
