<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
          <el-form-item label="邮政编码" prop="zipCode">
            <el-input v-model="queryParams.zipCode" placeholder="请输入邮政编码" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="区号" prop="cityCode">
            <el-input v-model="queryParams.cityCode" placeholder="请输入区号" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="queryParams.name" placeholder="请输入名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table v-loading="loading" :data="areaList">
        <el-table-column label="层级" align="center" prop="level" />
        <el-table-column label="父级行政代码" align="center" prop="parentCode" />
        <el-table-column label="行政代码" align="center" prop="areaCode" />
        <el-table-column label="邮政编码" align="center" prop="zipCode" />
        <el-table-column label="区号" align="center" prop="cityCode" />
        <el-table-column label="名称" align="center" prop="name" />
        <el-table-column label="组合名" align="center" prop="mergerName" />
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="Area" lang="ts">
import { listArea } from '@/api/scjk/permit/area';
import { AreaVO, AreaQuery } from '@/api/scjk/permit/area/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const areaList = ref<AreaVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const queryParams = ref<AreaQuery>({
  pageNum: 1,
  pageSize: 10,
  level: undefined,
  parentCode: undefined,
  areaCode: undefined,
  zipCode: undefined,
  cityCode: undefined,
  name: undefined,
  shortName: undefined,
  mergerName: undefined,
  pinyin: undefined,
  lng: undefined,
  lat: undefined,
  params: {}
});

/** 查询行政区划列表 */
const getList = async () => {
  loading.value = true;
  const res = await listArea(queryParams.value);
  areaList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

onMounted(() => {
  getList();
});
</script>
