<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
          <el-form-item label="用户码" prop="userCode">
            <el-input v-model="queryParams.userCode" placeholder="请输入用户码" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['scjk:balanceAssetLog:add']">新增</el-button>-->
          <!--          </el-col>-->
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['scjk:balanceAssetLog:edit']"-->
          <!--              >修改</el-button-->
          <!--            >-->
          <!--          </el-col>-->
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['scjk:balanceAssetLog:remove']"-->
          <!--              >删除</el-button-->
          <!--            >-->
          <!--          </el-col>-->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['scjk:balanceAssetLog:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="balanceAssetLogList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" v-if="false" prop="id" />
        <el-table-column label="用户" align="center" prop="userId" />
        <el-table-column label="变动类型(1:支出;2:收入)" align="center" prop="changesType">
          <template #default="scope">
            <dict-tag :options="change_type" :value="scope.row.changesType" />
          </template>
        </el-table-column>
        <el-table-column label="变动金额" align="center" prop="amount" />
        <el-table-column label="用户码" align="center" prop="userCode" />
        <el-table-column label="邀请人" align="center" prop="createBy" />
        <el-table-column label="变动时间" align="center" prop="createTime" width="180" />
        <!--        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
        <!--          <template #default="scope">-->
        <!--            <el-tooltip content="修改" placement="top">-->
        <!--              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['scjk:balanceAssetLog:edit']"></el-button>-->
        <!--            </el-tooltip>-->
        <!--            <el-tooltip content="删除" placement="top">-->
        <!--              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['scjk:balanceAssetLog:remove']"></el-button>-->
        <!--            </el-tooltip>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改资产变动记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="balanceAssetLogFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户" />
        </el-form-item>
        <el-form-item label="变动金额" prop="amount">
          <el-input v-model="form.amount" placeholder="请输入变动金额" />
        </el-form-item>
        <el-form-item label="邀请人编码" prop="userCode">
          <el-input v-model="form.userCode" placeholder="请输入邀请人编码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BalanceAssetLog" lang="ts">
import { listBalanceAssetLog, getBalanceAssetLog, delBalanceAssetLog, addBalanceAssetLog, updateBalanceAssetLog } from '@/api/scjk/balanceAssetLog';
import { BalanceAssetLogVO, BalanceAssetLogQuery, BalanceAssetLogForm } from '@/api/scjk/balanceAssetLog/types';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { change_type } = toRefs<any>(proxy?.useDict('change_type'));
const balanceAssetLogList = ref<BalanceAssetLogVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const balanceAssetLogFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: BalanceAssetLogForm = {
  id: undefined,
  userId: undefined,
  changesType: undefined,
  amount: undefined,
  userCode: undefined
};
const data = reactive<PageData<BalanceAssetLogForm, BalanceAssetLogQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    changesType: undefined,
    amount: undefined,
    userCode: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    userId: [{ required: true, message: '用户不能为空', trigger: 'blur' }],
    changesType: [{ required: true, message: '变动类型(1:支出;2:收入)不能为空', trigger: 'change' }],
    amount: [{ required: true, message: '变动金额不能为空', trigger: 'blur' }],
    userCode: [{ required: true, message: '邀请人编码不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询资产变动记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listBalanceAssetLog(queryParams.value);
  balanceAssetLogList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  balanceAssetLogFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: BalanceAssetLogVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加资产变动记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: BalanceAssetLogVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getBalanceAssetLog(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改资产变动记录';
};

/** 提交按钮 */
const submitForm = () => {
  balanceAssetLogFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateBalanceAssetLog(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addBalanceAssetLog(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: BalanceAssetLogVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除资产变动记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delBalanceAssetLog(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/balanceAssetLog/export',
    {
      ...queryParams.value
    },
    `balanceAssetLog_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
