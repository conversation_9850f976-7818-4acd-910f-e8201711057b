import { to } from 'await-to-js';
import { getToken, removeToken, setToken } from '@/utils/auth';
import { login as loginApi, logout as logoutApi, getInfo as getUserInfo } from '@/api/login';
import { LoginData } from '@/api/types';
import defAva from '@/assets/images/profile.jpg';
import store from '@/store';

export const useUserStore = defineStore('user', () => {
  const token = ref(getToken());
  const name = ref('');
  const nickname = ref('');
  const sex = ref('');
  const userId = ref<string | number>('');
  const avatar = ref('');
  const idCard = ref(''); // 身份证
  const roleName = ref('');
  const roles = ref<Array<string>>([]); // 用户角色编码集合 → 判断路由权限
  const permissions = ref<Array<string>>([]); // 用户权限编码集合 → 判断按钮权限
  const subscriptions = ref<Array<string>>([]); // 用户订阅信息
  const leaningInfo = ref({}); // 用户学习信息
  const isStudent = ref<boolean>(false); // 是不是学员
  const carTypeName = ref(''); // 当前车型名称
  const carType = ref(''); // 当前车型
  const subject = ref(''); // 当前科目
  const fontSize = ref(16); // 字体大小
  const fontColor = ref('#6c6c6c'); // 字体颜色
  /**
   * 登录
   * @param userInfo
   * @returns
   */
  const login = async (userInfo: LoginData): Promise<void> => {
    const [err, res] = await to(loginApi(userInfo));
    if (res) {
      const data = res.data;
      setToken(data.access_token);
      token.value = data.access_token;
      return Promise.resolve();
    }
    return Promise.reject(err);
  };

  // 获取用户信息
  const getInfo = async (): Promise<void> => {
    const [err, res] = await to(getUserInfo());
    if (res) {
      console.log('用户', res);
      const data = res.data;
      const user = data.user;
      const profile = user.avatar == '' || user.avatar == null ? defAva : user.avatar;

      if (data.roles && data.roles.length > 0) {
        // 验证返回的roles是否是一个非空数组
        roles.value = data.roles;
        permissions.value = data.permissions;
      } else {
        roles.value = ['ROLE_DEFAULT'];
      }

      leaningInfo.value = data.leaningInfo;
      subscriptions.value = data.subscriptions;
      name.value = user.userName;
      nickname.value = user.nickName;
      sex.value = user.sex === '0' ? '男' : user.sex === '1' ? '女' : '未知';
      avatar.value = profile;
      userId.value = user.userId;
      idCard.value = user.idCard;
      roleName.value = user.roles[0].roleName;
      isStudent.value = roleName.value.includes('学员');
      if (isStudent.value === true && leaningInfo.value !== null) {
        carType.value = leaningInfo.value?.carModelId;
        carTypeName.value = leaningInfo.value?.carModel !== null ? leaningInfo.value.carModel : '';
      }

      subject.value = user.subject;
      fontSize.value = user.fontSize;
      const userSettings = JSON.parse(user.userSettings);
      fontColor.value = userSettings?.fontColor;
      return Promise.resolve();
    }
    return Promise.reject(err);
  };

  // 注销
  const logout = async (): Promise<void> => {
    await logoutApi();
    token.value = '';
    roles.value = [];
    permissions.value = [];

    carType.value = '';
    carTypeName.value = '';
    subject.value = '';
    fontSize.value = 16;
    fontColor.value = '#6c6c6c';
    removeToken();
  };

  const setAvatar = (value: string) => {
    avatar.value = value;
  };

  return {
    userId,
    subscriptions,
    leaningInfo,
    token,
    nickname,
    sex,
    avatar,
    idCard,
    roles,
    permissions,
    login,
    getInfo,
    logout,
    setAvatar,
    roleName,
    isStudent,
    carTypeName,
    carType,
    subject,
    fontSize,
    fontColor
  };
});

export default useUserStore;
// 非setup
export function useUserStoreHook() {
  return useUserStore(store);
}
