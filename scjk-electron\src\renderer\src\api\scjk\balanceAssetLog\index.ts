import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { BalanceAssetLogVO, BalanceAssetLogForm, BalanceAssetLogQuery } from '@/api/scjk/balanceAssetLog/types';

/**
 * 查询资产变动记录列表
 * @param query
 * @returns {*}
 */

export const listBalanceAssetLog = (query?: BalanceAssetLogQuery): AxiosPromise<BalanceAssetLogVO[]> => {
  return request({
    url: '/scjk/balanceAssetLog/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询资产变动记录详细
 * @param id
 */
export const getBalanceAssetLog = (id: string | number): AxiosPromise<BalanceAssetLogVO> => {
  return request({
    url: '/scjk/balanceAssetLog/' + id,
    method: 'get'
  });
};

/**
 * 新增资产变动记录
 * @param data
 */
export const addBalanceAssetLog = (data: BalanceAssetLogForm) => {
  return request({
    url: '/scjk/balanceAssetLog',
    method: 'post',
    data: data
  });
};

/**
 * 修改资产变动记录
 * @param data
 */
export const updateBalanceAssetLog = (data: BalanceAssetLogForm) => {
  return request({
    url: '/scjk/balanceAssetLog',
    method: 'put',
    data: data
  });
};

/**
 * 删除资产变动记录
 * @param id
 */
export const delBalanceAssetLog = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/balanceAssetLog/' + id,
    method: 'delete'
  });
};
