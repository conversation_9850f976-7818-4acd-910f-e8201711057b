import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { UserPaperVO, UserPaperRankVO } from '@/api/scjk/paper/userPaper/types';

/**
 * 模拟考试
 * @param carType
 * @param subject
 */
export const mockExam = (carType: string | number, subject: string | number): AxiosPromise<UserPaperVO> => {
  return request({
    url: '/scjk/userPaper/mockExam?carType=' + carType + '&subject=' + subject,
    method: 'get'
  });
};

/**
 * 交卷
 * @param userPaper
 * @returns
 */
export const submitPaper = (userPaper: UserPaperVO) => {
  return request({
    url: '/scjk/userPaper/submitPaper',
    method: 'post',
    data: userPaper
  });
};

/**
 * 刷新token
 * @return
 */
export const fresh = () => {
  return request({
    url: '/scjk/userPaper/fresh',
    method: 'get'
  });
};

/**
 * 排行榜
 * @return
 */
export const rank = (): AxiosPromise<UserPaperRankVO> => {
  return request({
    url: '/scjk/userPaper/rank',
    method: 'get'
  });
};
