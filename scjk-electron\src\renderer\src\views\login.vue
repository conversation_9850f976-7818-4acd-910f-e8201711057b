<template>
  <div class="login-wrap">
    <el-switch
      v-model="isDark"
      class="theme-btn"
      style="margin-left: 24px"
      inline-prompt
      :active-icon="Moon"
      :inactive-icon="Sunny"
      @change="toggleDark"
    />
    背景
    <div class="back-wrap">
      <span style="margin-left: 20px">版本号：{{ version }}</span>
      <div v-if="serialNumber && certificationCode" class="device-info" style="margin-top: 10px">
        <span style="margin-left: 20px">设备编号：{{ serialNumber }}</span>
        <span style="margin-left: 20px">许可证编号：{{ certificationCode }}</span>
      </div>
      <div class="bg-item left one"></div>
      <div class="bg-item right two"></div>
      <div class="bg-item left three"></div>
      <div class="bg-item right four"></div>
    </div>
    <div class="loogin-container">
      <div class="logo">
        <img src="../assets/logo/logo2.png" alt="logo" />
      </div>
      <div class="login-title">速诚驾考教学管理系统</div>
      <div class="flex gap-4 mb-4">
        <el-form ref="loginRef" :model="loginForm" class="form-wrap" :rules="loginRules" autocomplete="off">
          <el-form-item v-if="tenantEnabled" prop="tenantId">
            <el-select v-model="loginForm.tenantId" filterable placeholder="请选择/输入公司名称" style="width: 100%">
              <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId"></el-option>
              <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
            </el-select>
          </el-form-item>
          <el-form-item prop="username">
            <el-input v-model="loginForm.username" class="login-input" placeholder="请输入账号或手机号" :prefix-icon="User"></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              class="login-input"
              placeholder="请输入密码"
              show-password
              :prefix-icon="Lock"
              @keyup.enter="handleLogin"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="captchaEnabled" prop="code">
            <el-input
              v-model="loginForm.code"
              class="login-input"
              size="large"
              auto-complete="off"
              placeholder="验证码"
              style="width: 63%"
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <svg-icon icon-class="validCode" class="el-input__icon input-icon" />
              </template>
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" class="login-code-img" alt="验证码" @click="getCode" />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <!-- 忘记密码 -->
      <div class="other-wrap">
        <el-button :loading="loading" size="large" type="primary" style="width: 100%" @click.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </div>
      <div v-if="register" style="float: right">
        <router-link class="link-type" :to="'/register'">立即注册</router-link>
      </div>
    </div>
    <div class="el-login-footer">
      <!-- <span>Copyright © 2018-2024 疯狂的狮子Li All Rights Reserved.</span> -->
    </div>
  </div>
</template>
<script setup lang="ts">
import { getCodeImg, getTenantList, getDeviceInfo } from '@/api/login';
import { authBinding } from '@/api/system/social/auth';
import { useUserStore } from '@/store/modules/user';
import { LoginData, TenantVO } from '@/api/types';
import { to } from 'await-to-js';
import { HttpStatus } from '@/enums/RespEnum';
import { User, Lock, Sunny, Moon } from '@element-plus/icons-vue';
import { useDark, useToggle } from '@vueuse/core';
import Fingerprint2 from 'fingerprintjs2';
import { version } from '../../../../package.json';
// 暗黑主题切换
const isDark = useDark();
const toggleDark = useToggle(isDark);
const serialNumber = ref('');
const certificationCode = ref('');
const userStore = useUserStore();
const router = useRouter();
const fingerPrint = ref('');
const macAddr = ref('');

const loginForm = ref<LoginData>({
  tenantId: '000000',
  username: '',
  password: '',
  fingerPrint: fingerPrint.value,
  macAddress: macAddr.value,
  rememberMe: false,
  code: '',
  uuid: ''
} as LoginData);
const loginRules: ElFormRules = {
  tenantId: [{ required: true, trigger: 'blur', message: '请输入您的租户编号' }],
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
};

const codeUrl = ref('');
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(false);
// 租户开关
const tenantEnabled = ref(true);

// 注册开关
const register = ref(false);
const redirect = ref(undefined);
const loginRef = ref<ElFormInstance>();
// 租户列表
const tenantList = ref<TenantVO[]>([]);

watch(
  () => router.currentRoute.value,
  (newRoute: any) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
  },
  { immediate: true }
);

const handleLogin = () => {
  loginRef.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      loading.value = true;
      loginForm.value.fingerPrint = fingerPrint.value;
      loginForm.value.macAddress = macAddr.value;
      // 勾选了需要记住密码设置在 localStorage 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        localStorage.setItem('tenantId', String(loginForm.value.tenantId));
        localStorage.setItem('username', String(loginForm.value.username));
        localStorage.setItem('password', String(loginForm.value.password));
        localStorage.setItem('rememberMe', String(loginForm.value.rememberMe));
      } else {
        // 否则移除
        localStorage.removeItem('tenantId');
        localStorage.removeItem('username');
        localStorage.removeItem('password');
        localStorage.removeItem('rememberMe');
      }
      // 调用action的登录方法
      const [err] = await to(userStore.login(loginForm.value));
      if (!err) {
        const redirectUrl = redirect.value || '/';
        await router.push(redirectUrl);
        loading.value = false;
      } else {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          await getCode();
        }
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};

/**
 * 获取验证码
 */
const getCode = async () => {
  const res = await getCodeImg();
  const { data } = res;
  captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled;
  if (captchaEnabled.value) {
    codeUrl.value = 'data:image/gif;base64,' + data.img;
    loginForm.value.uuid = data.uuid;
  }
};

const getLoginData = () => {
  const tenantId = localStorage.getItem('tenantId');
  const username = localStorage.getItem('username');
  const password = localStorage.getItem('password');
  const rememberMe = localStorage.getItem('rememberMe');
  loginForm.value = {
    tenantId: tenantId === null ? String(loginForm.value.tenantId) : tenantId,
    username: username === null ? String(loginForm.value.username) : username,
    password: password === null ? String(loginForm.value.password) : String(password),
    rememberMe: rememberMe === null ? false : Boolean(rememberMe)
  } as LoginData;
};

/**
 * 获取租户列表
 */
const initTenantList = async () => {
  const { data } = await getTenantList();
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
  if (tenantEnabled.value) {
    tenantList.value = data.voList;
    if (tenantList.value != null && tenantList.value.length !== 0) {
      loginForm.value.tenantId = tenantList.value[0].tenantId;
    }
  }
};

//检测租户选择框的变化
watch(
  () => loginForm.value.tenantId,
  () => {
    localStorage.setItem('tenantId', String(loginForm.value.tenantId));
  }
);

/**
 * 第三方登录
 * @param type
 */
const doSocialLogin = (type: string) => {
  authBinding(type).then((res: any) => {
    if (res.code === HttpStatus.SUCCESS) {
      // 获取授权地址跳转
      window.location.href = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};
const getFingerprint = () => {
  if (window.electron) {
    let macAddress = window.electron.ipcRenderer.sendSync('getMacAdress');
    if (macAddress !== 'undefined' || macAddress !== 'null' || macAddress !== '') {
      macAddr.value = macAddress.toUpperCase();
      getDeviceInfo(macAddr.value).then((res) => {
        const data = res.data;
        if (data) {
          serialNumber.value = data.serialNum;
          certificationCode.value = data.certificateCode;
        }
      });
    }
  }
  requestIdleCallback(function () {
    Fingerprint2.get(function (components) {
      fingerPrint.value = Fingerprint2.x64hash128(components.map((component: any) => component.value).join(), 31);
    });
  });
};

onMounted(() => {
  getCode();
  initTenantList();
  getLoginData();
  getFingerprint();
});
</script>
<style>
input:-internal-autofill-selected {
  background-color: transparent !important;
  background-image: none !important;
  color: rgb(255, 255, 255) !important;
}
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transition-delay: 500000s;
  transition: background-color 50000s ease-out;
  -webkit-transition-delay: 50000s;
  -webkit-transition: background-color 50000s ease-out;
}
</style>
<style scoped lang="scss">
.login-wrap {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  overflow: hidden;
  background-color: var(--el-color-primary-light-5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-btn {
  position: absolute;
  right: 100px;
  top: 50px;
  z-index: 9;
}

// 背景
.back-wrap {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 1;
  background: var(--el-color-primary-light-9);

  .bg-item {
    position: absolute;

    &.left {
      bottom: 0;
      left: 0;
      filter: drop-shadow(5px 0 20px rgba(0, 0, 0, 0.1));
    }

    &.right {
      bottom: 0;
      right: 0;
      filter: drop-shadow(-5px 0 20px rgba(0, 0, 0, 0.2));
    }

    &.one {
      border-bottom: 50vh solid var(--el-color-primary-light-3);
      border-right: 60vw solid transparent;
      z-index: 6;
    }

    &.two {
      border-bottom: 70vh solid var(--el-color-primary-light-5);
      border-left: 80vw solid transparent;
      z-index: 5;
    }

    &.three {
      border-bottom: 90vh solid var(--el-color-primary-light-7);
      border-right: 90vw solid transparent;
      z-index: 4;
    }

    &.four {
      border-bottom: 110vh solid var(--el-color-primary-light-8);
      border-left: 110vw solid transparent;
      z-index: 3;
    }
  }
}

.loogin-container {
  padding: 20px;
  position: relative;
  z-index: 2;
}

.login-title {
  color: var(--color-primary);
  font-size: 48px;
}
.form-wrap {
  width: 400px;
  margin: 0 auto;
  margin-top: 5vh;
  color: var(--color-text-2);
  :deep(.el-input__wrapper) {
    border-radius: 5px;
    border: 1px solid var(--el-color-primary-light-3);
    outline: none;
    box-shadow: none;
    &.is-focus,
    &:hover {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }

  :deep(.el-input__prefix) {
    color: var(--color-text-2);
    //font-size: large;
  }

  :deep(.el-input__inner) {
    //color: var(--color-primary);
    //outline: none;
    height: 40px;
  }
  //:deep(.el-input__inner::placeholder) {
  //  color: var(--color-primary);
  //}
}

.other-wrap {
  width: 400px;
  margin: 0 auto;
  margin-top: 10px;
  text-align: right;
}

.login-btn {
  width: 400px;
  height: 40px;
  // font-size: 16px;
  display: block;
  margin: 20px auto;
}
.logo {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10vh;
  img {
    width: 280px;
    height: 280px;
  }
}
:deep(.el-checkbox__inner) {
  border-color: var(--color-text-2);
}
.rememberpwd {
  color: var(--color-primary);
}
</style>
