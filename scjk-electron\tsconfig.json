{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    //    "useDefineForClassFields": true,
    "moduleResolution": "bundler",
    "strict": true,
    "jsx": "preserve",
    "strictNullChecks": false,
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "strictFunctionTypes": false,
    "lib": ["esnext", "dom"],
    "noImplicitAny": false,
    "baseUrl": "",
    "allowJs": true,
    "experimentalDecorators": true,
    "paths": {
      "@/*": ["src/renderer/src/*", "src/renderer/src/components/*"]
    },
    "types": ["vite/client"],
    "skipLibCheck": true,
    "removeComments": true,
    // 允许默认导入
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true
  },
  // "include": ["src/renderer/**/*.ts", "src/renderer/src/**/*.vue", "src/renderer/types/**/*.d.ts", "vite.config.ts","src/renderer/src/env.d.ts",
  // "src/renderer/src/**/*",
  // "src/renderer/src/**/*.vue",
  // "src/preload/*.d.ts","electron.vite.config.*", "src/main/**/*", "src/preload/**/*"],
  // "exclude": ["node_modules", "dist", "**/*.js", "**/*.md", "src/renderer/**/*.md"],
  "files": [],
  "references": [{ "path": "./tsconfig.node.json" }, { "path": "./tsconfig.web.json" }]
}
