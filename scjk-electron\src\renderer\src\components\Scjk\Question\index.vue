<template>
  <el-dialog :title="title" :model-value="visible" :width="`${width}px`" append-to-body>
    <div class="p-2">
      <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
        <div v-show="showSearch" class="search">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
            <el-form-item label="ID" prop="id">
              <el-input v-model="queryParams.id" placeholder="请输入ID" clearable style="width: 240px" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item v-if="!limit" label="车型" prop="carType">
              <car-type v-model="queryParams.carType" clearable placeholder="请选择车型" @keyup.enter="handleQuery"></car-type>
            </el-form-item>
            <el-form-item v-if="!limit" label="科目" prop="subject">
              <subject v-model="queryParams.subject" clearable placeholder="请选择科目" @keyup.enter="handleQuery"></subject>
            </el-form-item>
            <el-form-item label="章节" prop="chapter">
              <chapter v-model="queryParams.chapter" clearable placeholder="请选择章节" @keyup.enter="handleQuery"></chapter>
            </el-form-item>
            <el-form-item label="专题" prop="special">
              <special v-model="queryParams.special" clearable placeholder="请选择专题" @keyup.enter="handleQuery"></special>
            </el-form-item>
            <el-form-item label="题目" prop="question">
              <el-input v-model="queryParams.question" placeholder="请输入题目" clearable style="width: 240px" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="题型" prop="type">
              <el-select v-model="queryParams.type" style="width: 240px" placeholder="请选择题型" clearable>
                <el-option v-for="dict in question_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="答案" prop="answer">
              <el-input v-model="queryParams.answer" placeholder="请输入答案" clearable style="width: 240px" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </transition>

      <el-card shadow="never">
        <template #header>
          <el-row :gutter="10" class="mb8">
            <el-col :span="21">
              <span
                >已选择: <span style="color: red">{{ selectedQuestionList.length }}</span></span
              >
              <el-button type="danger" style="margin-left: 10px" @click="emptySelection">清空</el-button>
            </el-col>
            <el-col :span="3">
              <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
            </el-col>
          </el-row>
        </template>
        <el-table ref="questionTableRef" v-loading="loading" :data="questionList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="车型" align="center" prop="carType" fixed />
          <el-table-column label="科目" align="center" prop="subject" fixed />
          <el-table-column label="章节" align="center" prop="chapter" />
          <el-table-column label="题型" align="center" prop="type">
            <template #default="scope">
              <dict-tag :options="question_type" :value="scope.row.type" />
            </template>
          </el-table-column>
          <el-table-column label="题目" align="center" prop="question" width="300">
            <template #default="scope">
              <editor v-model="scope.row.question" :read-only="true" :min-height="50" :height="70"></editor>
            </template>
          </el-table-column>
          <el-table-column label="图片" align="center" prop="image" width="300">
            <template #default="scope">
              <ImagePreview v-if="scope.row.image" :width="50" :height="50" :src="scope.row.image" :preview-src="[scope.row.image]" />
            </template>
          </el-table-column>
          <el-table-column label="答案" align="center" prop="answerFormat" width="300" />
          <el-table-column label="试卷" align="center" prop="papers" width="300">
            <template #default="scope">
              <div style="text-align: left">{{ scope.row.papers }}</div>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="confirm"> 确定 </el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="Question" lang="ts">
import { listQuestion } from '@/api/scjk/questionbank/question';
import { QuestionVO, QuestionQuery } from '@/api/scjk/questionbank/question/types';
import CarType from '@/components/Scjk/CarType/index.vue';
import Subject from '@/components/Scjk/Subject/index.vue';
import Chapter from '@/components/Scjk/Chapter/index.vue';
import Special from '@/components/Scjk/Special/index.vue';
import Editor from '@/components/Scjk/Editor/index.vue';
import ImagePreview from '@/components/Scjk/ImagePreview/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { question_type } = toRefs<any>(proxy?.useDict('question_type'));

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  limit: {
    type: Boolean,
    default: false
  },
  carType: {
    type: String,
    default: ''
  },
  subject: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: '选择题目'
  },
  width: {
    type: Number,
    default: 800
  }
});

const emit = defineEmits(['update:value', 'update:visible', 'selected']);

const questionList = ref<QuestionVO[]>([]);
const loading = ref(true);
const showSearch = ref(false);
const ids = ref<Array<string | number>>([]);
const selectedQuestionList = ref<QuestionVO[]>([]);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const queryParams = ref<QuestionQuery>({
  pageNum: 1,
  pageSize: 10,
  id: undefined,
  carType: undefined,
  subject: undefined,
  chapter: undefined,
  special: undefined,
  question: undefined,
  answer: undefined,
  type: undefined,
  params: {}
});
const questionTableRef = ref<ElTableInstance>();

const isShow = computed(() => props.visible);

/** 查询题库列表 */
const getList = async () => {
  loading.value = true;
  if (props.limit) {
    queryParams.value.carType = props.carType;
    queryParams.value.subject = props.subject;
  }

  const res = await listQuestion(queryParams.value);
  console.log(res);
  questionList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: QuestionVO[]) => {
  // 保留之前记录，并去重
  ids.value = ids.value.concat(selection.map((item) => item.id));
  selectedQuestionList.value = selectedQuestionList.value.concat(selection);
};

const emptySelection = () => {
  ids.value = [];
  selectedQuestionList.value = [];
  questionTableRef.value!.clearSelection();
};

/**
 * 确认选择
 */
const confirm = () => {
  emit('update:value', selectedQuestionList.value);
  emit('update:visible', false);
  emit('selected', selectedQuestionList.value);
  reset();
};

/**
 * 取消
 */
const cancel = () => {
  emit('update:visible', false);
  reset();
};

/**
 * 重置
 */
const reset = () => {
  questionList.value = [];
  total.value = 0;
  loading.value = false;
  showSearch.value = false;
  ids.value = [];
  selectedQuestionList.value = [];
  questionTableRef.value!.clearSelection();
};

watch(isShow, () => {
  if (isShow.value) {
    getList();
  }
});
</script>
<style lang="scss" scoped>
.pagination-container {
  padding: 32px 16px;
  .el-pagination {
    float: v-bind(float);
  }
}
.pagination-container.hidden {
  display: none;
}
</style>
