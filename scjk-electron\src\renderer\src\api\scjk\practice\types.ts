import { Option } from '@/api/scjk/questionbank/question/types';

export interface PracticeInfoVO {
  /**
   * 头像
   */
  avatar: string;
  /**
   * 昵称
   */
  nickname: string;
  /**
   * 性别
   */
  sex: string;
  // 试卷名称
  paperName?: string;
  /**
   * 车型
   */
  carTypeName?: string;
  /**
   * 科目
   */
  subjectName?: string;
  /**
   * 满分
   */
  score?: number;
  /**
   * 及格
   */
  pass?: number;
  /**
   * 时间
   */
  time?: number;
  /**
   * 用时
   */
  spendTime?: number;
}

export interface PracticeQuestionVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 试卷
   */
  paper: string | number;

  /**
   * 题目
   */
  question: string | number;

  /**
   * 分数
   */
  val: number;

  /**
   * 得分
   */
  score?: number;

  /**
   * 顺序
   */
  sort: number;

  /**
   * 题目
   */
  title?: string;

  /**
   * 图片
   */
  image?: string;

  /**
   * 题型
   */
  type?: string;

  /**
   * 解析
   */
  analysis?: string;

  /**
   * 选项
   */
  options?: string | Array<Option>;

  /**
   * 选项
   */
  optionList?: Array<Option>;

  /**
   * 答案
   */
  answer?: string | Array<string>;

  /**
   * 正确答案
   */
  correct?: string;

  /**
   * 已切换
   */
  switched: boolean;

  /**
   * 技巧
   */
  skill?: string;
}
