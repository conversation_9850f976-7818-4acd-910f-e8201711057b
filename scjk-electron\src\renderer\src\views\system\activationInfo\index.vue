<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="设备编号" prop="serialNum">
              <el-input v-model="queryParams.serialNum" placeholder="请输入设备编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="绑定设备" prop="bindingDevice">
              <el-input v-model="queryParams.bindingDevice" placeholder="请输入绑定设备" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="授权编号" prop="certificateCode">
              <el-input v-model="queryParams.certificateCode" placeholder="请输入授权编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备指纹" prop="fingerPrint">
              <el-input v-model="queryParams.fingerPrint" placeholder="请输入绑定MAC地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="IP地址" prop="bindingIpAddr">
              <el-input v-model="queryParams.bindingIpAddr" placeholder="请输入绑定IP地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:activationInfo:add']">新增</el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:activationInfo:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:activationInfo:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:activationInfo:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="activationInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="true" label="设备编号" align="center" prop="serialNum" />
        <el-table-column label="绑定设备" align="center" prop="bindingDevice" />
        <el-table-column label="授权编号" align="center" prop="certificateCode" />
        <el-table-column label="设备指纹" align="center" prop="fingerPrint" />
        <el-table-column label="IP地址" align="center" prop="bindingIpAddr" />
        <el-table-column label="有效期" align="center" prop="period" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.period, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="激活状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <!--        <el-table-column label="授权人" align="center" prop="activatorId" />-->
        <el-table-column label="授权人账号" align="center" prop="activatorAccount" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['system:activationInfo:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['system:activationInfo:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改激活信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" append-to-body>
      <el-form ref="activationInfoFormRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="绑定设备" prop="bindingDevice">
          <el-input v-model="form.bindingDevice" placeholder="请输入绑定设备" />
        </el-form-item>
        <el-form-item label="授权编号" prop="certificateCode">
          <el-input v-model="form.certificateCode" placeholder="请输入授权编号" disabled />
        </el-form-item>
        <el-form-item label="绑定MAC地址" prop="bindingMacAddr">
          <el-input v-model="form.bindingMacAddr" placeholder="请输入绑定MAC地址" disabled />
        </el-form-item>
        <el-form-item label="绑定IP地址" prop="bindingIpAddr">
          <el-input v-model="form.bindingIpAddr" placeholder="请输入绑定IP地址" disabled />
        </el-form-item>
        <el-form-item label="有效期" prop="period">
          <el-date-picker v-model="form.period" clearable type="date" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择有效期"> </el-date-picker>
        </el-form-item>
        <el-form-item label="激活人id" prop="activatorId">
          <el-input v-model="form.activatorId" placeholder="请输入激活人id" disabled />
        </el-form-item>
        <el-form-item label="激活人账号" prop="activatorAccount">
          <el-input v-model="form.activatorAccount" placeholder="请输入激活人账号" disabled />
        </el-form-item>
        <!--        <el-form-item label="激活状态" prop="status">-->
        <!--          <el-radio-group v-model="form.status">-->
        <!--            <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>-->
        <!--          </el-radio-group>-->
        <!--        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ActivationInfo" lang="ts">
import { listActivationInfo, getActivationInfo, delActivationInfo, addActivationInfo, updateActivationInfo } from '@/api/system/activationInfo';
import { ActivationInfoVO, ActivationInfoQuery, ActivationInfoForm } from '@/api/system/activationInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const activationInfoList = ref<ActivationInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const activationInfoFormRef = ref<ElFormInstance>();
const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'));

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ActivationInfoForm = {
  serialNum: undefined,
  bindingDevice: undefined,
  certificateCode: undefined,
  bindingMacAddr: undefined,
  bindingIpAddr: undefined,
  period: undefined,
  status: undefined,
  activatorId: undefined,
  activatorAccount: undefined
};
const data = reactive<PageData<ActivationInfoForm, ActivationInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bindingDevice: undefined,
    certificateCode: undefined,
    bindingMacAddr: undefined,
    bindingIpAddr: undefined,
    period: undefined,
    status: undefined,
    activatorId: undefined,
    activatorAccount: undefined,
    params: {}
  },
  rules: {
    serialNum: [{ required: true, message: '授权序列号不能为空', trigger: 'blur' }],
    bindingDevice: [{ required: true, message: '绑定设备不能为空', trigger: 'blur' }],
    certificateCode: [{ required: true, message: '授权编号不能为空', trigger: 'blur' }],
    bindingMacAddr: [{ required: true, message: '绑定MAC地址不能为空', trigger: 'blur' }],
    bindingIpAddr: [{ required: true, message: '绑定IP地址不能为空', trigger: 'blur' }],
    period: [{ required: true, message: '有效期不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '激活状态不能为空', trigger: 'change' }],
    activatorId: [{ required: true, message: '激活人id不能为空', trigger: 'blur' }],
    activatorAccount: [{ required: true, message: '激活人账号不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询激活信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listActivationInfo(queryParams.value);
  activationInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  activationInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ActivationInfoVO[]) => {
  ids.value = selection.map((item) => item.serialNum);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
// const handleAdd = () => {
//   reset();
//   dialog.visible = true;
//   dialog.title = '添加激活信息';
// };

/** 修改按钮操作 */
const handleUpdate = async (row?: ActivationInfoVO) => {
  reset();
  const _serialNum = row?.serialNum || ids.value[0];
  const res = await getActivationInfo(_serialNum);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改激活信息';
};

/** 提交按钮 */
const submitForm = () => {
  activationInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.serialNum) {
        await updateActivationInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addActivationInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ActivationInfoVO) => {
  const _serialNums = row?.serialNum || ids.value;
  await proxy?.$modal.confirm('是否确认删除激活信息编号为"' + _serialNums + '"的数据项？').finally(() => (loading.value = false));
  await delActivationInfo(_serialNums);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/activationInfo/export',
    {
      ...queryParams.value
    },
    `activationInfo_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
