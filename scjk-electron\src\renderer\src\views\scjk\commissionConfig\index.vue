<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search" v-show="showSearch">
        <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
          <el-form-item label="名称" prop="name">
            <el-input v-model="queryParams.name" placeholder="请输入名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="一级分佣比例" prop="firstLevelCommissionRatio">
            <el-input
              v-model="queryParams.firstLevelCommissionRatio"
              placeholder="请输入一级分佣比例"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <!-- <el-form-item label="二级分佣比例" prop="secondLevelCommissionRatio">
            <el-input
              v-model="queryParams.secondLevelCommissionRatio"
              placeholder="请输入二级分佣比例"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['scjk:commissionConfig:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['scjk:commissionConfig:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['scjk:commissionConfig:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['scjk:commissionConfig:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="commissionConfigList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" prop="id" v-if="false" />
        <el-table-column label="名称" align="center" prop="name" />
        <el-table-column label="分佣产品" align="center" prop="planName" />
        <el-table-column label="一级分佣比例" align="center" prop="firstLevelCommissionRatio" />
        <el-table-column label="一级分佣角色" align="center" prop="roleName" />
        <!-- <el-table-column label="二级分佣比例" align="center" prop="secondLevelCommissionRatio" /> -->
        <!-- <el-table-column label="二级分佣角色" align="center" prop="secondLevelCommissionRole" /> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['scjk:commissionConfig:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['scjk:commissionConfig:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改分佣配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="commissionConfigFormRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="分佣产品" prop="subscriptionPlanId">
          <el-select v-model="form.subscriptionPlanId" placeholder="请选择分佣产品">
            <el-option v-for="item in subscriptionOptions" :key="item.id" :label="item.plan" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="11">
            <el-form-item label="一级分佣角色" prop="firstLevelCommissionRole">
              <el-select v-model="form.firstLevelCommissionRole" placeholder="请选择分佣产品">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleKey"
                  :label="item.roleName"
                  :value="item.roleKey"
                  :disabled="item.status == '1'"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="分佣比例" prop="firstLevelCommissionRatio">
              <el-input-number type="number" v-model="form.firstLevelCommissionRatio" max="100" min="0" placeholder="分佣比例">
                <template #append>%</template></el-input-number
              >
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="11">
            <el-form-item label="一级分佣角色" prop="secondLevelCommissionRole">
              <el-select v-model="form.secondLevelCommissionRole" placeholder="请选择分佣产品">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleKey"
                  :label="item.roleName"
                  :value="item.roleKey"
                  :disabled="item.status == '1'"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="分佣比例" prop="secondLevelCommissionRatio">
              <el-input-number type="number" v-model="form.secondLevelCommissionRatio" max="100" min="0" placeholder="分佣比例">
                <template #append>%</template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CommissionConfig" lang="ts">
import {
  listCommissionConfig,
  getCommissionConfig,
  delCommissionConfig,
  addCommissionConfig,
  updateCommissionConfig
} from '@/api/scjk/commissionConfig';
import { CommissionConfigVO, CommissionConfigQuery, CommissionConfigForm } from '@/api/scjk/commissionConfig/types';
import { listSubscriptionPlan } from '@/api/scjk/subscriptionPlan';
import { SubscriptionPlanVO } from '@/api/scjk/subscriptionPlan/types';
import { listCommonRole, listRole } from '@/api/system/role';
import { RoleVO } from '@/api/system/role/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const commissionConfigList = ref<CommissionConfigVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const commissionConfigFormRef = ref<ElFormInstance>();
const subscriptionOptions = ref<SubscriptionPlanVO[]>([]);
const roleOptions = ref<RoleVO[]>([]);
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CommissionConfigForm = {
  id: undefined,
  name: undefined,
  subscriptionPlanId: undefined,
  firstLevelCommissionRatio: undefined,
  firstLevelCommissionRole: undefined,
  secondLevelCommissionRatio: undefined,
  secondLevelCommissionRole: undefined
};
const data = reactive<PageData<CommissionConfigForm, CommissionConfigQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    subscriptionPlanId: undefined,
    firstLevelCommissionRatio: undefined,
    firstLevelCommissionRole: undefined,
    secondLevelCommissionRatio: undefined,
    secondLevelCommissionRole: undefined,
    params: {}
  },
  rules: {
    name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
    subscriptionPlanId: [{ required: true, message: '分佣产品不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询分佣配置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCommissionConfig(queryParams.value);
  commissionConfigList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  commissionConfigFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: CommissionConfigVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加分佣配置';
  getSubscriptionPlan();
  getRole();
};

/** 修改按钮操作 */
const handleUpdate = async (row?: CommissionConfigVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getCommissionConfig(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改分佣配置';
  getSubscriptionPlan();
  getRole();
};

/** 提交按钮 */
const submitForm = () => {
  commissionConfigFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateCommissionConfig(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addCommissionConfig(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CommissionConfigVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除分佣配置编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delCommissionConfig(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/commissionConfig/export',
    {
      ...queryParams.value
    },
    `commissionConfig_${new Date().getTime()}.xlsx`
  );
};
/** 订阅按钮 */
const getSubscriptionPlan = async () => {
  const res = await listSubscriptionPlan();
  subscriptionOptions.value = res.rows;
  if (subscriptionOptions.value.length === 0) {
    ElMessageBox.alert('暂无套餐，请联系管理员');
    return false;
  }
  return true;
};

const getRole = async () => {
  const res = await listCommonRole();
  roleOptions.value = res;
};

/** 页面加载完成 */
onMounted(() => {
  getList();
});
</script>
