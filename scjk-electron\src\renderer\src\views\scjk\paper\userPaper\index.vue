<template>
  <div class="container">
    <div v-if="isStudent === false">
      <el-row :gutter="20">
        <el-col :span="6"> 车型： <car-type v-model="carType" :clearable="false" placeholder="请选择车型"></car-type> </el-col>
        <el-col :span="12"></el-col>
      </el-row>

      <el-divider />
    </div>
    <el-tabs v-model="subject">
      <el-tab-pane v-for="(item, index) of learnSubjects" :key="index" :label="item.name" :name="item.id">
        <el-button class="mock-button" @click="mockExam">模拟考试</el-button>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="UserPaper" lang="ts">
import useUserStore from '@/store/modules/user';
import useSubjectStore from '@/store/modules/subject';
import CarType from '@/components/Scjk/CarType/index.vue';

const userStore = useUserStore();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 科目列表
const subjects = computed(() => {
  return useSubjectStore().list;
});
// 报考的科目
const learnSubjects = ref([]);
// 默认科目
const defaultSubject = useSubjectStore().list ? useSubjectStore().list[0].id : '';
// 当前科目
const subject = ref<string | number>(defaultSubject);
// 默认车型
const carType = ref<string | number>('1784750369061953537');
// 是否是学生(默认是)
const isStudent = computed(() => {
  return userStore.roleName === '学员';
});

const mockExam = () => {
  // 跳转到模拟考试页面
  proxy?.$tab.closePage(proxy?.$route);

  if (carType.value === undefined || carType.value === '') {
    proxy.$modal.msgError('未找到您报考的车型，请联系管理员！');
    return;
  }
  if (subject.value === undefined || subject.value === '') {
    carType.value = '';
    proxy.$modal.msgError('未找到您报考的科目，请联系管理员！');
    return;
  }

  proxy?.$router.push({ path: '/exam/userPaperExam', query: { carType: carType.value, subject: subject.value } });
};

onMounted(() => {
  learnSubjects.value = subjects.value.map((item) => item);
  // 学员
  if (isStudent.value === true) {
    let carModelId = userStore.leaningInfo?.carModelId;
    if (carModelId === undefined || carModelId === null || carModelId === '') {
      carType.value = '';
      proxy.$modal.msgError('未找到您报考的车型，请联系管理员！');
      return;
    }
    carType.value = carModelId;

    let studentSubjectStr = userStore.leaningInfo?.subject;
    if (studentSubjectStr === undefined || studentSubjectStr === null || studentSubjectStr === '') {
      subject.value = '';
      learnSubjects.value = [];
      proxy.$modal.msgError('未找到您报考的科目，请联系管理员！');
      return;
    }

    let studentSubjects = studentSubjectStr.split(',');
    learnSubjects.value = subjects.value.filter((item) => {
      return studentSubjects.includes(String(item.id));
    });
    if (learnSubjects.value.length < 1) {
      subject.value = '';
      proxy.$modal.msgError('未找到您报考的科目，请联系管理员！');
      return;
    }
    subject.value = learnSubjects.value[0].id;
  }
});
</script>
<style>
.container {
  padding: 30px;
  height: 100%;
}

.mock-button {
  float: left;
  width: 449px;
  height: 90px;
  margin: 20px 20px 0 0;
  background: url('../../../../assets/scjk/btn-default.png') no-repeat 0 0;
  background-size: 100% 100%;
  position: relative;
  transition: all ease 0.4s;
  border-radius: 20px;
  color: white;
  font-size: 28px;
}
</style>
