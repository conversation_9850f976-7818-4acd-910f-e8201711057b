import { app, shell, BrowserWindow, ipcMain } from 'electron';
import { join } from 'path';
import { electronApp, optimizer, is } from '@electron-toolkit/utils';
import icon from '../../resources/icon.png?asset';
import { getMacAddress } from './getMacAddress';

const { autoUpdater } = require('electron-updater');

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1024,
    height: 768,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  });
  mainWindow.maximize();
  mainWindow.on('ready-to-show', () => {
    mainWindow.show();
  });

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url);
    return { action: 'deny' };
  });

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL']);
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'));
  }
  checkUpdate(mainWindow, ipcMain);
}

const checkUpdate = (mainWindow, ipcMain) => {
  autoUpdater.forceDevUpdateConfig = true;
  autoUpdater.autoDownload = true; // 自动下载
  autoUpdater.autoInstallOnAppQuit = true; // 应用退出后自动安装
  // 检测是否有更新包并通知
  // autoUpdater.checkForUpdatesAndNotify().catch();
  autoUpdater.on('update-available', (info) => {
    console.log('有更新包');
    mainWindow.webContents.send('updateAvailable');
  });
  autoUpdater.on('update-not-available', (info) => {
    console.log('无需更新');
  });
  autoUpdater.on('download-progress', (prog) => {
    mainWindow.webContents.send('update', {
      speed: Math.ceil(prog.bytesPerSecond / 1000), // 网速
      percent: Math.ceil(prog.percent) // 百分比
    });
  });

  autoUpdater.on('update-downloaded', (info) => {
    mainWindow.webContents.send('downloaded');
    // 下载完成后强制用户安装，不推荐
    //autoUpdater.quitAndInstall();
  });

  // 监听渲染进程的 install 事件，触发退出应用并安装
  ipcMain.handle('install', () => autoUpdater.quitAndInstall());
};
// 解决GPU和编码问题的启动参数
app.commandLine.appendSwitch('--disable-gpu-sandbox');
app.commandLine.appendSwitch('--disable-software-rasterizer');
app.commandLine.appendSwitch('--disable-gpu');
app.commandLine.appendSwitch('--no-sandbox');
// 在现有的启动参数基础上添加
app.commandLine.appendSwitch('--disable-dev-shm-usage');
app.commandLine.appendSwitch('--disable-extensions');
app.commandLine.appendSwitch('--disable-background-timer-throttling');
app.commandLine.appendSwitch('--disable-backgrounding-occluded-windows');
app.commandLine.appendSwitch('--disable-renderer-backgrounding');
// 解决中文乱码问题
app.commandLine.appendSwitch('--lang', 'zh-CN');

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('cn.yntdrj.scjk');

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window);
  });
  ipcMain.on('update-downloaded', () => {
    autoUpdater.on('update-downloaded', (info) => {
      console.log(2222);
      // 下载完成后强制用户安装，不推荐
      //autoUpdater.quitAndInstall();
    });
  });
  ipcMain.on('app.quit', () => {
    app.quit;
  });

  ipcMain.on('getMacAdress', async (event) => {
    event.returnValue = await getMacAddress();
  });
  createWindow();
  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// In this file you can include the rest of your app"s specific main process
// code. You can also put them in separate files and require them here.
