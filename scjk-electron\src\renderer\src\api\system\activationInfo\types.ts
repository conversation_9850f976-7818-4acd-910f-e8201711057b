export interface ActivationInfoVO {
  /**
   * 授权序列号
   */
  serialNum: number;

  /**
   * 绑定设备
   */
  bindingDevice: string;

  /**
   * 授权编号
   */
  certificateCode: string;

  /**
   * 绑定MAC地址
   */
  bindingMacAddr: string;

  /**
   * 绑定IP地址
   */
  bindingIpAddr: string;

  /**
   * 有效期
   */
  period: string;

  /**
   * 激活状态
   */
  status: string;

  /**
   * 激活人id
   */
  activatorId: string | number;

  /**
   * 激活人账号
   */
  activatorAccount: string;

  fingerPrint: string;
}

export interface ActivationInfoForm extends BaseEntity {
  /**
   * 授权序列号
   */
  serialNum?: number;

  /**
   * 绑定设备
   */
  bindingDevice?: string;

  /**
   * 授权编号
   */
  certificateCode?: string;

  /**
   * 绑定MAC地址
   */
  bindingMacAddr?: string;

  /**
   * 绑定IP地址
   */
  bindingIpAddr?: string;

  /**
   * 有效期
   */
  period?: string;

  /**
   * 激活状态
   */
  status?: string;

  /**
   * 激活人id
   */
  activatorId?: string | number;

  /**
   * 激活人账号
   */
  activatorAccount?: string;

  fingerPrint?: string;
}

export interface ActivationInfoQuery extends PageQuery {
  /**
   * 绑定设备
   */
  bindingDevice?: string;

  /**
   * 授权编号
   */
  certificateCode?: string;

  /**
   * 绑定MAC地址
   */
  bindingMacAddr?: string;

  /**
   * 绑定IP地址
   */
  bindingIpAddr?: string;

  /**
   * 有效期
   */
  period?: string;

  /**
   * 激活状态
   */
  status?: string;

  /**
   * 激活人id
   */
  activatorId?: string | number;

  /**
   * 激活人账号
   */
  activatorAccount?: string;

  /**
   * 日期范围参数
   */
  params?: any;
  fingerPrint?: string;
}
