<script setup lang="ts">
import { addActivationInfo, updateActivationInfo, getCertInfo, getDeviceInfo, getActivation } from '@/api/system/activationInfo';
import { ActivationInfoQuery, ActivationInfoForm } from '@/api/system/activationInfo/types';
import { ElLoading } from 'element-plus';
import Fingerprint2 from 'fingerprintjs2';

const ActivationInfo = ref(null);
const buttonLoading = ref(false);
const activationInfoFormRef = ref<ElFormInstance>();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const isElectron = window.electron;
const isExpired = ref(false);
const isOutOfAUTHORIZED = ref(false);
const authorizedNum = ref(0);
const activatedNum = ref(0);
const isComponentUnmounted = ref(false);
const murmur = ref('');

// 初始化表单数据
const initFormData: ActivationInfoForm = {
  serialNum: undefined,
  bindingDevice: undefined,
  certificateCode: undefined,
  bindingMacAddr: undefined,
  bindingIpAddr: undefined,
  period: undefined,
  status: undefined,
  activatorId: undefined,
  activatorAccount: undefined
};

const data = reactive<PageData<ActivationInfoForm, ActivationInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bindingDevice: undefined,
    certificateCode: undefined,
    bindingMacAddr: undefined,
    bindingIpAddr: undefined,
    period: undefined,
    status: undefined,
    activatorId: undefined,
    activatorAccount: undefined,
    params: {}
  },
  rules: {
    serialNum: [{ required: true, message: '授权序列号不能为空', trigger: 'blur' }],
    bindingDevice: [{ required: true, message: '绑定设备不能为空', trigger: 'blur' }],
    certificateCode: [{ required: true, message: '授权编号不能为空', trigger: 'blur' }],
    bindingMacAddr: [{ required: true, message: '绑定MAC地址不能为空', trigger: 'blur' }],
    bindingIpAddr: [{ required: true, message: '绑定IP地址不能为空', trigger: 'blur' }],
    period: [{ required: true, message: '有效期不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '激活状态不能为空', trigger: 'change' }],
    activatorId: [{ required: true, message: '激活人id不能为空', trigger: 'blur' }],
    activatorAccount: [{ required: true, message: '激活人账号不能为空', trigger: 'blur' }],
    fingerPrint: [{ required: true, message: '设备指纹不能为空', trigger: 'blur' }]
  }
});

const { form, rules } = toRefs(data);

// 获取证书信息
const getInfo = async () => {
  try {
    const res = await getCertInfo();
    if (res.data !== null) {
      form.value.certificateCode = res.data.certificateCode;
      form.value.period = res.data.period;
      form.value.activatorId = res.data.authorizedId;
      form.value.activatorAccount = res.data.authorizedAccount;
      activatedNum.value = res.data.activedNum;
      authorizedNum.value = res.data.authorizedNum;
    }
    isOutOfAUTHORIZED.value = activatedNum.value >= authorizedNum.value;
    isExpired.value = new Date().getTime() > new Date(form.value.period).getTime();
  } catch (error) {
    console.error('获取证书信息失败:', error);
  }
};

// 获取设备信息
const getDevice = async () => {
  buttonLoading.value = true;
  try {
    const res = await getDeviceInfo();
    form.value.bindingDevice = res.data.deviceName;
    form.value.bindingIpAddr = res.data.ipAddress;
    form.value.fingerPrint = murmur.value;
  } catch (error) {
    console.error('获取设备信息失败:', error);
  } finally {
    buttonLoading.value = false;
  }
};

// 提交表单
const submitForm = () => {
  activationInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        if (form.value.serialNum) {
          await updateActivationInfo(form.value);
        } else {
          await addActivationInfo(form.value);
        }
        proxy?.$modal.msgSuccess('操作成功');
        proxy?.$router.push({ path: '/scjk/permit/activationInfo' });
      } catch (error) {
        console.error('提交表单失败:', error);
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

// 获取激活信息
const getActivationInfo = async (macAddress: string) => {
  if (!macAddress || isComponentUnmounted.value) return;
  
  let loading = null;
  try {
    loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    const res = await getActivation(macAddress);
    if (!isComponentUnmounted.value) {
      ActivationInfo.value = res.data;
    }
  } catch (error) {
    console.error('获取激活信息失败:', error);
  } finally {
    if (loading) loading.close();
  }
};

// 获取设备指纹
const getFingerprint = () => {
  if (isComponentUnmounted.value) return;
  
  if (window.electron) {
    try {
      const macAddress = window.electron.ipcRenderer.sendSync('getMacAdress');
      if (macAddress && macAddress !== 'undefined' && macAddress !== 'null') {
        form.value.bindingMacAddr = macAddress.toUpperCase();
      }
    } catch (error) {
      console.error('获取MAC地址失败:', error);
    }
  }
  
  // 使用标准的 setTimeout 替代 requestIdleCallback
  setTimeout(function () {
    if (isComponentUnmounted.value) return;
    
    Fingerprint2.get(function (components) {
      if (isComponentUnmounted.value) return;
      
      murmur.value = Fingerprint2.x64hash128(components.map((component: any) => component.value).join(), 31);
      getActivationInfo(form.value.bindingMacAddr);
    });
  }, 0);
};

// 生命周期钩子
onMounted(() => {
  isComponentUnmounted.value = false;
  getInfo();
  getFingerprint();
});

onBeforeUnmount(() => {
  isComponentUnmounted.value = true;
});
</script>

<template>
  <div>
    <!-- 主要判断：设备是否已绑定 + 是否超出授权次数 -->
    <el-card v-if="ActivationInfo === null && !isOutOfAUTHORIZED">
      <el-form ref="activationInfoFormRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="绑定设备" prop="bindingDevice">
          <el-input v-model="form.bindingDevice" placeholder="请输入绑定设备" />
        </el-form-item>
        <el-form-item label="授权编号" prop="certificateCode">
          <el-input v-model="form.certificateCode" placeholder="请输入授权编号" disabled />
        </el-form-item>
        <el-form-item label="设备指纹" prop="fingerPrint">
          <el-input v-model="form.fingerPrint" placeholder="请输入设备指纹" disabled />
        </el-form-item>
        <el-form-item label="MAC地址" prop="bindingMacAddr">
          <el-input v-model="form.bindingMacAddr" placeholder="请输入绑定MAC地址" disabled />
        </el-form-item>
        <el-form-item label="IP地址" prop="bindingIpAddr">
          <el-input v-model="form.bindingIpAddr" placeholder="请输入绑定IP地址" disabled />
        </el-form-item>
        <el-form-item label="有效期" prop="period">
          <el-date-picker v-model="form.period" clearable type="datetime" disabled value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择有效期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="激活人id" prop="activatorId">
          <el-input v-model="form.activatorId" placeholder="请输入激活人id" disabled />
        </el-form-item>
        <el-form-item label="激活人账号" prop="activatorAccount">
          <el-input v-model="form.activatorAccount" placeholder="请输入激活人账号" disabled />
        </el-form-item>
        <el-form-item prop="activatorAccount">
          <el-button icon="Refresh" :loading="buttonLoading" type="primary" @click="getDevice">获取设备信息</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" style="display: flex; justify-content: center; margin-top: 10px">
          <el-button v-if="form.bindingDevice != null" :loading="buttonLoading" type="primary" @click="submitForm">绑 定 设 备</el-button>
        </div>
      </template>
    </el-card>
    <!-- 简化后的条件判断 -->
    <el-empty v-else-if="ActivationInfo !== null" style="margin-top: 20px" description="该设备已绑定证书，请勿重复绑定" />
    <el-empty v-else style="margin-top: 20px" description="您的授权绑定次数已用完，请联系管理员" />
  </div>
</template>

<style scoped lang="scss">
/* 可以添加一些样式来确保组件在各种状态下都有合适的高度 */
.el-card {
  margin-bottom: 20px;
}
</style>
