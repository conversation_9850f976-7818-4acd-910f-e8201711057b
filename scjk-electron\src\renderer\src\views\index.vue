<template>
  <div class="app-container home">
    <template v-if="isStudent !== true">
      <el-card>
        <el-row :gutter="20" justify="space-between">
          <el-col :span="15">
            <div style="display: flex; flex-direction: row">
              <el-avatar :size="60" :src="useUserStore().avatar" />
              <div style="display: flex; flex-direction: column; margin-left: 20px; line-height: 28px">
                <h1>{{ getTimeState() }}！{{ useUserStore().nickname }}，欢迎登录速诚驾考教学管理系统!</h1>
                <div>
                  <p>
                    <b>当前时间：</b> <span>{{ localTime() }}</span>
                  </p>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="3">
            <el-statistic title="角色类型" :value="useUserStore().roleName" />
          </el-col>
          <el-col :span="3">
            <el-statistic
              v-if="useUserStore().roleName === '学员' && useUserStore().leaningInfo.carModel != null"
              title="报考车型"
              :value="useUserStore().leaningInfo.carModel"
            />
          </el-col>
        </el-row>
      </el-card>
      <el-divider />
      <el-row :gutter="20">
        <el-col :span="24" :xs="24" :lg="12">
          <el-card style="min-width: 480px" shadow="always">
            <template #header
              ><b>通知公告</b> <router-link style="float: right" to="/system/notice"><el-button type="text">更多</el-button></router-link></template
            >
            <!-- <el-empty description="暂无数据" /> -->
            <systemNotice />
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20"></el-row>
    </template>

    <template v-else>
      <student></student>
    </template>
  </div>
</template>

<script setup name="Index" lang="ts">
import useUserStore from '@/store/modules/user';
import useSettingsStore from '@/store/modules/settings';
import systemNotice from '../layout/components/SystemNotice/index.vue';
import Student from '@/components/Scjk/Student/index.vue';
import { onBeforeRouteLeave } from 'vue-router';

const goTarget = (url: string) => {
  window.open(url, '__blank');
};
const nowTime = ref('');
const nowDate = ref('');
const getTimeState = () => {
  const now = new Date();
  const hour = now.getHours();
  if (hour < 6) {
    return '凌晨好';
  } else if (hour < 9) {
    return '早上好';
  } else if (hour < 12) {
    return '上午好';
  } else if (hour < 14) {
    return '中午好';
  } else if (hour < 17) {
    return '下午好';
  } else if (hour < 19) {
    return '傍晚好';
  } else if (hour < 22) {
    return '晚上好';
  } else {
    return '深夜好';
  }
};

const isStudent = computed(() => {
  return useUserStore().isStudent;
});
onMounted(() => {
  var date = new Date();
  var yy = date.getFullYear();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  nowDate.value = yy + ' 年 ' + mm + ' 月 ' + dd + ' 日';
  setInterval(() => {
    nowTime.value = new Date().toLocaleTimeString();
  }, 1000);

  useSettingsStore().isIndex = true;
});

onBeforeRouteLeave((to, from, next) => {
  useSettingsStore().isIndex = false;
  next();
});
const localTime = () => {
  return `${nowDate.value} ${nowTime.value}`;
};
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: 'open sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
.avatar-container {
  margin-right: 40px;

  .avatar-wrapper {
    margin-top: 5px;
    position: relative;

    .user-avatar {
      cursor: pointer;
      width: 40px;
      height: 40px;
      border-radius: 10px;
      margin-top: 10px;
    }

    i {
      cursor: pointer;
      position: absolute;
      right: -20px;
      top: 25px;
      font-size: 12px;
    }
  }
}
</style>
