<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="试题" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入题目" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="EditPen" @click="handlePractice()">练习</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['scjk:collection:remove']"
              >删除</el-button
            >
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="collectionList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="题目" align="center" prop="questionFormat">
          <template #default="scope">
            <editor v-model="scope.row.questionFormat" :read-only="true" :min-height="50" :height="100"></editor>
          </template>
        </el-table-column>
        <el-table-column label="收藏时间" align="center" prop="time" width="300">
          <template #default="scope">
            <span>{{ parseTime(scope.row.time, '{y}-{m}-{d} {hh}:{mm}:{ss}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="130">
          <template #default="scope">
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['scjk:collection:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="Collection" lang="ts">
import { listCollection, delCollection } from '@/api/scjk/collection';
import { CollectionVO, CollectionQuery, CollectionForm } from '@/api/scjk/collection/types';
import Editor from '@/components/Scjk/Editor/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const collectionList = ref<CollectionVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();

const initFormData: CollectionForm = {
  questionId: undefined
};
const data = reactive<PageData<CollectionForm, CollectionQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: undefined,
    params: {}
  },
  rules: {}
});

const { queryParams, form } = toRefs(data);

/** 查询用户收藏列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCollection(queryParams.value);
  collectionList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: CollectionVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 删除按钮操作 */
const handleDelete = async (row?: CollectionVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除用户收藏编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delCollection(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/**
 * 练习
 */
const handlePractice = () => {
  proxy?.$router.push({
    path: '/collection/practice'
  });
};

onMounted(() => {
  getList();
});
</script>
