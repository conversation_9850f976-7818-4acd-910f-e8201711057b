<template>
  <div class="p-2">
    <el-form ref="questionFormRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="车型" prop="carType">
        <car-type v-model="form.carType" :multiple="true" clearable placeholder="请选择车型"></car-type>
      </el-form-item>
      <el-form-item label="科目" prop="subject">
        <subject v-model="form.subject" :multiple="true" clearable placeholder="请选择科目"></subject>
      </el-form-item>
      <el-form-item label="章节" prop="chapter">
        <chapter v-model="form.chapter" :multiple="true" clearable placeholder="请选择章节"></chapter>
      </el-form-item>
      <el-form-item label="专题" prop="special">
        <special v-model="form.special" :multiple="true" clearable placeholder="请选择专题"></special>
      </el-form-item>
      <el-form-item label="题型" prop="type">
        <el-radio-group v-model="form.type">
          <el-radio v-for="qsType in question_type" :key="qsType.value" :label="qsType.value">{{ qsType.label }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="题目" prop="question">
        <el-input v-model="form.question" class="question-option-content-input" @focus="editorInputClick(form, 'question')" />
      </el-form-item>
      <el-form-item label="图片" prop="questionImage">
        <image-upload v-model="form.questionImage" :limit="1"></image-upload>
      </el-form-item>
      <el-form-item label="选项" prop="options">
        <el-form-item v-for="(item, index) in form.options" :key="item.prefix" :label="item.prefix" label-width="50px" class="question-option-label">
          <el-input v-model="item.prefix" style="width: 50px" />
          <el-input v-model="item.content" class="question-option-content-input" @focus="editorInputClick(item, 'content')" />
          <el-button type="danger" class="question-option-remove" :icon="Delete" @click="removeQustionOption(index)"></el-button>
        </el-form-item>
      </el-form-item>
      <el-form-item label="" prop="">
        <el-button type="primary" @click="addQuesitonOption">添加</el-button>
      </el-form-item>
      <el-form-item label="答案" prop="answer">
        <!-- 单选||判断 -->
        <template v-if="form.type === 'S' || form.type === 'J'">
          <el-radio-group v-model="form.answer">
            <el-radio v-for="item in form.options" :key="item.prefix" :label="item.prefix">{{ item.prefix }}</el-radio>
          </el-radio-group>
        </template>
        <!-- 多选 -->
        <template v-else-if="form.type === 'M'">
          <el-checkbox-group v-model="form.answer">
            <el-checkbox v-for="item in form.options" :key="item.prefix" :label="item.prefix" :value="item.prefix"></el-checkbox>
          </el-checkbox-group>
        </template>
      </el-form-item>
      <el-form-item label="解析" prop="analysis">
        <el-input v-model="form.analysis" class="question-option-content-input" @focus="editorInputClick(form, 'analysis')" />
      </el-form-item>
      <el-form-item label="技巧" prop="skill">
        <el-input v-model="form.skill" class="question-option-content-input" @focus="editorInputClick(form, 'skill')" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="cancel">取消</el-button>
        <el-button type="danger" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <el-dialog
      v-model="editorDialog.visible"
      append-to-body
      :close-on-click-modal="false"
      style="width: 700px; height: 500px"
      :show-close="false"
      center
    >
      <editor v-model="editorDialog.content" :width="600" :min-height="200" :height="300"></editor>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="editorDialogConfirm"> 确定 </el-button>
          <el-button @click="editorDialogCancel">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script name="QuestionEdit" setup lang="ts">
import { getQuestionOld, addQuestionOld, updateQuestionOld } from '@/api/scjk/questionbank/questionOld';
import { QuestionOldForm } from '@/api/scjk/questionbank/questionOld/types';
import CarType from '@/components/Scjk/CarType/index.vue';
import Chapter from '@/components/Scjk/Chapter/index.vue';
import Special from '@/components/Scjk/Special/index.vue';
import Subject from '@/components/Scjk/Subject/index.vue';
import { Delete } from '@element-plus/icons-vue';

import Editor from '@/components/Scjk/Editor/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { question_type } = toRefs<any>(proxy?.useDict('question_type'));

const questionFormRef = ref<ElFormInstance>();

const initFormData: QuestionOldForm = {
  id: undefined,
  carType: undefined,
  subject: undefined,
  chapter: undefined,
  special: undefined,
  question: undefined,
  options: [
    { prefix: 'A', content: '' },
    { prefix: 'B', content: '' },
    { prefix: 'C', content: '' },
    { prefix: 'D', content: '' }
  ],
  answer: undefined,
  analysis: undefined,
  skill: undefined,
  type: 'S' // 默认单选
};

const form = ref<QuestionOldForm>({ ...initFormData });
const rules = ref<ElFormRules>({
  id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }],
  carType: [{ required: true, message: '车型不能为空', trigger: 'change', type: 'array' }],
  subject: [{ required: true, message: '科目不能为空', trigger: 'change', type: 'array' }],
  question: [{ required: true, message: '题目不能为空', trigger: 'blur' }],
  options: [{ required: true, message: '选项不能为空', trigger: 'blur' }],
  answer: [{ required: true, message: '答案不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '题型不能为空', trigger: 'blur' }]
});

const editorDialog = ref({
  visible: false,
  content: null,
  object: null,
  propName: '',
  instance: null
});

const title = ref<string>('新增');

/**
 * 添加选项
 */
const addQuesitonOption = () => {
  form.value.options.push({ prefix: '', content: '' });
};

/**
 * 删除选项
 * @param index
 */
const removeQustionOption = (index: number) => {
  form.value.options.splice(index, 1);
};

/**
 * 富文本窗口弹窗
 * @param object
 * @param propName
 */
const editorInputClick = (object: any, propName: string) => {
  editorDialog.value.object = object;
  editorDialog.value.propName = propName;
  editorDialog.value.content = editorDialog.value.object[editorDialog.value.propName];
  editorDialog.value.visible = true;
};
/**
 * 富文本弹窗确认
 */
const editorDialogConfirm = () => {
  // 更新数据
  editorDialog.value.object[editorDialog.value.propName] = editorDialog.value.content;
  // 关闭窗口
  editorDialog.value.visible = false;
};

/**
 * 富文本弹窗关闭
 */
const editorDialogCancel = () => {
  editorDialog.value.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  questionFormRef.value?.resetFields();
};

/**
 * 取消
 */
const cancel = () => {
  proxy?.$router.back();
};

/** 提交按钮 */
const submitForm = async () => {
  questionFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 格式化处理
      const formStringify = JSON.stringify(form.value);
      const saveForm: QuestionOldForm = JSON.parse(formStringify);

      saveForm.carType = saveForm.carType?.join();
      saveForm.subject = saveForm.subject?.join();
      saveForm.chapter = saveForm.chapter?.join();
      saveForm.special = saveForm.special?.join();
      saveForm.options = JSON.stringify(saveForm.options);
      // 多选时逗号分隔
      saveForm.answer = saveForm.type === 'M' ? saveForm.answer?.join() : saveForm.answer;

      if (saveForm.id) {
        await updateQuestionOld(saveForm).finally(() => {
          // 关闭
          cancel();
        });
      } else {
        await addQuestionOld(saveForm).finally(() => {
          // 关闭
          cancel();
        });
      }
      proxy?.$modal.msgSuccess(`${title.value}成功`);
    }
  });
};

onMounted(() => {
  reset();
  const id = proxy?.$route.query.id;

  if (id !== undefined) {
    title.value = '编辑';
    getQuestionOld(id).then((res) => {
      if (res.code === 200) {
        // 格式化
        const tempForm: QuestionOldForm = { ...res.data };
        tempForm.carType = tempForm.carType?.split(',');
        tempForm.subject = tempForm.subject?.split(',');
        tempForm.chapter = tempForm.chapter?.split(',');
        tempForm.special = tempForm.special?.split(',');
        tempForm.options = JSON.parse(tempForm.options);

        // 多选时才转数组
        tempForm.answer = tempForm.type === 'M' ? tempForm.answer?.split(',') : tempForm.answer;

        form.value = { ...tempForm };
        console.log('---', form.value);
      }
    });
  }
});
</script>
<style>
.question-option-label {
  margin-top: 10px;
  margin-bottom: 10px !important;
  width: 90%;
}

.question-option-content-input {
  margin-left: 8px;
  width: 60%;
}

.question-option-remove {
  margin-left: 20px;
}
</style>
