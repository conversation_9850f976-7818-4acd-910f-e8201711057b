export interface CertificateAuthInfoVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 授权编号
   */
  certificateCode: string;

  /**
   * 授权人名称
   */
  authorizedAccount: string;

  /**
   * 授权人
   */
  authorizedId: string | number;

  /**
   * 授权数量
   */
  authorizedNum: number;

  /**
   * 绑定数量
   */
  bindingNum: number;

  /**
   * 激活数量
   */
  activedNum: number;

  period: string;
}

export interface CertificateAuthInfoForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 授权编号
   */
  certificateCode?: string;

  /**
   * 授权人名称
   */
  authorizedAccount?: string;

  /**
   * 授权人
   */
  authorizedId?: string | number;

  /**
   * 授权数量
   */
  authorizedNum?: number;

  /**
   * 绑定数量
   */
  bindingNum?: number;

  /**
   * 激活数量
   */
  activedNum?: number;

  period?: string;
}

export interface CertificateAuthInfoQuery extends PageQuery {
  /**
   * 授权编号
   */
  certificateCode?: string;

  /**
   * 授权人名称
   */
  authorizedAccount?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
