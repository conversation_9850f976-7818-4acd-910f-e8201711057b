import { Option } from '@/api/scjk/questionbank/question/types';

export interface AnswerVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 题目
   */
  question: number;

  /**
   * 答案
   */
  answer: string;
  answerFormat: string;

  /**
   * 正确[0-错,1-对]
   */
  correct: number;

  /**
   * 来源
   */
  source: number;

  switched: boolean;

  /**
   * 选项
   */
  optionList: Array<Option>;
}

export interface AnswerQuery extends PageQuery {
  name: string;
  carType: string | number;
  subject: string | number;
}
