<template>
  <div class="p-2">
    <el-form ref="paperFormRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item
        ><el-icon color="red"><WarnTriangleFilled /></el-icon><span style="color: red">注意：每题默认1分!!!</span>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="分类" prop="category">
        <el-radio-group v-model="form.category">
          <el-radio v-for="ct in paper_category" :key="ct.value" :value="ct.value">{{ ct.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="车型" prop="carType">
        <car-type v-model="form.carType" clearable placeholder="请选择车型"></car-type>
      </el-form-item>
      <el-form-item label="科目" prop="subject">
        <subject v-model="form.subject" clearable placeholder="请选择科目"></subject>
      </el-form-item>
      <el-form-item label="时间" prop="time"> <el-input-number v-model="form.time" :min="1" :step="1"></el-input-number>&nbsp;分钟 </el-form-item>
      <!-- 试题-->
      <el-form-item label="试题" prop="questions">
        <el-row style="width: 100%">
          <el-col :span="21"><el-button type="primary" @click="addQuestion">选择试题</el-button></el-col>
          <el-col :span="3"><el-button v-if="hasQuestions" type="danger" @click="emptyQuestions">清空</el-button></el-col>
        </el-row>

        <el-row style="margin-top: 10px">
          <el-col>
            <div class="sort">
              <div class="q-wrap">
                <li v-for="question of form.questions" :key="question.id" :data-order="question.sort" class="qn">
                  <p>{{ question.sort }}</p>
                </li>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-collapse style="width: 100%; margin-top: 10px">
          <el-collapse-item>
            <template #title>
              预览-[<span style="color: red">{{ form.questions.length }}</span
              >]
            </template>
            <el-card style="width: 100%">
              <el-card v-for="question of form.questions" :key="question.id" style="margin-bottom: 10px; width: 100%">
                <p>
                  <el-row>
                    <el-col :span="22"
                      ><span class="question-title">顺序：</span>
                      <el-input-number
                        v-model="question.sort"
                        :min="1"
                        :step="1"
                        :max="999"
                        controls-position="right"
                        style="width: 90px"
                        @change="resortQuestions"
                      ></el-input-number>
                    </el-col>
                    <el-col :span="2"><el-button type="warning" :icon="Delete" @click="removeQuestion(question)"></el-button></el-col>
                  </el-row>
                </p>
                <p>
                  <span class="question-title">题目：</span>（<dict-tag
                    :options="question_type"
                    :value="question.type"
                    style="display: inline-block"
                  />题）{{ question.title }}
                </p>
                <p v-if="question.image">
                  <image-preview :src="question.image" :width="100" :height="100"></image-preview>
                </p>

                <p>
                  <span class="question-title">选项：</span>
                  <template v-for="option of JSON.parse(question.options)" :key="option.prefix">
                    <span>{{ option.prefix }}：{{ option.content }}&nbsp;&nbsp;&nbsp; </span>
                  </template>
                </p>
                <p><span class="question-title">答案：</span>{{ question.answer }}</p>
              </el-card>
            </el-card>
          </el-collapse-item>
        </el-collapse>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="close">关闭</el-button>
        <el-button type="danger" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <!--选择试题弹窗-->
    <question v-model:visible="questionDialogVisible" title="选择试卷" :width="900" @selected="selectedQuestions"></question>

    <el-backtop :right="50" :bottom="100" />
  </div>
</template>
<script name="PaperEdit" setup lang="ts">
import { getPaper, savePaper } from '@/api/scjk/paper/paper';
import { PaperForm } from '@/api/scjk/paper/paper/types';
import { QuestionVO } from '@/api/scjk/questionbank/question/types';

import CarType from '@/components/Scjk/CarType/index.vue';
import Subject from '@/components/Scjk/Subject/index.vue';
import Question from '@/components/Scjk/Question/index.vue';
import ImagePreview from '@/components/Scjk/ImagePreview/index.vue';
import { Delete } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { paper_category, question_type } = toRefs<any>(proxy?.useDict('paper_category', 'question_type'));

const paperFormRef = ref<ElFormInstance>();

const initFormData = {
  id: undefined,
  name: undefined,
  category: 'B',
  carType: undefined,
  subject: undefined,
  full: 0,
  pass: 0,
  time: 45, // 默认45分钟
  status: 'D',
  sort: 1,
  questions: []
};

const form = ref<PaperForm>({ ...initFormData });

const rules = ref<ElFormRules>({
  id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  category: [{ required: true, message: '分类不能为空', trigger: 'blur' }],
  carType: [{ required: true, message: '车型不能为空', trigger: 'blur' }],
  subject: [{ required: true, message: '科目不能为空', trigger: 'blur' }],
  time: [{ required: true, message: '时间不能为空', trigger: 'blur' }]
});

/**
 * 是否有试题
 */
const hasQuestions = computed(() => form.value.questions.length > 0);

/**
 * 试题弹窗
 */
const questionDialogVisible = ref<boolean>(false);

/** 选择试题钮操作 */
const addQuestion = () => {
  if (form.value.carType === undefined || form.value.carType === '') {
    proxy?.$modal.msgError('请先选择车型！');
    return;
  }
  if (form.value.subject === undefined || form.value.subject === '') {
    proxy?.$modal.msgError('请先选择科目！');
    return;
  }

  questionDialogVisible.value = true;
};

/**
 * 选择试题
 * @param question
 */
const selectedQuestions = (questions: Array<QuestionVO>, edit = false) => {
  console.log('questions', questions);
  if (questions.length < 1) {
    return;
  }

  let formateQuestions = [];
  try {
    formateQuestions = questions.map((question) => {
      console.log('问题', question);
      // 默认1分
      return edit ? question : { ...question, paper: undefined, question: question.id, title: question.question, val: 1 };
    });
  } catch (e) {
    console.error(e);
    proxy?.$modal.msgError('选择试题失败' + e);
    return;
  }

  if (formateQuestions.length < 1) {
    proxy?.$modal.msgError('选择试题内容有误！请检查');
    return;
  }

  console.log(formateQuestions);

  // 按题型分组，并去重
  const map = new Map();
  for (const question of form.value.questions) {
    map.set(question.id);
  }

  let repeatCount = 0;
  const mergeQuestions = formateQuestions.reduce((previous, current) => {
    if (map.has(current.id)) {
      repeatCount++;
      return previous;
    }

    current.sort = previous.length + 1;
    map.set(current.id);
    previous.push(current);
    return previous;
  }, form.value.questions);

  if (repeatCount > 0) {
    proxy.$modal.alertError(`选择试题[${questions.length}]道，其中重复[${repeatCount}]道！`);
  }

  form.value.questions = mergeQuestions;
};

/**
 * 清空试题
 */
const emptyQuestions = async () => {
  await proxy?.$modal.confirm('确定要清空所有试题吗？');
  form.value.questions = [];

  proxy?.$modal.msgSuccess('已清空');
};

/**
 * 删除试题
 */
const removeQuestion = async (question: QuestionVO) => {
  await proxy?.$modal.confirm('确定要删除试题吗？');
  form.value.questions = form.value.questions.filter((item) => item.id !== question.id);
  proxy?.$modal.msgSuccess('已删除');
};

/**
 * 重新排序
 */
const resortQuestions = () => {
  form.value.questions = form.value.questions.sort((a, b) => a.sort - b.sort);
};

/** 提交按钮 */
const submitForm = () => {
  paperFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      let isUpdate = form.value.id !== undefined && form.value.id !== '';
      await savePaper(form.value).then(() => {
        close();
        proxy?.$modal.msgSuccess(isUpdate ? '修改成功' : '新增成功');
      });
    }
  });
};

/**
 * 关闭
 */
const close = () => {
  reset();
  proxy?.$tab.closePage(proxy?.$route);
  proxy?.$router.back();
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  paperFormRef.value?.resetFields();
};

onMounted(async () => {
  reset();

  const id = proxy?.$route.query.id;
  if (id !== undefined) {
    getPaper(id).then(async (res) => {
      if (res.code === 200) {
        form.value = { ...res.data };
      }
    });
  }
});
</script>
<style lang="scss" scoped>
.question-title {
  font-weight: 700;
}

li {
  list-style: none;
}

p {
  margin: 0;
  padding: 0;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    \\5FAE\8F6F\96C5\9ED1,
    Arial,
    sans-serif !important;
}

.sort {
  width: 400px;
}
.sort .q-wrap {
  overflow: hidden;
}
.sort .q-wrap li {
  width: 40px;
  height: 40px;
  color: #4a4a4a;
  font-size: 13px;
  border: 1px solid #eee;
  text-align: center;
  float: left;
  box-sizing: border-box;
  cursor: pointer;
}
</style>
