<template>
  <el-select
    :model-value="value"
    :placeholder="placeholder"
    :multiple="multiple"
    :clearable="clearable"
    :disabled="disabled"
    @change="handleChange"
    style="width: 240px"
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>

<script name="CarType" lang="ts" setup>
import { listSelect } from '@/api/scjk/paper/paperConfig';
import { SelectVO } from '@/api/common/types';

const props = defineProps({
  value: {
    type: String || Array,
    default: undefined
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否可以清空选项
  clearable: {
    type: Boolean,
    default: true
  },
  // 占位符，默认为“Select”
  placeholder: {
    type: String,
    default: '请选择'
  }
});

const emit = defineEmits(['update:modelValue', 'selected']);
const handleChange = (value: string) => {
  emit('update:modelValue', value);

  for (const option of options.value) {
    if (option.value === value) {
      emit('selected', option);
    }
  }
};

const options = ref<Array<SelectVO>>([]);

onMounted(() => {
  listSelect().then((res) => {
    if (res.code == 200 && res.data.length > 0) {
      options.value = [...res.data];
    }
  });
});
</script>
