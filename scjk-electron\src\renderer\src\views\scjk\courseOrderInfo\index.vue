<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="课程名称" prop="courseName">
            <el-input v-model="queryParams.courseName" placeholder="请输入课程名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="教练名称" prop="teacherName">
            <el-input v-model="queryParams.teacherName" placeholder="请输入教练名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker v-model="queryParams.startDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择开始日期" />
          </el-form-item>
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker v-model="queryParams.endDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择结束日期" />
          </el-form-item>
          <el-form-item label="预约人" prop="bookingPeople">
            <el-input v-model="queryParams.bookingPeople" placeholder="请输入预约人" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="预约状态" prop="bookState">
            <el-select v-model="queryParams.bookState" placeholder="请选择预约状态" clearable>
              <el-option v-for="dict in course_book_state" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:courseOrderInfo:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:courseOrderInfo:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:courseOrderInfo:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:courseOrderInfo:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="courseOrderInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="false" label="主键" align="center" prop="id" />
        <el-table-column label="课程名称" align="center" prop="courseName" />
        <el-table-column label="教练名称" align="center" prop="teacherName" />
        <el-table-column label="开始日期" align="center" prop="startDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束日期" align="center" prop="endDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="预约人" align="center" prop="bookingPeople" />
        <el-table-column label="预约状态" align="center" prop="bookState">
          <template #default="scope">
            <dict-tag :options="course_book_state" :value="scope.row.bookState" />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="取消预约" placement="top">
              <el-button
                v-if="scope.row.bookState != 'cancel'"
                v-hasPermi="['scjk:courseOrderInfo:edit']"
                link
                type="warning"
                icon="CloseBold"
                @click="cancleOrder(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['scjk:courseOrderInfo:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['scjk:courseOrderInfo:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改课程预约记录对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="600px" append-to-body>
      <el-form ref="courseOrderInfoFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课程名称" prop="courseId">
          <el-select v-model="form.courseId" @change="handleChange(form.courseId, courseList)">
            <el-option v-for="item in courseList" :key="item.id" :label="item.courseName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="教练" prop="teacherName">
          <el-input v-model="form.teacherName" disabled placeholder="请输入教练姓名" />
          <!-- <el-select v-model="form.teacherId">
            <el-option v-for="item in teacherList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select> -->
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker v-model="form.startDate" disabled clearable type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择开始日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker v-model="form.endDate" disabled clearable type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="预约人" prop="bookingPeople">
          <el-input v-model="form.bookingPeople" disabled placeholder="请输入预约人" />
        </el-form-item>
        <!-- <el-form-item label="预约状态" prop="bookState">
          <el-select v-model="form.bookState" placeholder="请选择预约状态">
            <el-option v-for="dict in course_book_state" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CourseOrderInfo" lang="ts">
import { listCourseOrderInfo, getCourseOrderInfo, delCourseOrderInfo, addCourseOrderInfo, updateCourseOrderInfo } from '@/api/scjk/courseOrderInfo';
import { CourseOrderInfoVO, CourseOrderInfoQuery, CourseOrderInfoForm } from '@/api/scjk/courseOrderInfo/types';
import { CourseScheduleVO } from '@/api/scjk/courseSchedule/types';
import { listCourseSchedule } from '@/api/scjk/courseSchedule/index';
import useUserStore from '@/store/modules/user';
import { getUser } from '@/api/system/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { course_book_state } = toRefs<any>(proxy?.useDict('course_book_state'));
const courseList = ref<CourseScheduleVO[]>([]);
const courseOrderInfoList = ref<CourseOrderInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const userStore = useUserStore();

const userId = ref(userStore.userId);

const queryFormRef = ref<ElFormInstance>();
const courseOrderInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CourseOrderInfoForm = {
  id: undefined,
  courseId: undefined,
  courseName: undefined,
  teacherId: undefined,
  teacherName: undefined,
  startDate: undefined,
  endDate: undefined,
  bookingPeople: undefined,
  bookState: undefined,
  remark: undefined
};
const data = reactive<PageData<CourseOrderInfoForm, CourseOrderInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    courseName: undefined,
    teacherName: undefined,
    startDate: undefined,
    endDate: undefined,
    bookingPeople: undefined,
    bookState: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    courseId: [{ required: true, message: '课程id不能为空', trigger: 'blur' }],
    courseName: [{ required: true, message: '课程名称不能为空', trigger: 'blur' }],
    teacherId: [{ required: true, message: '教练id不能为空', trigger: 'blur' }],
    teacherName: [{ required: true, message: '教练名称不能为空', trigger: 'blur' }],
    startDate: [{ required: true, message: '开始日期不能为空', trigger: 'blur' }],
    endDate: [{ required: true, message: '结束日期不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询课程预约记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCourseOrderInfo(queryParams.value);
  courseOrderInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  courseOrderInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: CourseOrderInfoVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加课程预约记录';
  getCourseSchedule();
};

/** 修改按钮操作 */
const handleUpdate = async (row?: CourseOrderInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getCourseOrderInfo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改课程预约记录';
  getCourseSchedule();
};

/** 提交按钮 */
const submitForm = () => {
  courseOrderInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      form.value.bookState = 'booked';
      if (form.value.id) {
        await updateCourseOrderInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addCourseOrderInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CourseOrderInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除课程预约记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delCourseOrderInfo(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/courseOrderInfo/export',
    {
      ...queryParams.value
    },
    `courseOrderInfo_${new Date().getTime()}.xlsx`
  );
};
/** 查询教师信息 */
async function getCourseSchedule() {
  const res = await listCourseSchedule();
  courseList.value = res.rows;
}

const handleChange = (courseId: string, dataArr: any[]) => {
  if (courseId !== '') {
    console.log(dataArr);
    form.value.courseName = dataArr.filter((item) => item.id == courseId)[0].courseName;
    form.value.teacherId = dataArr.filter((item) => item.id == courseId)[0].teacherId;
    form.value.startDate = dataArr.filter((item) => item.id == courseId)[0].startDate;
    form.value.endDate = dataArr.filter((item) => item.id == courseId)[0].endDate;
    form.value.bookingPeople = userId;
  }
  const teacherId = form.value.teacherId;
  getTeacherInfo(teacherId);
};
/** 查询教师信息 */
async function getTeacherInfo(teacherId: number | string | undefined) {
  const res = await getUser(teacherId);
  form.value.teacherName = res.data.user.nickName;
}
const cancleOrder = async (row: Array<string>) => {
  row.bookState = 'cancel';
  await updateCourseOrderInfo(row).finally(() => (buttonLoading.value = false));
  proxy?.$modal.msgSuccess('修改成功');
  await getList();
};
onMounted(() => {
  getList();
});
</script>
