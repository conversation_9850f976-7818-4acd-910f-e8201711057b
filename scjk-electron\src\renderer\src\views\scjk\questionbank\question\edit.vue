<template>
  <div class="p-2">
    <el-form ref="questionFormRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="车型" prop="carType">
        <car-type v-model="form.carType" :multiple="true" clearable placeholder="请选择车型"></car-type>
      </el-form-item>
      <el-form-item label="科目" prop="subject">
        <subject v-model="form.subject" :multiple="true" clearable placeholder="请选择科目"></subject>
      </el-form-item>
      <el-form-item label="章节" prop="chapter">
        <chapter v-model="form.chapter" :multiple="true" clearable placeholder="请选择章节"></chapter>
      </el-form-item>
      <el-form-item label="专题" prop="special">
        <special v-model="form.special" :multiple="true" clearable placeholder="请选择专题"></special>
      </el-form-item>
      <el-form-item label="题型" prop="type">
        <el-radio-group v-model="form.type" @change="questionTypeOnChange">
          <el-radio v-for="qsType in question_type" :key="qsType.value" :value="qsType.value">{{ qsType.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="题目" prop="question">
        <div class="editor">
          <quill-editor ref="questionQuillEditorRef" v-model:content="form.question" content-type="html" :options="options" style="height: 200px" />
        </div>
      </el-form-item>
      <el-form-item label="图片" prop="image">
        <image-upload v-model="form.image" :limit="1"></image-upload>
      </el-form-item>
      <el-form-item label="选项" prop="options">
        <el-form-item v-for="(item, index) in form.options" :key="item.prefix" :label="item.prefix" label-width="50px" class="question-option-label">
          <el-input v-model="item.prefix" style="width: 50px" />
          <el-input v-model="item.content" class="question-option-content-input" />
          <el-button type="danger" class="question-option-remove" :icon="Delete" @click="removeQustionOption(index)"></el-button>
        </el-form-item>
      </el-form-item>
      <el-form-item label="" prop="">
        <el-button type="primary" @click="addQuesitonOption">添加</el-button>
      </el-form-item>
      <el-form-item label="答案" prop="answer">
        <!-- 单选||判断 -->
        <template v-if="form.type === 'S' || form.type === 'J'">
          <el-radio-group v-model="form.answer">
            <el-radio v-for="item in form.options" :key="item.prefix" :value="item.prefix">{{ item.prefix }}</el-radio>
          </el-radio-group>
        </template>
        <!-- 多选 -->
        <template v-else-if="form.type === 'M'">
          <el-checkbox-group v-model="form.answer">
            <el-checkbox v-for="item in form.options" :key="item.prefix" :label="item.prefix" :value="item.prefix"></el-checkbox>
          </el-checkbox-group>
        </template>
      </el-form-item>
      <el-form-item label="解析" prop="analysis">
        <div class="editor">
          <quill-editor ref="analysisQuillEditorRef" v-model:content="form.analysis" content-type="html" :options="options" style="height: 200px" />
        </div>
      </el-form-item>
      <el-form-item label="解析音频" prop="analysisAudio">
        <audio-upload v-model="form.analysisAudio" :limit="1"></audio-upload>
      </el-form-item>
      <el-form-item label="解析视频" prop="analysisVideo">
        <video-upload v-model="form.analysisVideo" :limit="1"></video-upload>
      </el-form-item>
      <el-form-item label="技巧" prop="skill">
        <div class="editor">
          <quill-editor ref="skillQuillEditorRef" v-model:content="form.skill" content-type="html" :options="options" style="height: 200px" />
        </div>
      </el-form-item>
      <el-form-item label="技巧音频" prop="skillAudio">
        <audio-upload v-model="form.skillAudio" :limit="1"></audio-upload>
      </el-form-item>
      <el-form-item label="技巧视频" prop="skillVideo">
        <video-upload v-model="form.skillVideo" :limit="1"></video-upload>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="close">关闭</el-button>
        <el-button type="danger" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script name="QuestionEdit" setup lang="ts">
import { getQuestion, addQuestion, updateQuestion } from '@/api/scjk/questionbank/question';
import { QuestionForm } from '@/api/scjk/questionbank/question/types';
import CarType from '@/components/Scjk/CarType/index.vue';
import Chapter from '@/components/Scjk/Chapter/index.vue';
import Special from '@/components/Scjk/Special/index.vue';
import Subject from '@/components/Scjk/Subject/index.vue';
import ImageUpload from '@/components/ImageUpload';
import AudioUpload from '@/components/Scjk/AudioUpload';
import VideoUpload from '@/components/Scjk/VideoUpload';
import { Delete } from '@element-plus/icons-vue';

import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { question_type } = toRefs<any>(proxy?.useDict('question_type'));

const questionFormRef = ref<ElFormInstance>();

const initFormData: QuestionForm = {
  id: undefined,
  carType: undefined,
  subject: undefined,
  chapter: undefined,
  special: undefined,
  question: undefined,
  image: undefined,
  options: [
    { prefix: 'A', content: '' },
    { prefix: 'B', content: '' },
    { prefix: 'C', content: '' },
    { prefix: 'D', content: '' }
  ],
  answer: undefined,
  analysis: undefined,
  analysisAudio: undefined,
  analysisVideo: undefined,
  skill: undefined,
  skillAudio: undefined,
  skillVideo: undefined,
  type: 'S' // 默认单选
};

const form = ref<QuestionForm>({ ...initFormData });
const rules = ref<ElFormRules>({
  id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }],
  carType: [{ required: true, message: '车型不能为空', trigger: 'change', type: 'array' }],
  subject: [{ required: true, message: '科目不能为空', trigger: 'change', type: 'array' }],
  question: [{ required: true, message: '题目不能为空', trigger: 'blur' }],
  options: [{ required: true, message: '选项不能为空', trigger: 'blur' }],
  answer: [{ required: true, message: '答案不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '题型不能为空', trigger: 'blur' }]
});

const questionQuillEditorRef = ref();
const analysisQuillEditorRef = ref();
const skillQuillEditorRef = ref();

const title = ref<string>('新增');

// 富文本编辑器配置
const options = ref({
  placeholder: '',
  readOnly: false,
  theme: 'snow',
  bounds: document.body,
  debug: 'warn',
  modules: {
    // 工具栏配置
    toolbar: {
      container: [
        ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
        ['blockquote', 'code-block'], // 引用  代码块
        [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
        [{ indent: '-1' }, { indent: '+1' }], // 缩进
        [{ size: ['small', false, 'large', 'huge'] }], // 字体大小
        [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
        [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
        [{ align: [] }], // 对齐方式
        ['clean'] // 清除文本格式
      ]
    }
  }
});

/**
 * 选择题型
 */
const questionTypeOnChange = (value: string) => {
  if (value === 'M') {
    form.value.answer = [];
  } else {
    form.value.answer = '';
  }
};

/**
 * 添加选项
 */
const addQuesitonOption = () => {
  form.value.options.push({ prefix: '', content: '' });
};

/**
 * 删除选项
 * @param index
 */
const removeQustionOption = (index: number) => {
  form.value.options.splice(index, 1);
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  questionQuillEditorRef.value.setText('');
  analysisQuillEditorRef.value.setText('');
  skillQuillEditorRef.value.setText('');
  questionFormRef.value?.resetFields();
};

/**
 * 取消
 */
const close = () => {
  reset();
  proxy?.$tab.closePage(proxy?.$route);
  proxy?.$router.back();
};

/** 提交按钮 */
const submitForm = async () => {
  questionFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 格式化处理
      const formStringify = JSON.stringify(form.value);
      const saveForm: QuestionForm = JSON.parse(formStringify);

      saveForm.carType = saveForm.carType?.join();
      saveForm.subject = saveForm.subject?.join();
      saveForm.chapter = saveForm.chapter?.join();
      saveForm.special = saveForm.special?.join();
      saveForm.options = JSON.stringify(saveForm.options);
      // 多选时逗号分隔
      saveForm.answer = saveForm.type === 'M' ? saveForm.answer?.join() : saveForm.answer;

      if (saveForm.id) {
        await updateQuestion(saveForm).then((res) => {
          console.log('更新', res);
          // 关闭
          close();
          proxy?.$modal.msgSuccess(`${title.value}成功`);
        });
      } else {
        await addQuestion(saveForm).then((res) => {
          console.log('新增', res);
          // 关闭
          close();
          proxy?.$modal.msgSuccess(`${title.value}成功`);
        });
      }
    }
  });
};

onMounted(() => {
  reset();
  const id = proxy?.$route.query.id;

  if (id !== undefined) {
    title.value = '编辑';
    getQuestion(id).then((res) => {
      if (res.code === 200) {
        // 格式化
        const tempForm: QuestionForm = { ...res.data };
        tempForm.carType = tempForm.carType?.split(',');
        tempForm.subject = tempForm.subject?.split(',');
        tempForm.chapter = tempForm.chapter?.split(',');
        tempForm.special = tempForm.special?.split(',');
        tempForm.options = JSON.parse(tempForm.options);

        // 多选时才转数组
        tempForm.answer = tempForm.type === 'M' ? tempForm.answer?.split(',') : tempForm.answer;

        form.value = { ...tempForm };
      }
    });
  }
});
</script>
<style>
.question-option-label {
  margin-top: 10px;
  margin-bottom: 10px !important;
  width: 90%;
}

.question-option-content-input {
  margin-left: 8px;
  width: 60%;
}

.question-option-remove {
  margin-left: 20px;
}

.editor-img-uploader {
  display: none;
}
.editor,
.ql-toolbar {
  white-space: pre-wrap !important;
  line-height: normal !important;
}
.quill-img {
  display: none;
}
.ql-snow .ql-tooltip[data-mode='link']::before {
  content: '请输入链接地址:';
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0;
  content: '保存';
  padding-right: 0;
}
.ql-snow .ql-tooltip[data-mode='video']::before {
  content: '请输入视频地址:';
}
.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: '14px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='small']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='small']::before {
  content: '10px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='large']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='large']::before {
  content: '18px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='huge']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='huge']::before {
  content: '32px';
}
.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: '文本';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='1']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {
  content: '标题1';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='2']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before {
  content: '标题2';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='3']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before {
  content: '标题3';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='4']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before {
  content: '标题4';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='5']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before {
  content: '标题5';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='6']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before {
  content: '标题6';
}
.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: '标准字体';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='serif']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='serif']::before {
  content: '衬线字体';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='monospace']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='monospace']::before {
  content: '等宽字体';
}
</style>
