const { readFileSync } = require('fs');
const path = require('path');

// 读取自动导入的全局变量配置
let autoImportGlobals = {};
try {
  const autoImportContent = readFileSync(path.join(__dirname, '.eslintrc-auto-import.json'), 'utf8');
  autoImportGlobals = JSON.parse(autoImportContent);
} catch (error) {
  console.warn('无法读取自动导入配置文件:', error.message);
}

module.exports = [
  {
    ignores: [
      'dist/**',
      'out/**',
      'node_modules/**',
      '*.min.js',
      'public/**',
      '.vscode/**',
      '.idea/**',
      '**/*.d.ts',
      '**/*.ts',
      '**/*.tsx',
      '**/*.vue',
      'src/renderer/vite/**',
      'src/types/**',
      'electron.vite.config.ts',
      'uno.config.ts'
    ]
  },
  {
    files: ['**/*.{js,jsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      sourceType: 'module',
      globals: {
        ...autoImportGlobals.globals,
        DialogOption: 'readonly',
        OptionType: 'readonly',
        process: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        global: 'readonly',
        window: 'readonly',
        document: 'readonly',
        console: 'readonly'
      }
    },
    rules: {
      'no-console': 'off',
      'no-debugger': 'warn',
      'no-unused-vars': 'off',
      'prefer-rest-params': 'off'
    }
  }
];