<template>
  <div class="m-4">
    <el-cascader
      :model-value="value"
      :options="options"
      :props="{ expandTrigger: expandTrigger }"
      :placeholder="placeholder"
      :disabled="disabled"
      :clearable="clearable"
      :separator="separator"
      style="width: 300px"
      @change="handleChange"
    />
  </div>
</template>
<script setup name="AreaCascader" lang="ts">
import { listAreaCascader } from '@/api/scjk/permit/area';
import { CascaderVO } from '@/api/common/types';

const props = defineProps({
  // 次级菜单的展开方式
  expandTrigger: {
    type: String,
    default: 'hover'
  },
  // 选中值/默认值
  value: {
    type: Array<string>,
    default: []
  },
  // 输入框占位文本
  placeholder: {
    type: String,
    default: '请选择'
  },
  // 禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否支持清空选项
  clearable: {
    type: Boolean,
    default: false
  },
  // 用于分隔选项的字符
  separator: {
    type: String,
    default: '-'
  }
});

const emit = defineEmits(['update:modelValue']);
const options = ref<CascaderVO[]>([]);

const handleChange = (value: Array<string>) => {
  emit('update:modelValue', value);
};

onMounted(() => {
  listAreaCascader().then((res) => {
    if (res.code == 200 && res.data.length > 0) {
      options.value = [...res.data];
    }
  });
});
</script>
