<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="驾校全称" prop="schoolFullname">
            <el-input v-model="queryParams.schoolFullname" placeholder="请输入驾校全称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="驾校简称" prop="schoolShortname">
            <el-input v-model="queryParams.schoolShortname" placeholder="请输入驾校简称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="联系人" prop="contact">
            <el-input v-model="queryParams.contact" placeholder="请输入联系人" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="联系人电话" prop="phoneNumber">
            <el-input v-model="queryParams.phoneNumber" placeholder="请输入联系人电话" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="所属区域名称" prop="locationAreaName">
            <el-input
              v-model="queryParams.locationAreaName"
              placeholder="请输入所属区域名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:drivingSchool:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:drivingSchool:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:drivingSchool:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:drivingSchool:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="drivingSchoolList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="false" label="主键" align="center" prop="id" />
        <el-table-column label="驾校全称" align="center" prop="schoolFullname" />
        <el-table-column label="联系人" align="center" prop="contact" />
        <el-table-column label="联系人电话" align="center" prop="phoneNumber" />
        <el-table-column label="所属区域名称" align="center" prop="locationAreaName" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['scjk:drivingSchool:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['scjk:drivingSchool:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改驾校信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="960px" append-to-body>
      <el-form ref="drivingSchoolFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="驾校全称" prop="schoolFullname">
          <el-input v-model="form.schoolFullname" placeholder="请输入驾校全称" style="width: 240px" />
        </el-form-item>
        <el-form-item label="驾校简称" prop="schoolShortname">
          <el-input v-model="form.schoolShortname" placeholder="请输入驾校简称" style="width: 240px" />
        </el-form-item>
        <el-form-item label="驾校资质" prop="schoolQuality">
          <file-upload v-model="form.schoolQuality" />
        </el-form-item>
        <el-form-item label="联系人" prop="contact">
          <el-input v-model="form.contact" placeholder="请输入联系人" style="width: 240px" :suffix-icon="Avatar" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入联系人电话" :suffix-icon="Phone" style="width: 240px" />
        </el-form-item>
        <el-form-item label="所属区域" prop="locationAreaCode">
          <el-cascader
            ref="cascaderAddr"
            v-model="form.locationAreaCode"
            :options="areaList"
            placeholder="请选择所属区域"
            clearable
            @change="selectItem"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="教学经验">
          <editor v-model="form.teachingExperience" :min-height="192" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" maxlength="200" placeholder="请输入" show-word-limit type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DrivingSchool" lang="ts">
import { CascaderVO } from '@/api/common/types';
import { listDrivingSchool, getDrivingSchool, delDrivingSchool, addDrivingSchool, updateDrivingSchool } from '@/api/scjk/drivingSchool';
import { DrivingSchoolVO, DrivingSchoolQuery, DrivingSchoolForm } from '@/api/scjk/drivingSchool/types';
import { listAreaCascader } from '@/api/scjk/permit/area';
import { ref } from 'vue';
import { Phone, Avatar } from '@element-plus/icons-vue';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const drivingSchoolList = ref<DrivingSchoolVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const areaList = ref<CascaderVO[]>([]);
const cascaderAddr = ref<any>(null);

const queryFormRef = ref<ElFormInstance>();
const drivingSchoolFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: DrivingSchoolForm = {
  id: undefined,
  schoolFullname: undefined,
  schoolShortname: undefined,
  schoolQuality: undefined,
  teachingExperience: undefined,
  remark: undefined,
  contact: undefined,
  phoneNumber: undefined,
  locationProvince: undefined,
  locationCity: undefined,
  locationCountry: undefined,
  locationAreaName: undefined,
  locationAreaCode: undefined
};
const data = reactive<PageData<DrivingSchoolForm, DrivingSchoolQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    schoolFullname: undefined,
    schoolShortname: undefined,
    contact: undefined,
    phoneNumber: undefined,
    locationAreaName: undefined,
    params: {}
  },
  rules: {
    schoolFullname: [{ required: true, message: '驾校全称不能为空', trigger: 'blur' }],
    schoolShortname: [{ required: true, message: '驾校简称不能为空', trigger: 'blur' }],
    schoolQuality: [{ required: true, message: '驾校资质不能为空', trigger: 'blur' }],
    teachingExperience: [{ required: true, message: '教学经验不能为空', trigger: 'blur' }],
    contact: [{ required: true, message: '联系人不能为空', trigger: 'blur' }],
    phoneNumber: [{ required: true, message: '联系人电话不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询驾校信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDrivingSchool(queryParams.value);
  drivingSchoolList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  drivingSchoolFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: DrivingSchoolVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加驾校信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: DrivingSchoolVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getDrivingSchool(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改驾校信息';
};

/** 提交按钮 */
const submitForm = () => {
  drivingSchoolFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateDrivingSchool(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addDrivingSchool(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: DrivingSchoolVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除驾校信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delDrivingSchool(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/drivingSchool/export',
    {
      ...queryParams.value
    },
    `drivingSchool_${new Date().getTime()}.xlsx`
  );
};

/** 查询行政区划列表 */
const getAreaList = async () => {
  const res = await listAreaCascader();
  areaList.value = res.data;
};
const selectItem = (value: Array<string>) => {
  form.value.locationProvince = value[0];
  form.value.locationCity = value[1];
  form.value.locationCountry = value[2];
  const AreaText = cascaderAddr.value.getCheckedNodes()[0].text.replace(/\s+/g, '');
  form.value.locationAreaName = AreaText;
};

onMounted(() => {
  getList();
  getAreaList();
});
</script>
