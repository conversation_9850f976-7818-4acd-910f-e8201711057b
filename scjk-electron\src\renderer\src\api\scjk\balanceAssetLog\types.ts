export interface BalanceAssetLogVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 用户
   */
  userId: string | number;

  /**
   * 变动类型(1:支出;2:收入)
   */
  changesType: string;

  /**
   * 变动金额
   */
  amount: number;

  /**
   * 邀请人编码
   */
  userCode: string;
}

export interface BalanceAssetLogForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 用户
   */
  userId?: string | number;

  /**
   * 变动类型(1:支出;2:收入)
   */
  changesType?: string;

  /**
   * 变动金额
   */
  amount?: number;

  /**
   * 邀请人编码
   */
  userCode?: string;
}

export interface BalanceAssetLogQuery extends PageQuery {
  /**
   * 用户
   */
  userId?: string | number;

  /**
   * 变动类型(1:支出;2:收入)
   */
  changesType?: string;

  /**
   * 变动金额
   */
  amount?: number;

  /**
   * 邀请人编码
   */
  userCode?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
