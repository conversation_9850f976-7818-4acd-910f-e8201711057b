<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="车型" prop="carType">
            <car-type v-model="queryParams.carType" clearable placeholder="请选择车型" @keyup.enter="handleQuery"></car-type>
          </el-form-item>
          <el-form-item label="科目" prop="subject">
            <subject v-model="queryParams.subject" clearable placeholder="请选择科目" @keyup.enter="handleQuery"></subject>
          </el-form-item>
          <el-form-item label="章节" prop="chapter">
            <chapter v-model="queryParams.chapter" clearable placeholder="请选择章节" @keyup.enter="handleQuery"></chapter>
          </el-form-item>
          <el-form-item label="题目" prop="question">
            <el-input v-model="queryParams.question" placeholder="请输入题目" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="原ID" prop="oldId">
            <el-input v-model="queryParams.oldId" placeholder="请输入原ID" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:questionOld:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col> -->
          <!-- <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:questionOld:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col> -->
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:questionOld:export']" type="warning" plain icon="Upload" @click="handleImport">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button plain icon="DocumentCopy" @click="handleRecover()">恢复试卷</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="questionOldList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="车型" align="center" prop="carType" />
        <el-table-column label="科目" align="center" prop="subject" />
        <el-table-column label="章节" align="center" prop="chapter" />
        <el-table-column label="题型" align="center" prop="type">
          <template #default="scope">
            <dict-tag :options="question_type" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="题目" align="center" prop="question" />
        <el-table-column label="题目图片" align="center" prop="questionImage" width="100">
          <template #default="scope">
            <ImagePreview
              v-if="scope.row.questionImage"
              :width="100"
              :height="100"
              :src="scope.row.questionImage"
              :preview-src="[scope.row.questionImage]"
            />
          </template>
        </el-table-column>
        <el-table-column label="原ID" align="center" prop="oldId" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['scjk:questionOld:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <!-- <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['scjk:questionOld:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <i-ep-upload-filled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="text-center el-upload__tip">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="QuestionOld" lang="ts">
import { listQuestionOld, delQuestionOld, recoverPaper } from '@/api/scjk/questionbank/questionOld';
import { QuestionOldVO, QuestionOldQuery, QuestionOldForm } from '@/api/scjk/questionbank/questionOld/types';
import { globalHeaders } from '@/utils/request';
import CarType from '@/components/Scjk/CarType/index.vue';
import Chapter from '@/components/Scjk/Chapter/index.vue';
import Subject from '@/components/Scjk/Subject/index.vue';
import ImagePreview from '@/components/Scjk/ImagePreview/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { question_type } = toRefs<any>(proxy?.useDict('question_type'));

const questionOldList = ref<QuestionOldVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const data = reactive<PageData<QuestionOldForm, QuestionOldQuery>>({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    carType: undefined,
    subject: undefined,
    chapter: undefined,
    type: undefined,
    question: undefined,
    oldId: undefined,
    params: {}
  },
  rules: {}
});

const { queryParams } = toRefs(data);

const uploadRef = ref<ElUploadInstance>();

/*** 用户导入参数 */
const upload = reactive<ImportOption>({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: globalHeaders(),
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/scjk/questionOld/importData'
});

/** 查询题库(老)列表 */
const getList = async () => {
  loading.value = true;
  const res = await listQuestionOld(queryParams.value);
  questionOldList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: QuestionOldVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: QuestionOldVO) => {
  const id = row?.id || ids.value[0];
  proxy?.$router.push({ path: '/questionbank/questionOldEdit', query: { id: id } });
};

/** 删除按钮操作 */
const handleDelete = async (row?: QuestionOldVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除题库(老)编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delQuestionOld(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = '题库导入';
  upload.open = true;
};

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.handleRemove(file);
  ElMessageBox.alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', {
    dangerouslyUseHTMLString: true
  });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit();
}
/**
 * 恢复试卷
 */
const handleRecover = async () => {
  await recoverPaper();
  proxy?.$modal.msgSuccess('恢复成功');
};
onMounted(() => {
  getList();
});
</script>
