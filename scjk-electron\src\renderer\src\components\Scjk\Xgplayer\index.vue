<template>
  <div :id="`${playerId}`" />
</template>
<script setup lang="ts">
import Player from 'xgplayer';
import FlvPlugin from 'xgplayer-flv';
import HlsPlugin from 'xgplayer-hls';
import Mp4Plugin from 'xgplayer-mp4';
import MusicPreset from 'xgplayer-music';
import 'xgplayer/dist/index.min.css';
import { ref, watch, onMounted, onUnmounted } from 'vue';

interface propsType {
  type: 'audio' | 'video';
  playerId?: string;
  width?: number;
  height?: number;
  url: string;
  plugin?: 'flv' | 'hls' | 'mp4';
  fitVideoSize?: 'fixed' | 'fixWidth' | 'fixHeight' | undefined;
  playsinline: boolean;
  autoplay: boolean;
}

const props = withDefaults(defineProps<propsType>(), {
  type: 'video',
  playerId: 'playerContainer',
  width: 640,
  height: 320,
  url: '',
  plugin: 'hls',
  fitVideoSize: 'fixWidth',
  playsinline: true,
  autoplay: false
});
const player = ref<any>(null);
const clientWidth = ref<number>(1920);
const clientHeight = ref<number>(1080);

onMounted(() => {
  init();
  clientWidth.value = document.body.clientWidth;
  clientHeight.value = document.body.clientHeight;
  window.addEventListener(
    'resize',
    () => {
      clientWidth.value = document.body.clientWidth;
      clientHeight.value = document.body.clientHeight;
      init();
    },
    false
  );
});
watch(
  () => props.url,
  () => {
    init();
  },
  { deep: true }
);
const getPlugins = () => {
  let plugins = [Mp4Plugin];
  switch (props.plugin) {
    case 'hls':
      // @ts-expect-error version报错
      plugins = [HlsPlugin];
      break;
    case 'flv':
      // @ts-expect-error version报错
      plugins = [FlvPlugin];
      break;
    default:
      plugins = [Mp4Plugin];
      break;
  }
  return plugins;
};
const init = async () => {
  if (props.type === 'audio') {
    player.value = new Player({
      id: props.playerId,
      mediaType: 'audio',
      volume: 0.8,
      height: 50,
      width: props.width * (clientWidth.value / 1920),
      url: props.url,
      controls: {
        initShow: true,
        mode: 'flex',
        autoHide: false
      },
      ignores: ['playbackrate'],
      videoConfig: {
        crossOrigin: 'anonymous'
      },
      presets: ['default']
      // preset: [MusicPreset] // 如果要同时使用默认preset，那么配置是['default', MusicPreset]
    });
  } else {
    player.value = new Player({
      id: props.playerId,
      url: props.url,
      autoplay: props.autoplay,
      plugins: await getPlugins(),
      fitVideoSize: props.fitVideoSize,
      height: props.height * (clientHeight.value / 1080),
      width: props.width * (clientWidth.value / 1920),
      lang: 'zh-cn'
    });
  }
};

const playerIns = () => {
  return player.value;
};

defineExpose({
  playerIns
});

/**
 * 销毁播放器
 */
onUnmounted(() => {
  player.value.destroy();
});
</script>
