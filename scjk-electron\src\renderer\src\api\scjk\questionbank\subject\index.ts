import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SubjectVO, SubjectForm, SubjectQuery } from '@/api/scjk/questionbank/subject/types';
import { SelectVO } from '@/api/common/types';

/**
 * 查询科目列表
 * @param query
 * @returns {*}
 */

export const listSubject = (query?: SubjectQuery): AxiosPromise<SubjectVO[]> => {
  return request({
    url: '/scjk/subject/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询科目详细
 * @param id
 */
export const getSubject = (id: string | number): AxiosPromise<SubjectVO> => {
  return request({
    url: '/scjk/subject/' + id,
    method: 'get'
  });
};

/**
 * 新增科目
 * @param data
 */
export const addSubject = (data: SubjectForm) => {
  return request({
    url: '/scjk/subject',
    method: 'post',
    data: data
  });
};

/**
 * 修改科目
 * @param data
 */
export const updateSubject = (data: SubjectForm) => {
  return request({
    url: '/scjk/subject',
    method: 'put',
    data: data
  });
};

/**
 * 删除科目
 * @param id
 */
export const delSubject = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/subject/' + id,
    method: 'delete'
  });
};

/**
 * 车型select列表
 * @param query
 * @returns {*}
 */

export const listSelect = (): AxiosPromise<SelectVO[]> => {
  return request({
    url: '/scjk/subject/selectList',
    method: 'get'
  });
};

/**
 * 科目列表
 * @returns
 */
export const subjects = (): AxiosPromise<SubjectVO[]> => {
  return request({
    url: '/scjk/subject/subjects',
    method: 'get'
  });
};
