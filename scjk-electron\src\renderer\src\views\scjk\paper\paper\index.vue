<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="名称" prop="name">
            <el-input v-model="queryParams.name" placeholder="请输入名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="分类" prop="category">
            <el-select v-model="queryParams.category" style="width: 240px" placeholder="请选择分类" clearable>
              <el-option v-for="dict in paper_category" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="车型" prop="carType">
            <car-type v-model="queryParams.carType" clearable placeholder="请选择车型" @keyup.enter="handleQuery"></car-type>
          </el-form-item>
          <el-form-item label="科目" prop="subject">
            <subject v-model="queryParams.subject" clearable placeholder="请选择科目" @keyup.enter="handleQuery"></subject>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" style="width: 240px" placeholder="请选择状态" clearable>
              <el-option v-for="dict in paper_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:paper:save']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:paper:save']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()">修改</el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:paper:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:paper:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="paperList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="true" label="ID" align="center" prop="id" />
        <el-table-column label="名称" align="center" prop="name" />
        <el-table-column label="分类" align="center" prop="category">
          <template #default="scope">
            <dict-tag :options="paper_category" :value="scope.row.category" />
          </template>
        </el-table-column>
        <el-table-column label="车型" align="center" prop="carTypeName" />
        <el-table-column label="科目" align="center" prop="subjectName" />
        <el-table-column label="满分(分)" align="center" prop="full" />
        <el-table-column label="及格(分)" align="center" prop="pass" />
        <el-table-column label="时间(分钟)" align="center" prop="time" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="paper_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="sort" width="160">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.sort"
              :min="1"
              :step="1"
              controls-position="right"
              style="width: 110px"
              @change="(current) => handleUpdateSort(scope.row, current)"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding" width="150">
          <template #default="scope">
            <template v-if="scope.row.status === 'A'">
              <el-tooltip content="禁用" placement="top">
                <el-button v-hasPermi="['scjk:paper:save']" link type="primary" icon="TurnOff" @click="handleChangeStatus(scope.row)"></el-button>
              </el-tooltip>
            </template>
            <template v-else>
              <el-tooltip content="修改" placement="top">
                <el-button v-hasPermi="['scjk:paper:save']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button v-hasPermi="['scjk:paper:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="启用" placement="top">
                <el-button v-hasPermi="['scjk:paper:save']" link type="primary" icon="Open" @click="handleChangeStatus(scope.row)"></el-button>
              </el-tooltip>
            </template>
            <el-tooltip content="复制" placement="top">
              <el-button v-hasPermi="['scjk:paper:copy']" link type="primary" icon="CopyDocument" @click="handleCopy(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改试卷对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="900px" append-to-body>
      <el-form ref="paperFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item
          ><el-icon color="red"><WarnTriangleFilled /></el-icon><span style="color: red">注意：每题默认1分!!!</span>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-radio-group v-model="form.category">
            <el-radio v-for="ct in paper_category" :key="ct.value" :value="ct.value">{{ ct.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="车型" prop="carType">
          <car-type v-model="form.carType" clearable placeholder="请选择车型"></car-type>
        </el-form-item>
        <el-form-item label="科目" prop="subject">
          <subject v-model="form.subject" clearable placeholder="请选择科目"></subject>
        </el-form-item>
        <el-form-item label="时间" prop="time"> <el-input-number v-model="form.time" :min="1" :step="1"></el-input-number>&nbsp;分钟 </el-form-item>
        <!-- 试题-->
        <el-form-item label="试题" prop="questions">
          <el-row style="width: 100%">
            <el-col :span="21"><el-button type="primary" @click="addQuestion">选择试题</el-button></el-col>
            <el-col :span="3"><el-button v-if="hasQuestions" type="danger" @click="emptyQuestions">清空</el-button></el-col>
          </el-row>

          <el-row style="margin-top: 10px">
            <el-col>
              <div class="sort">
                <div class="q-wrap">
                  <li v-for="question of form.questions" :key="question.id" :data-order="question.sort" class="qn">
                    <p>{{ question.sort }}</p>
                  </li>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-collapse style="width: 100%; margin-top: 10px">
            <el-collapse-item>
              <template #title>
                预览-[<span style="color: red">{{ form.questions.length }}</span
                >]
              </template>
              <el-card style="width: 100%">
                <el-card v-for="question of form.questions" :key="question.id" style="margin-bottom: 10px; width: 100%">
                  <p>
                    <el-row>
                      <el-col :span="22"
                        ><span class="question-title">顺序：</span>
                        <el-input-number
                          v-model="question.sort"
                          :min="1"
                          :step="1"
                          :max="999"
                          controls-position="right"
                          style="width: 90px"
                          @change="resortQuestions"
                        ></el-input-number>
                      </el-col>
                      <el-col :span="2"><el-button type="warning" :icon="Delete" @click="removeQuestion(question)"></el-button></el-col>
                    </el-row>
                  </p>
                  <p>
                    <span class="question-title">题目：</span>（<dict-tag
                      :options="question_type"
                      :value="question.type"
                      style="display: inline-block"
                    />题）{{ question.title }}
                  </p>
                  <p v-if="question.image">
                    <image-preview :src="question.image" :width="100" :height="100"></image-preview>
                  </p>

                  <p>
                    <span class="question-title">选项：</span>
                    <template v-for="option of JSON.parse(question.options)" :key="option.prefix">
                      <span>{{ option.prefix }}：{{ option.content }}&nbsp;&nbsp;&nbsp; </span>
                    </template>
                  </p>
                  <p><span class="question-title">答案：</span>{{ question.answer }}</p>
                </el-card>
              </el-card>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!--选择试题弹窗-->
    <question v-model:visible="questionDialogVisible" title="选择试卷" :width="900" @selected="selectedQuestions"></question>

    <el-backtop :right="100" :bottom="100" />
  </div>
</template>

<script setup name="Paper" lang="ts">
import { listPaper, delPaper, switchStatus, copy, updateSort, getPaper, savePaper } from '@/api/scjk/paper/paper';
import { PaperVO, PaperQuery, PaperForm } from '@/api/scjk/paper/paper/types';
import { QuestionVO } from '@/api/scjk/questionbank/question/types';

import CarType from '@/components/Scjk/CarType/index.vue';
import Subject from '@/components/Scjk/Subject/index.vue';
import Question from '@/components/Scjk/Question/index.vue';
import ImagePreview from '@/components/Scjk/ImagePreview/index.vue';
import { Delete } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { paper_category, paper_status, question_type } = toRefs<any>(proxy?.useDict('paper_category', 'paper_status', 'question_type'));

const paperList = ref<PaperVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const paperFormRef = ref<ElFormInstance>();
const queryFormRef = ref<ElFormInstance>();
const queryParams = ref<PaperQuery>({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  category: undefined,
  carType: undefined,
  subject: undefined,
  score: undefined,
  time: undefined,
  status: undefined,
  params: {}
});

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData = {
  id: undefined,
  name: undefined,
  category: 'B',
  carType: undefined,
  subject: undefined,
  full: 0,
  pass: 0,
  time: 45, // 默认45分钟
  status: 'D',
  sort: 1,
  questions: []
};

const form = ref<PaperForm>({ ...initFormData });

const rules = ref<ElFormRules>({
  id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  category: [{ required: true, message: '分类不能为空', trigger: 'blur' }],
  carType: [{ required: true, message: '车型不能为空', trigger: 'blur' }],
  subject: [{ required: true, message: '科目不能为空', trigger: 'blur' }],
  time: [{ required: true, message: '时间不能为空', trigger: 'blur' }]
});

/** 查询试卷列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPaper(queryParams.value);
  paperList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PaperVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  paperFormRef.value?.resetFields();
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加试卷';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PaperVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getPaper(_id);
  form.value = { ...res.data };
  dialog.visible = true;
  dialog.title = '修改试卷';
};

/** 删除按钮操作 */
const handleDelete = async (row?: PaperVO) => {
  await proxy?.$modal.confirm('是否确认删除试卷[' + row.name + ']？').finally(() => (loading.value = false));
  await delPaper(row?.id);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/**
 * 切换状态
 * @param row
 */
const handleChangeStatus = async (row?: PaperVO) => {
  await proxy?.$modal.confirm('是否确认修改试卷[' + row.name + ']的状态？').finally(() => (loading.value = false));
  await switchStatus(row?.id);
  proxy?.$modal.msgSuccess('修改成功');
  await getList();
};

/**
 * 拷贝
 * @param row
 */
const handleCopy = async (row?: PaperVO) => {
  await proxy?.$modal.confirm('是否确认复制试卷[' + row.name + ']？').finally(() => (loading.value = false));
  await copy(row?.id);
  proxy?.$modal.msgSuccess('复制成功');
  await getList();
};

/**
 * 更新排序
 * @param row
 */
const handleUpdateSort = async (row?: PaperVO, currentValue: number) => {
  await updateSort(row?.id, currentValue);
  proxy?.$modal.msgSuccess('修改排序成功');
  // await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/paper/export',
    {
      ...queryParams.value
    },
    `试卷_${new Date().getTime()}.xlsx`
  );
};

/**
 * 是否有试题
 */
const hasQuestions = computed(() => form.value.questions.length > 0);

/**
 * 试题弹窗
 */
const questionDialogVisible = ref<boolean>(false);

/** 选择试题钮操作 */
const addQuestion = () => {
  questionDialogVisible.value = true;
};

/**
 * 选择试题
 * @param question
 */
const selectedQuestions = (questions: Array<QuestionVO>, edit = false) => {
  // if (questions.length < 1) {
  //   return;
  // }

  let formateQuestions = [];
  // try {
  formateQuestions = questions.map((question) => {
    // 默认1分
    return edit ? question : { ...question, paper: undefined, question: question.id, title: question.question, val: 1 };
  });
  // } catch (e) {
  //   console.error(e);
  //   proxy?.$modal.msgError('选择试题失败' + e);
  //   return;
  // }

  // if (formateQuestions.length < 1) {
  //   proxy?.$modal.msgError('选择试题内容有误！请检查');
  //   return;
  // }

  // 按题型分组，并去重
  const map = new Map();
  for (const question of form.value.questions) {
    map.set(question.id);
  }

  let repeatCount = 0;
  const mergeQuestions = formateQuestions.reduce((previous, current) => {
    if (map.has(current.id)) {
      repeatCount++;
      return previous;
    }

    current.sort = previous.length + 1;
    map.set(current.id);
    previous.push(current);
    return previous;
  }, form.value.questions);

  if (repeatCount > 0) {
    proxy.$modal.alertError(`选择试题[${questions.length}]道，其中重复[${repeatCount}]道！`);
  }

  form.value.questions = mergeQuestions;
};

/**
 * 清空试题
 */
const emptyQuestions = async () => {
  await proxy?.$modal.confirm('确定要清空所有试题吗？');
  form.value.questions = [];

  proxy?.$modal.msgSuccess('已清空');
};

/**
 * 删除试题
 */
const removeQuestion = async (question: QuestionVO) => {
  await proxy?.$modal.confirm('确定要删除试题吗？');
  form.value.questions = form.value.questions.filter((item) => item.id !== question.id);
  proxy?.$modal.msgSuccess('已删除');
};

/**
 * 重新排序
 */
const resortQuestions = () => {
  form.value.questions = form.value.questions.sort((a, b) => a.sort - b.sort);
};

/** 提交按钮 */
const submitForm = () => {
  paperFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      await savePaper(form.value).finally(() => (buttonLoading.value = false));
      proxy?.$modal.msgSuccess('操作成功');
      reset();
      dialog.visible = false;
      // await getList();
    }
  });
};

onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.question-title {
  font-weight: 700;
}

li {
  list-style: none;
}

p {
  margin: 0;
  padding: 0;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    \\5FAE\8F6F\96C5\9ED1,
    Arial,
    sans-serif !important;
}

.sort {
  width: 400px;
}
.sort .q-wrap {
  overflow: hidden;
}
.sort .q-wrap li {
  width: 40px;
  height: 40px;
  color: #4a4a4a;
  font-size: 13px;
  border: 1px solid #eee;
  text-align: center;
  float: left;
  box-sizing: border-box;
  cursor: pointer;
}
</style>
