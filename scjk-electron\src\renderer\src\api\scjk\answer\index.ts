import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { AnswerQuery, AnswerVO } from '@/api/scjk/answer/types';

/**
 * 答题记录分组列表
 * @param query
 * @returns {*}
 */

export const list = (query?: AnswerQuery): AxiosPromise<AnswerVO[]> => {
  return request({
    url: '/scjk/answerError/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询答题记录详细
 * @param id
 */
export const getAnswer = (id: string | number): AxiosPromise<AnswerVO> => {
  return request({
    url: '/scjk/answerError/' + id,
    method: 'get'
  });
};

/**
 * 所有答题记录列表
 * @param query
 * @returns {*}
 */

export const all = (): AxiosPromise<AnswerVO[]> => {
  return request({
    url: '/scjk/answerError/all',
    method: 'get'
  });
};

/**
 * 添加正确答题记录
 * @param id
 * @param answerError
 */
export const addAnswerRight = (id, answerError) => {
  return request({
    url: '/scjk/answerError/add',
    method: 'get',
    params: {
      questionId: id,
      answerError: answerError,
      right: 1
    }
  });
};

/**
 * 添加错误答题记录
 * @param id
 * @param answerError
 */
export const addAnswerError = (id, answerError) => {
  return request({
    url: '/scjk/answerError/add',
    method: 'get',
    params: {
      questionId: id,
      answerError: answerError,
      right: 0
    }
  });
};

/**
 * 答题记录明细
 * @param query
 * @returns {*}
 */

export const answerByGroup = (name: string, carType: string | number, subject: string | number): AxiosPromise<AnswerVO[]> => {
  return request({
    url: '/scjk/answerError/answerByGroup',
    method: 'get',
    params: {
      name: name,
      carType: carType,
      subject: subject
    }
  });
};

/**
 * 批量添加
 * @param answers
 * @returns
 */
export const addBatch = (answers: Array<AnswerVO>) => {
  return request({
    url: '/scjk/answerError/addBatch',
    method: 'post',
    data: { answers: answers }
  });
};
