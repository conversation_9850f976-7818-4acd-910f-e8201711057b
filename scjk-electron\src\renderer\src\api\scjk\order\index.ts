import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { OrderVO, OrderForm, OrderQuery } from '@/api/scjk/order/types';

/**
 * 查询订单管理列表
 * @param query
 * @returns {*}
 */

export const listOrder = (query?: OrderQuery): AxiosPromise<OrderVO[]> => {
  return request({
    url: '/scjk/order/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询订单管理详细
 * @param id
 */
export const getOrder = (id: string | number): AxiosPromise<OrderVO> => {
  return request({
    url: '/scjk/order/' + id,
    method: 'get'
  });
};

/**
 * 查询订单管理详细
 * @param orderNo
 */
export const getOrderByOrderNo = (orderNo: string | number): AxiosPromise<OrderVO> => {
  return request({
    url: '/scjk/order/orderInfo/' + orderNo,
    method: 'get'
  });
};

/**
 * 新增订单管理
 * @param data
 */
export const addOrder = (data: OrderForm) => {
  return request({
    url: '/scjk/order',
    method: 'post',
    data: data
  });
};

/**
 * 修改订单管理
 * @param data
 */
export const updateOrder = (data: OrderForm) => {
  return request({
    url: '/scjk/order',
    method: 'put',
    data: data
  });
};

/**
 * 删除订单管理
 * @param id
 */
export const delOrder = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/order/' + id,
    method: 'delete'
  });
};
