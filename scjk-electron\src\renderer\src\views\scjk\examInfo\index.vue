<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="学员姓名" prop="studentName">
            <el-input v-model="queryParams.studentName" placeholder="请输入学员姓名" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="预约场次" prop="examTimes">
            <el-input v-model="queryParams.examTimes" placeholder="请输入预约场次" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="考试场地" prop="examLocation">
            <el-input v-model="queryParams.examLocation" placeholder="请输入考试场地" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="考试日期" prop="examDate">
            <el-date-picker v-model="queryParams.examDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择考试日期" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:examInfo:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:examInfo:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:examInfo:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:examInfo:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="examInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="false" label="主键" align="center" prop="id" />
        <el-table-column label="考试科目" align="center" prop="subjectName" />

        <el-table-column label="考试车型" align="center" prop="carType" />
        <el-table-column label="学员姓名" align="center" prop="studentName" />
        <el-table-column label="预约场次" align="center" prop="examTimes" width="240">
          <template #default="scope">
            <dict-tag :options="exam_item" :value="scope.row.examTimes" />
          </template>
        </el-table-column>
        <el-table-column label="考试场地" align="center" prop="examLocation" />
        <el-table-column label="考试日期" align="center" prop="examDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.examDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['scjk:examInfo:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['scjk:examInfo:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改考试预约信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="960px" append-to-body>
      <el-form ref="examInfoFormRef" :model="form" :rules="rules" style="max-width: 600px" label-width="80px">
        <el-form-item label="学员姓名" prop="studentName">
          <el-select v-model="form.studentName">
            <el-option v-for="item in studentList" :key="item.userId" :label="item.nickName" :value="item.userId" />
          </el-select>
        </el-form-item>
        <el-form-item label="考试科目" prop="examSubject">
          <subject v-model="form.examSubject" :multiple="false" clearable placeholder="请选择科目"></subject>
        </el-form-item>
        <el-form-item label="报考车型" prop="carType">
          <car-type v-model="form.carType" />
        </el-form-item>
        <el-form-item label="预约场次" prop="examTimes">
          <el-select v-model="form.examTimes" placeholder="请选择预约场次">
            <el-option v-for="dict in exam_item" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考试场地" prop="examLocation">
          <el-input v-model="form.examLocation" placeholder="请输入考试场地" />
        </el-form-item>
        <el-form-item label="考试日期" prop="examDate">
          <el-date-picker v-model="form.examDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择考试日期"> </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ExamInfo" lang="ts">
import { listExamInfo, getExamInfo, delExamInfo, addExamInfo, updateExamInfo } from '@/api/scjk/examInfo';
import { ExamInfoVO, ExamInfoQuery, ExamInfoForm } from '@/api/scjk/examInfo/types';
import { getMemberList } from '@/api/system/role';
import { UserVO } from '@/api/system/user/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { student_type, exam_item, exam_subject } = toRefs<any>(proxy?.useDict('student_type', 'exam_item', 'exam_subject'));

const examInfoList = ref<ExamInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const examInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const studentList = ref<UserVO[]>();

const initFormData: ExamInfoForm = {
  examSubject: undefined,
  carType: undefined,
  studentName: undefined,
  examReason: undefined,
  examTimes: undefined,
  // startDate: undefined,
  // endDate: undefined,
  examLocation: undefined,
  examDate: undefined,
  remark: undefined
};
const data = reactive<PageData<ExamInfoForm, ExamInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    examSubject: undefined,
    carType: undefined,
    studentName: undefined,
    examReason: undefined,
    examTimes: undefined,
    examLocation: undefined,
    examDate: undefined,
    params: {}
  },
  rules: {
    examSubject: [{ required: true, message: '考试科目不能为空', trigger: 'change' }],
    carType: [{ required: true, message: '考试车型不能为空', trigger: 'change' }],
    studentName: [{ required: true, message: '学员姓名不能为空', trigger: 'blur' }],
    examTimes: [{ required: true, message: '预约场次不能为空', trigger: 'blur' }],
    examLocation: [{ required: true, message: '考试场地不能为空', trigger: 'blur' }],
    examDate: [{ required: true, message: '考试日期不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询考试预约信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listExamInfo(queryParams.value);
  examInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  examInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ExamInfoVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加考试预约信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ExamInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getExamInfo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改考试预约信息';
};

/** 提交按钮 */
const submitForm = () => {
  examInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateExamInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addExamInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ExamInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除考试预约信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delExamInfo(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/examInfo/export',
    {
      ...queryParams.value
    },
    `examInfo_${new Date().getTime()}.xlsx`
  );
};
const getStudentList = async () => {
  queryParams.value.roleKey = 'student';
  const res = await getMemberList(queryParams.value);
  studentList.value = res.rows;
};

onMounted(() => {
  getList();
  getStudentList();
});
</script>
