import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PosterConfigVO, PosterConfigForm, PosterConfigQuery } from '@/api/system/posterConfig/types';

/**
 * 查询海报配置列表
 * @param query
 * @returns {*}
 */

export const listPosterConfig = (query?: PosterConfigQuery): AxiosPromise<PosterConfigVO[]> => {
  return request({
    url: '/system/posterConfig/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询海报配置详细
 * @param id
 */
export const getPosterConfig = (id: string | number): AxiosPromise<PosterConfigVO> => {
  return request({
    url: '/system/posterConfig/' + id,
    method: 'get'
  });
};

/**
 * 新增海报配置
 * @param data
 */
export const addPosterConfig = (data: PosterConfigForm) => {
  return request({
    url: '/system/posterConfig',
    method: 'post',
    data: data
  });
};

/**
 * 修改海报配置
 * @param data
 */
export const updatePosterConfig = (data: PosterConfigForm) => {
  return request({
    url: '/system/posterConfig',
    method: 'put',
    data: data
  });
};

/**
 * 删除海报配置
 * @param id
 */
export const delPosterConfig = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/posterConfig/' + id,
    method: 'delete'
  });
};
