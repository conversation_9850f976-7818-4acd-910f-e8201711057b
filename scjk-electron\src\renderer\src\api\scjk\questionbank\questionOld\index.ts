import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { QuestionOldVO, QuestionOldForm, QuestionOldQuery } from '@/api/scjk/questionbank/questionOld/types';

/**
 * 查询题库(老)列表
 * @param query
 * @returns {*}
 */

export const listQuestionOld = (query?: QuestionOldQuery): AxiosPromise<QuestionOldVO[]> => {
  return request({
    url: '/scjk/questionOld/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询题库(老)详细
 * @param id
 */
export const getQuestionOld = (id: string | number): AxiosPromise<QuestionOldVO> => {
  return request({
    url: '/scjk/questionOld/' + id,
    method: 'get'
  });
};

/**
 * 新增题库(老)
 * @param data
 */
export const addQuestionOld = (data: QuestionOldForm) => {
  return request({
    url: '/scjk/questionOld',
    method: 'post',
    data: data
  });
};

/**
 * 修改题库(老)
 * @param data
 */
export const updateQuestionOld = (data: QuestionOldForm) => {
  return request({
    url: '/scjk/questionOld',
    method: 'put',
    data: data
  });
};

/**
 * 删除题库(老)
 * @param id
 */
export const delQuestionOld = (id: string | number | Array<string | number>) => {
  return request({
    url: '/scjk/questionOld/' + id,
    method: 'delete'
  });
};

/**
 * 恢复试卷
 * @returns
 */
export const recoverPaper = () => {
  return request({
    url: '/scjk/questionOld/recoverPaper',
    method: 'post'
  });
};
