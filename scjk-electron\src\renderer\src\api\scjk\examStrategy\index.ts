import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ExamStrategyVO, ExamStrategyForm, ExamStrategyQuery } from '@/api/scjk/examStrategy/types';

/**
 * 查询考试攻略配置列表
 * @param query
 * @returns {*}
 */

export const listExamStrategy = (query?: ExamStrategyQuery): AxiosPromise<ExamStrategyVO[]> => {
  return request({
    url: '/examStrategy/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询考试攻略配置详细
 * @param id
 */
export const getExamStrategy = (id: string | number): AxiosPromise<ExamStrategyVO> => {
  return request({
    url: '/examStrategy/' + id,
    method: 'get'
  });
};

/**
 * 新增考试攻略配置
 * @param data
 */
export const addExamStrategy = (data: ExamStrategyForm) => {
  return request({
    url: '/examStrategy',
    method: 'post',
    data: data
  });
};

/**
 * 修改考试攻略配置
 * @param data
 */
export const updateExamStrategy = (data: ExamStrategyForm) => {
  return request({
    url: '/examStrategy',
    method: 'put',
    data: data
  });
};

/**
 * 删除考试攻略配置
 * @param id
 */
export const delExamStrategy = (id: string | number | Array<string | number>) => {
  return request({
    url: '/examStrategy/' + id,
    method: 'delete'
  });
};
