// electron.vite.config.ts
import { resolve } from "path";
import { defineConfig, externalizeDepsPlugin, loadEnv } from "electron-vite";

// src/renderer/vite/plugins/index.ts
import vue from "@vitejs/plugin-vue";

// src/renderer/vite/plugins/unocss.ts
import UnoCss from "unocss/vite";
var unocss_default = () => {
  return UnoCss({
    hmrTopLevelAwait: false
    // unocss默认是true，低版本浏览器是不支持的，启动后会报错
  });
};

// src/renderer/vite/plugins/auto-import.ts
import AutoImport from "unplugin-auto-import/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import IconsResolver from "unplugin-icons/resolver";
var __electron_vite_injected_dirname = "E:\\workspace\\scjk-5.2\\scjk-electron\\src\\renderer\\vite\\plugins";
var auto_import_default = (path3) => {
  return AutoImport({
    // 自动导入 Vue 相关函数
    imports: ["vue", "vue-router", "@vueuse/core", "pinia"],
    eslintrc: {
      enabled: false,
      filepath: "./.eslintrc-auto-import.json",
      globalsPropValue: true
    },
    resolvers: [
      // 自动导入 Element Plus 相关函数ElMessage, ElMessageBox... (带样式)
      ElementPlusResolver(),
      IconsResolver({
        prefix: "Icon"
      })
    ],
    vueTemplate: true,
    // 是否在 vue 模板中自动导入
    dts: path3.resolve(path3.resolve(__electron_vite_injected_dirname, "../../src"), "types", "auto-imports.d.ts")
  });
};

// src/renderer/vite/plugins/components.ts
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver as ElementPlusResolver2 } from "unplugin-vue-components/resolvers";
import IconsResolver2 from "unplugin-icons/resolver";
var __electron_vite_injected_dirname2 = "E:\\workspace\\scjk-5.2\\scjk-electron\\src\\renderer\\vite\\plugins";
var components_default = (path3) => {
  return Components({
    resolvers: [
      // 自动导入 Element Plus 组件
      ElementPlusResolver2(),
      // 自动注册图标组件
      IconsResolver2({
        enabledCollections: ["ep"]
      })
    ],
    dts: path3.resolve(path3.resolve(__electron_vite_injected_dirname2, "../../src"), "types", "components.d.ts")
  });
};

// src/renderer/vite/plugins/icons.ts
import Icons from "unplugin-icons/vite";
var icons_default = () => {
  return Icons({
    // 自动安装图标库
    autoInstall: true
  });
};

// src/renderer/vite/plugins/svg-icon.ts
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
var __electron_vite_injected_dirname3 = "E:\\workspace\\scjk-5.2\\scjk-electron\\src\\renderer\\vite\\plugins";
var svg_icon_default = (path3, isBuild) => {
  return createSvgIconsPlugin({
    // 指定需要缓存的图标文件夹
    iconDirs: [path3.resolve(path3.resolve(__electron_vite_injected_dirname3, "../../src"), "assets/icons/svg")],
    // 指定symbolId格式
    symbolId: "icon-[dir]-[name]",
    svgoOptions: isBuild
  });
};

// src/renderer/vite/plugins/compression.ts
import compression from "vite-plugin-compression";
var compression_default = (env) => {
  const { VITE_BUILD_COMPRESS } = env;
  const plugin = [];
  if (VITE_BUILD_COMPRESS) {
    const compressList = VITE_BUILD_COMPRESS.split(",");
    if (compressList.includes("gzip")) {
      plugin.push(
        compression({
          ext: ".gz",
          deleteOriginFile: false
        })
      );
    }
    if (compressList.includes("brotli")) {
      plugin.push(
        compression({
          ext: ".br",
          algorithm: "brotliCompress",
          deleteOriginFile: false
        })
      );
    }
  }
  return plugin;
};

// src/renderer/vite/plugins/setup-extend.ts
import setupExtend from "unplugin-vue-setup-extend-plus/vite";
var setup_extend_default = () => {
  return setupExtend({});
};

// src/renderer/vite/plugins/i18n.ts
import VueI18nPlugin from "@intlify/unplugin-vue-i18n/vite";
var __electron_vite_injected_dirname4 = "E:\\workspace\\scjk-5.2\\scjk-electron\\src\\renderer\\vite\\plugins";
var i18n_default = (path3) => {
  return VueI18nPlugin({
    include: [path3.resolve(__electron_vite_injected_dirname4, "../../src/lang/**.json")]
  });
};

// src/renderer/vite/plugins/index.ts
import path from "path";
var plugins_default = (viteEnv, isBuild = false) => {
  const vitePlugins = [];
  vitePlugins.push(vue());
  vitePlugins.push(unocss_default());
  vitePlugins.push(auto_import_default(path));
  vitePlugins.push(components_default(path));
  vitePlugins.push(compression_default(viteEnv));
  vitePlugins.push(icons_default());
  vitePlugins.push(svg_icon_default(path, isBuild));
  vitePlugins.push(setup_extend_default());
  vitePlugins.push(i18n_default(path));
  return vitePlugins;
};

// electron.vite.config.ts
import AutoImport2 from "unplugin-auto-import/vite";
import { ElementPlusResolver as ElementPlusResolver3 } from "unplugin-vue-components/resolvers";
import Components2 from "unplugin-vue-components/vite";
import IconsResolver3 from "unplugin-icons/resolver";
import path2 from "path";
var __electron_vite_injected_dirname5 = "E:\\workspace\\scjk-5.2\\scjk-electron";
var electron_vite_config_default = defineConfig(({ mode, command }) => {
  const env = loadEnv(mode);
  return {
    main: {
      plugins: [externalizeDepsPlugin()]
    },
    preload: {
      plugins: [externalizeDepsPlugin()]
    },
    renderer: {
      // 部署生产环境和开发环境下的URL。
      // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
      base: env.VITE_APP_CONTEXT_PATH,
      resolve: {
        alias: {
          "@renderer": resolve("src/renderer/src"),
          "~": path2.resolve(__electron_vite_injected_dirname5, "./src/renderer/"),
          "@": path2.resolve(__electron_vite_injected_dirname5, "./src/renderer/src/")
        },
        extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
      },
      build: {
        reportCompressedSize: false
      },
      plugins: [
        plugins_default(env, command === "build"),
        AutoImport2({
          resolvers: [
            ElementPlusResolver3({
              importStyle: "sass"
            })
          ],
          imports: ["vue", "vue-router"]
        }),
        Components2({
          resolvers: [
            ElementPlusResolver3({
              importStyle: "sass"
            }),
            // 自动注册图标组件
            IconsResolver3({
              enabledCollections: ["ep"]
            })
          ]
        })
      ],
      server: {
        host: "0.0.0.0",
        port: Number(env.VITE_APP_PORT),
        open: true,
        proxy: {
          [env.VITE_APP_BASE_API]: {
            target: "http://localhost:4025",
            changeOrigin: true,
            ws: true,
            rewrite: (path3) => path3.replace(new RegExp("^" + env.VITE_APP_BASE_API), "")
          }
        }
      },
      css: {
        preprocessorOptions: {
          scss: {
            javascriptEnabled: true,
            additionalData: `
              @use "@/assets/style/main.scss" as globalScss; @use "@/assets/style/element/index.scss" as *;
            `
          }
        },
        postcss: {
          plugins: [
            {
              postcssPlugin: "internal:charset-removal",
              AtRule: {
                charset: (atRule) => {
                  if (atRule.name === "charset") {
                    atRule.remove();
                  }
                }
              }
            }
          ]
        }
      },
      // 预编译
      optimizeDeps: {
        include: [
          "vue",
          "vue-router",
          "pinia",
          "axios",
          "@vueuse/core",
          "path-to-regexp",
          "echarts",
          "vue-i18n",
          "@vueup/vue-quill",
          "bpmn-js/lib/Viewer",
          "bpmn-js/lib/Modeler.js",
          "bpmn-js-properties-panel",
          "min-dash",
          "diagram-js/lib/navigation/movecanvas",
          "diagram-js/lib/navigation/zoomscroll",
          "bpmn-js/lib/features/palette/PaletteProvider",
          "bpmn-js/lib/features/context-pad/ContextPadProvider",
          "diagram-js/lib/draw/BaseRenderer",
          "tiny-svg",
          "image-conversion",
          "element-plus/es/components/text/style/css",
          "element-plus/es/components/collapse-item/style/css",
          "element-plus/es/components/collapse/style/css",
          "element-plus/es/components/space/style/css",
          "element-plus/es/components/container/style/css",
          "element-plus/es/components/aside/style/css",
          "element-plus/es/components/main/style/css",
          "element-plus/es/components/header/style/css",
          "element-plus/es/components/button-group/style/css",
          "element-plus/es/components/radio-button/style/css",
          "element-plus/es/components/checkbox-group/style/css",
          "element-plus/es/components/form/style/css",
          "element-plus/es/components/form-item/style/css",
          "element-plus/es/components/button/style/css",
          "element-plus/es/components/input/style/css",
          "element-plus/es/components/input-number/style/css",
          "element-plus/es/components/switch/style/css",
          "element-plus/es/components/upload/style/css",
          "element-plus/es/components/menu/style/css",
          "element-plus/es/components/col/style/css",
          "element-plus/es/components/icon/style/css",
          "element-plus/es/components/row/style/css",
          "element-plus/es/components/tag/style/css",
          "element-plus/es/components/dialog/style/css",
          "element-plus/es/components/loading/style/css",
          "element-plus/es/components/radio/style/css",
          "element-plus/es/components/radio-group/style/css",
          "element-plus/es/components/popover/style/css",
          "element-plus/es/components/scrollbar/style/css",
          "element-plus/es/components/tooltip/style/css",
          "element-plus/es/components/dropdown/style/css",
          "element-plus/es/components/dropdown-menu/style/css",
          "element-plus/es/components/dropdown-item/style/css",
          "element-plus/es/components/sub-menu/style/css",
          "element-plus/es/components/menu-item/style/css",
          "element-plus/es/components/divider/style/css",
          "element-plus/es/components/card/style/css",
          "element-plus/es/components/link/style/css",
          "element-plus/es/components/breadcrumb/style/css",
          "element-plus/es/components/breadcrumb-item/style/css",
          "element-plus/es/components/table/style/css",
          "element-plus/es/components/tree-select/style/css",
          "element-plus/es/components/table-column/style/css",
          "element-plus/es/components/select/style/css",
          "element-plus/es/components/option/style/css",
          "element-plus/es/components/pagination/style/css",
          "element-plus/es/components/tree/style/css",
          "element-plus/es/components/alert/style/css",
          "element-plus/es/components/checkbox/style/css",
          "element-plus/es/components/date-picker/style/css",
          "element-plus/es/components/transfer/style/css",
          "element-plus/es/components/tabs/style/css",
          "element-plus/es/components/image/style/css",
          "element-plus/es/components/tab-pane/style/css"
        ]
      }
    }
  };
});
export {
  electron_vite_config_default as default
};
