export interface QuestionVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 车型
   */
  carType: string;
  /**
   * 车型（多）
   */
  carTypeMul: Array<string>;

  /**
   * 科目
   */
  subject: string;
  /**
   * 科目（多）
   */
  subjectMul: Array<string>;

  /**
   * 章节
   */
  chapter: number;
  /**
   * 章节（多）
   */
  chapterMul: Array<string>;

  /**
   * 专题
   */
  special: number;
  /**
   * 专题（多）
   */
  specialMul: Array<string>;

  /**
   * 题目
   */
  question: string;

  /**
   * 题目
   */
  title?: string;

  /**
   * 图片
   */
  image: string;

  /**
   * 选项
   */
  options: string;

  /**
   * 选项格式化
   */
  optionsFormat: string;

  /**
   * 答案
   */
  answer: string;

  /**
   * 解析
   */
  analysis: string;

  /**
   * 解析音频
   */
  analysisAudio?: string;

  /**
   * 解析视频
   */
  analysisVideo?: string;

  /**
   * 技巧
   */
  skill: string;

  /**
   * 技巧音频
   */
  skillAudio?: string;

  /**
   * 技巧视频
   */
  skillVideo?: string;

  /**
   * 题型[单选-S,多选-M,判断-J]
   */
  type: string;

  /**
   * 关联试卷
   */
  papers?: string;

  /**
   * 排序
   */
  sort?: number;
}

export interface QuestionForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 车型
   */
  carType?: string | Array<string>;

  /**
   * 科目
   */
  subject?: string | Array<string>;

  /**
   * 章节
   */
  chapter?: string | Array<string>;

  /**
   * 专题
   */
  special?: string | Array<string>;

  /**
   * 题目
   */
  question?: string;

  /**
   * 图片
   */
  image: string;

  /**
   * 选项
   */
  options?: string | Array<Option>;

  /**
   * 答案
   */
  answer?: string | Array<string>;

  /**
   * 解析
   */
  analysis?: string;
  /**
   * 解析音频
   */
  analysisAudio?: string;
  /**
   * 解析视频
   */
  analysisVideo?: string;

  /**
   * 技巧
   */
  skill?: string;
  /**
   * 技巧音频
   */
  skillAudio?: string;
  /**
   * 技巧视频
   */
  skillVideo?: string;

  /**
   * 题型[单选-S,多选-M,判断-J]
   */
  type?: string;
}

export interface QuestionQuery extends PageQuery {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 车型
   */
  carType?: string;

  /**
   * 科目
   */
  subject?: string;

  /**
   * 章节
   */
  chapter?: string;

  /**
   * 专题
   */
  special?: string;

  /**
   * 题目
   */
  question?: string;

  /**
   * 题型[单选-S,多选-M,判断-J]
   */
  type?: string;

  /**
   * 答案
   */
  answer?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 选项
 */
export interface Option {
  prefix?: string;
  content?: string;
}
