/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ApprovalRecord: typeof import('./src/components/Process/approvalRecord.vue')['default']
    AreaCascader: typeof import('./src/components/Scjk/AreaCascader/index.vue')['default']
    AudioUpload: typeof import('./src/components/Scjk/AudioUpload/index.vue')['default']
    BpmnDesign: typeof import('./src/components/BpmnDesign/index.vue')['default']
    BpmnView: typeof import('./src/components/BpmnView/index.vue')['default']
    Breadcrumb: typeof import('./src/components/Breadcrumb/index.vue')['default']
    BuildCode: typeof import('./src/components/BuildCode/index.vue')['default']
    CarType: typeof import('./src/components/Scjk/CarType/index.vue')['default']
    Chapter: typeof import('./src/components/Scjk/Chapter/index.vue')['default']
    DictTag: typeof import('./src/components/DictTag/index.vue')['default']
    Editor: typeof import('./src/components/Editor/index.vue')['default']
    FileUpload: typeof import('./src/components/FileUpload/index.vue')['default']
    Hamburger: typeof import('./src/components/Hamburger/index.vue')['default']
    HeaderSearch: typeof import('./src/components/HeaderSearch/index.vue')['default']
    IconSelect: typeof import('./src/components/IconSelect/index.vue')['default']
    IFrame: typeof import('./src/components/iFrame/index.vue')['default']
    ImagePreview: typeof import('./src/components/ImagePreview/index.vue')['default']
    ImageUpload: typeof import('./src/components/ImageUpload/index.vue')['default']
    InfoForm: typeof import('./src/components/Scjk/InfoForm/index.vue')['default']
    LangSelect: typeof import('./src/components/LangSelect/index.vue')['default']
    LottieWeb: typeof import('./src/components/LottieWeb/LottieWeb.vue')['default']
    MultiInstanceUser: typeof import('./src/components/Process/multiInstanceUser.vue')['default']
    NewCarType: typeof import('./src/components/Scjk/NewCarType/index.vue')['default']
    NewSubject: typeof import('./src/components/Scjk/NewSubject/index.vue')['default']
    Pagination: typeof import('./src/components/Pagination/index.vue')['default']
    PaperConfig: typeof import('./src/components/Scjk/PaperConfig/index.vue')['default']
    ParentView: typeof import('./src/components/ParentView/index.vue')['default']
    Practice: typeof import('./src/components/Scjk/Practice/index.vue')['default']
    Pure: typeof import('./src/components/Pure/index.vue')['default']
    Question: typeof import('./src/components/Scjk/Question/index.vue')['default']
    Render: typeof import('./src/components/BuildCode/render.vue')['default']
    RightToolbar: typeof import('./src/components/RightToolbar/index.vue')['default']
    RoleSelect: typeof import('./src/components/RoleSelect/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Screenfull: typeof import('./src/components/Screenfull/index.vue')['default']
    SizeSelect: typeof import('./src/components/SizeSelect/index.vue')['default']
    Special: typeof import('./src/components/Scjk/Special/index.vue')['default']
    Student: typeof import('./src/components/Scjk/Student/index.vue')['default']
    Subject: typeof import('./src/components/Scjk/Subject/index.vue')['default']
    SubmitVerify: typeof import('./src/components/Process/submitVerify.vue')['default']
    SvgIcon: typeof import('./src/components/SvgIcon/index.vue')['default']
    TopNav: typeof import('./src/components/TopNav/index.vue')['default']
    TreeSelect: typeof import('./src/components/TreeSelect/index.vue')['default']
    UserSelect: typeof import('./src/components/UserSelect/index.vue')['default']
    VideoUpload: typeof import('./src/components/Scjk/VideoUpload/index.vue')['default']
    WxPayQrCode: typeof import('./src/components/wxPayQrCode/index.vue')['default']
    Xgplayer: typeof import('./src/components/Scjk/Xgplayer/index.vue')['default']
  }
}
