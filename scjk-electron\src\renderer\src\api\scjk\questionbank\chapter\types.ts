export interface ChapterVO {
  /**
   * ID
   */
  id: number;

  /**
   * 名称
   */
  name: string;

  /**
   * 权重
   */
  weight?: number;
}

export interface ChapterForm extends BaseEntity {
  /**
   * ID
   */
  id?: number;

  /**
   * 名称
   */
  name?: string;

  /**
   * 权重
   */
  weight?: number;
}

export interface ChapterQuery extends PageQuery {
  /**
   * 名称
   */
  name?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
