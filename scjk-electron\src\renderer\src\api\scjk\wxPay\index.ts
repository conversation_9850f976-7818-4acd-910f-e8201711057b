import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { OrderForm } from '../order/types';

/**
 * wx预下单
 * @param query
 * @returns {*}
 */

export const createOrder = (data: OrderForm) => {
  return request({
    url: '/scjk/order/createOrder',
    method: 'post',
    data: data
  });
};

/**
 * 订单查询
 * @param query
 * @returns {*}
 */

export const queryOrder = (data: OrderForm) => {
  return request({
    url: '/scjk/order/pay/queryOrder',
    method: 'post',
    data: data
  });
};
