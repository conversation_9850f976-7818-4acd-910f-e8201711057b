<script setup lang="ts">
import { ref } from 'vue';
import { useTransition } from '@vueuse/core';
import { ChatLineRound, Male, Ticket } from '@element-plus/icons-vue';
import { ScrollRankingBoard } from '@kjgl77/datav-vue3';
import { CapsuleChart } from '@kjgl77/datav-vue3';
import { getInfo } from '@/api/scjk/statistics';
import { BorderBox12 } from '@kjgl77/datav-vue3';
import { rank } from '@/api/scjk/paper/userPaper';
const statisticsInfo = ref({
  schoolCount: 0,
  studentCount: 0,
  teacherCount: 0,
  subscriberCount: 0,
  pcUserCount: 0,
  mpUserCount: 0
});
const outputValue = ref();

onMounted(() => {
  getInfo().then((res) => {
    statisticsInfo.value = res.data;
    outputValue.value = useTransition(statisticsInfo.value.studentCount, {
      duration: 1500
    });
    conf.data = res.data.platformInfo;
  });

  rank().then((res) => {
    let data = [];
    for (const item of res.data) {
      data.push({ name: item.name, value: item.score });
    }

    config.data = data;
  });
});

const config = reactive({
  textColor: '#005691',
  carousel: 'single',
  waitTime: 5000,
  color: '#005691',
  fontSize: 18,
  rowNum: 5,
  data: [],
  unit: '分'
});

const conf = reactive({
  textColor: '#3f72af',
  data: [],
  colors: ['#e062ae', '#fb7293', '#e690d1', '#32c5e9', '#96bfff'],
  unit: '人',
  labelNum: 8,
  fontSize: 18
});
</script>

<template>
  <div class="p-2">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card style="max-width: 480px">
          <el-statistic :value="statisticsInfo.schoolCount">
            <template #title>
              <div style="display: inline-flex; align-items: center; font-size: 18px">
                驾校数量
                <el-icon style="margin-left: 4px" :size="18">
                  <School />
                </el-icon>
              </div>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="max-width: 480px">
          <el-statistic :value="statisticsInfo.teacherCount">
            <template #title>
              <div style="display: inline-flex; align-items: center; font-size: 18px">
                教师人数
                <el-icon style="margin-left: 4px" :size="18">
                  <Avatar />
                </el-icon>
              </div>
            </template>
            <!--        <template #suffix>/100</template>-->
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="max-width: 480px">
          <el-statistic :value="outputValue">
            <template #title>
              <div style="display: inline-flex; align-items: center; font-size: 18px">
                学员人数
                <el-icon style="margin-left: 4px" :size="18">
                  <User />
                </el-icon>
              </div>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="max-width: 480px">
          <el-statistic :value="statisticsInfo.subscriberCount">
            <template #title>
              <div style="display: inline-flex; align-items: center; font-size: 18px">
                订阅人数
                <el-icon style="margin-left: 4px" :size="18">
                  <Ticket />
                </el-icon>
              </div>
            </template>
            <template #suffix>
              <el-icon style="vertical-align: -0.125em">
                <ChatLineRound />
              </el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="16">
        <border-box12>
          <el-card style="text-align: left; min-height: 700px">
            <template #header>排行榜</template>
            <!--        <div w50rem h24rem p3 flex="~ col" justify-center items-center bg-white>-->

            <div dv-bg>
              <scroll-ranking-board :config="config" style="width: 100%; height: 600px" />
            </div>
            <!--        </div>-->
          </el-card>
        </border-box12>
      </el-col>
      <el-col :span="8">
        <el-card style="text-align: left">
          <template #header>电脑统计</template>
          <capsule-chart :config="conf" style="width: 25rem; height: 15rem" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped lang="scss">
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.el-col {
  border-radius: 4px;
  text-align: center;
}
</style>
