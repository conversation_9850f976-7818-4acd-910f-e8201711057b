<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="激活账号" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入激活账号" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="生效时间" prop="effectiveTime">
            <el-date-picker v-model="queryParams.effectiveTime" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择生效时间" />
          </el-form-item>
          <el-form-item label="到期时间" prop="expiredDate">
            <el-date-picker v-model="queryParams.expiredDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择到期时间" />
          </el-form-item>
          <el-form-item label="订单号" prop="orderNo">
            <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="开通方式" prop="activeWay">
            <el-select v-model="queryParams.activeWay" placeholder="请选择开通方式" clearable>
              <el-option v-for="dict in subscription_active_way" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="订阅状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择订阅状态" clearable>
              <el-option v-for="dict in subscription_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['scjk:subscriptionRecord:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['scjk:subscriptionRecord:edit']"
              >修改</el-button
            >
          </el-col> -->
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:subscriptionRecord:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['scjk:subscriptionRecord:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="subscriptionRecordList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="false" label="主键" align="center" prop="id" />
        <el-table-column label="订单号" align="center" prop="orderNo" width="180" />
        <el-table-column label="订阅套餐" align="center" prop="subscriptionPlanName" />
        <el-table-column label="激活日期" align="center" prop="activeDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.activeDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="激活账号" align="center" prop="userName" />
        <el-table-column label="生效时间" align="center" prop="effectiveTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.effectiveTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="到期时间" align="center" prop="expiredDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.expiredDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="下次付款日期" align="center" prop="nextPayDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.nextPayDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>

        <el-table-column label="开通方式" align="center" prop="activeWay">
          <template #default="scope">
            <dict-tag :options="subscription_active_way" :value="scope.row.activeWay" />
          </template>
        </el-table-column>
        <el-table-column label="订阅状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="subscription_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <!-- <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['scjk:subscriptionRecord:edit']"></el-button>
            </el-tooltip> -->
            <el-tooltip content="删除" placement="top">
              <el-button
                v-hasPermi="['scjk:subscriptionRecord:remove']"
                link
                type="danger"
                icon="Delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="取消该订阅" placement="top">
              <el-button
                v-if="scope.row.status === 'active'"
                v-hasPermi="['scjk:subscriptionRecord:remove']"
                link
                type="danger"
                icon="CloseBold"
                @click="cancelTrial(scope.row)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改订阅信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" append-to-body>
      <el-form ref="subscriptionRecordFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="订阅套餐" prop="subscriptionPlanId">
          <el-input v-model="form.subscriptionPlanId" placeholder="请输入订阅套餐" />
        </el-form-item>
        <el-form-item label="激活日期" prop="activeDate">
          <el-date-picker v-model="form.activeDate" clearable type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择激活日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="开通人" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入开通人" />
        </el-form-item>
        <el-form-item label="激活账号" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入激活账号" />
        </el-form-item>
        <el-form-item label="生效时间" prop="effectiveTime">
          <el-date-picker v-model="form.effectiveTime" clearable type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择生效时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="到期时间" prop="expiredDate">
          <el-date-picker v-model="form.expiredDate" clearable type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择到期时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="下次付款日期" prop="nextPayDate">
          <el-date-picker v-model="form.nextPayDate" clearable type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择下次付款日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="form.orderNo" placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="开通方式" prop="activeWay">
          <el-select v-model="form.activeWay" placeholder="请选择开通方式">
            <el-option v-for="dict in subscription_active_way" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订阅状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择订阅状态">
            <el-option v-for="dict in subscription_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SubscriptionRecord" lang="ts">
import {
  listSubscriptionRecord,
  getSubscriptionRecord,
  delSubscriptionRecord,
  addSubscriptionRecord,
  updateSubscriptionRecord,
  unSubscribe
} from '@/api/scjk/subscriptionRecord';
import { SubscriptionRecordVO, SubscriptionRecordQuery, SubscriptionRecordForm } from '@/api/scjk/subscriptionRecord/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { subscription_active_way, subscription_status } = toRefs<any>(proxy?.useDict('subscription_active_way', 'subscription_status'));

const subscriptionRecordList = ref<SubscriptionRecordVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const subscriptionRecordFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: SubscriptionRecordForm = {
  id: undefined,
  subscriptionPlanId: undefined,
  activeDate: undefined,
  userId: undefined,
  userName: undefined,
  effectiveTime: undefined,
  expiredDate: undefined,
  nextPayDate: undefined,
  orderNo: undefined,
  activeWay: undefined,
  status: undefined,
  subscriptionPlanName: undefined
};
const data = reactive<PageData<SubscriptionRecordForm, SubscriptionRecordQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    subscriptionPlanId: undefined,
    activeDate: undefined,
    userId: undefined,
    userName: undefined,
    effectiveTime: undefined,
    expiredDate: undefined,
    nextPayDate: undefined,
    orderNo: undefined,
    activeWay: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    subscriptionPlanId: [{ required: true, message: '订阅套餐不能为空', trigger: 'blur' }],
    activeDate: [{ required: true, message: '激活日期不能为空', trigger: 'blur' }],
    userId: [{ required: true, message: '开通人不能为空', trigger: 'blur' }],
    userName: [{ required: true, message: '激活账号不能为空', trigger: 'blur' }],
    effectiveTime: [{ required: true, message: '生效时间不能为空', trigger: 'blur' }],
    expiredDate: [{ required: true, message: '到期时间不能为空', trigger: 'blur' }],
    nextPayDate: [{ required: true, message: '下次付款日期不能为空', trigger: 'blur' }],
    orderNo: [{ required: true, message: '订单号不能为空', trigger: 'blur' }],
    activeWay: [{ required: true, message: '开通方式不能为空', trigger: 'change' }],
    status: [{ required: true, message: '订阅状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询订阅信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listSubscriptionRecord(queryParams.value);
  subscriptionRecordList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  subscriptionRecordFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: SubscriptionRecordVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加订阅信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: SubscriptionRecordVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getSubscriptionRecord(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改订阅信息';
};
const cancelTrial = async (row: any) => {
  if (row.activeWay === '1') {
    await proxy?.$modal.msgError('该用户已付费开通,无法取消试用');
    return;
  }
  await proxy?.$modal.confirm('是否确认取消账号"' + row.userName + '"的订阅吗?');
  const unSubForm = { subscriptionPlanId: undefined, userId: undefined, userName: undefined };
  unSubForm.subscriptionPlanId = row.subscriptionPlanId;
  unSubForm.userId = row.userId;
  unSubForm.userName = row.userName;
  const res = await unSubscribe(unSubForm);
  if (res.code === 200) {
    proxy?.$modal.msgSuccess('取消成功');
    await getList();
  } else {
    proxy?.$modal.msgSuccess('取消失败');
  }
};
/** 提交按钮 */
const submitForm = () => {
  subscriptionRecordFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateSubscriptionRecord(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addSubscriptionRecord(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('修改成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: SubscriptionRecordVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除订阅信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delSubscriptionRecord(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'scjk/subscriptionRecord/export',
    {
      ...queryParams.value
    },
    `subscriptionRecord_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
