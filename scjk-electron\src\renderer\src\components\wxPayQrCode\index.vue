<template>
  <div class="pay-weixin">
    <img :src="qrCodeImgUrl" />
    <div class="pay-weixin-text">
      请扫码支付<span class="pay-weixin-amount">{{ props.formData.orderAmount }}</span> 元
    </div>
  </div>
</template>
<script setup lang="ts">
import { createOrder } from '@/api/scjk/wxPay';
import { re } from 'mathjs';
import QRCode from 'qrcode';
const qrCodeImgUrl = ref('');
const socket = ref(null);
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
});
onMounted(async () => {
  await createOrder(props.formData).then((res) => {
    if (res.code === 500) {
      ElMessage.error('创建订单失败:' + res);
      return;
    }
    let img = QRCode.toDataURL(res.codeUrl);
    img.then((url) => {
      qrCodeImgUrl.value = url;
    });
  });
});
</script>
<style lang="scss" scoped>
.pay-weixin {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background-color: #f5f5f5;
}
.pay-weixin-text {
  font-size: 16px;
  color: #333;
  margin-top: 10px;
}
.pay-weixin-amount {
  font-size: 17px;
  color: #ff6700;
  margin-left: 5px;
}
</style>
