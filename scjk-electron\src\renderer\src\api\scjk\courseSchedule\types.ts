export interface CourseScheduleVO {
  /**
   * 主键
   */
  id: string | number;

  courseID: string | number;

  /**
   * 课程名称
   */
  courseName: string;

  /**
   * 讲师
   */
  teacherId: string;

  /**
   * 课程简介
   */
  courseIntro: string;

  /**
   * 开始日期
   */
  startDate: string;

  /**
   * 结束日期
   */
  endDate: string;

  /**
   * 备注
   */
  remark: string;
}

export interface CourseScheduleForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 课程名称
   */
  courseName?: string;

  /**
   * 讲师
   */
  teacherId?: string;

  /**
   * 课程简介
   */
  courseIntro?: string;

  /**
   * 开始日期
   */
  startDate?: string;

  /**
   * 结束日期
   */
  endDate?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface CourseScheduleQuery extends PageQuery {
  /**
   * 课程名称
   */
  courseName?: string;

  /**
   * 讲师
   */
  teacherId?: string | number;

  /**
   * 课程类型
   */
  courseType: string;

  /**
   * 课程简介
   */
  courseIntro?: string;

  /**
   * 开始日期
   */
  startDate?: string;

  /**
   * 结束日期
   */
  endDate?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
