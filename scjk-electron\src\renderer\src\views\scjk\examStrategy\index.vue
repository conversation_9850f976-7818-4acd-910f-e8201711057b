<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave"> </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:examStrategy:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:examStrategy:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:examStrategy:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:examStrategy:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="examStrategyList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="攻略标题" align="center" prop="title" />
        <el-table-column label="图标" align="center" prop="iconUrl">
          <template #default="scope">
            <div v-if="scope.row.iconUrl" style="display: flex; justify-content: center; align-items: center; min-height: 32px;">
              <!-- 如果是SVG代码 -->
              <div 
                v-if="scope.row.iconUrl.includes('<svg')" 
                v-html="scope.row.iconUrl" 
                style="width: 24px; height: 24px; line-height: 1;"
                class="svg-icon-wrapper"
              ></div>
              <!-- 如果是图片URL -->
              <img 
                v-else-if="scope.row.iconUrl.startsWith('http')" 
                :src="scope.row.iconUrl" 
                alt="图标" 
                style="width: 24px; height: 24px; object-fit: contain;" 
                @error="$event.target.style.display='none'" 
              />
              <!-- 其他情况显示文本 -->
              <span v-else style="font-size: 12px; color: #999;">{{ scope.row.iconUrl.substring(0, 10) }}...</span>
            </div>
            <span v-else style="color: #ccc;">-</span>
          </template>
        </el-table-column>
        <el-table-column label="背景颜色" align="center" prop="bgColor">
          <template #default="scope">
            <div
              v-if="scope.row.bgColor"
              :style="{
                backgroundColor: scope.row.bgColor,
                width: '30px',
                height: '20px',
                borderRadius: '4px',
                border: '1px solid #ddd',
                margin: '0 auto'
              }"
            ></div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-switch v-model="scope.row.status" active-value="0" inactive-value="1" @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:examStrategy:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:examStrategy:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改考试攻略配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="examStrategyFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="攻略标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入攻略标题" />
        </el-form-item>
        <el-form-item label="图标" prop="iconUrl">
          <el-input v-model="form.iconUrl" placeholder="请输入图标URL或SVG代码" type="textarea" :rows="3" />
          <div style="margin-top: 8px; font-size: 12px; color: #909399; line-height: 1.5;">
            <div><strong>使用说明：</strong></div>
            <div>• 支持图片URL：https://example.com/icon.png</div>
            <div>• 支持SVG代码：&lt;svg&gt;...&lt;/svg&gt;</div>
            <div><strong>推荐免费商用图标网站：</strong></div>
            <div>• <a href="https://www.iconfont.cn" target="_blank" style="color: #409EFF;">阿里巴巴矢量图标库</a> - 国内最大的图标库</div>
            <div>• <a href="https://iconpark.oceanengine.com" target="_blank" style="color: #409EFF;">IconPark</a> - 字节跳动开源图标库</div>
            <div>• <a href="https://remixicon.com" target="_blank" style="color: #409EFF;">Remix Icon</a> - 开源图标集</div>
          </div>
        </el-form-item>
        <el-form-item label="背景颜色" prop="bgColor">
          <el-color-picker v-model="form.bgColor" />
        </el-form-item>
        <el-form-item label="内容">
          <editor v-model="form.content" :min-height="192" />
        </el-form-item>
        <el-form-item label="排序号" prop="sortNum">
          <el-input-number v-model="form.sortNum" :min="0" :max="9999" placeholder="请输入排序号(升序)" style="width: 100%" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.svg-icon-wrapper {
  display: inline-block;
  overflow: hidden;
}

.svg-icon-wrapper :deep(svg) {
  width: 24px !important;
  height: 24px !important;
  vertical-align: middle;
}

.svg-icon-wrapper :deep(svg *) {
  max-width: 24px;
  max-height: 24px;
}
</style>

<script setup lang="ts">
import { listExamStrategy, getExamStrategy, delExamStrategy, addExamStrategy, updateExamStrategy } from '@/api/scjk/examStrategy';
import { ExamStrategyVO, ExamStrategyQuery, ExamStrategyForm } from '@/api/scjk/examStrategy/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const examStrategyList = ref<ExamStrategyVO[]>([]);

// 预定义颜色
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const examStrategyFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ExamStrategyForm = {
  id: undefined,
  title: undefined,
  iconUrl: undefined,
  bgColor: undefined,
  content: undefined,
  sortNum: undefined,
  status: undefined,
  remark: undefined,
  deleted: undefined
};
const data = reactive<PageData<ExamStrategyForm, ExamStrategyQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deleted: undefined,
    params: {}
  },
  rules: {
    title: [{ required: true, message: '攻略标题不能为空', trigger: 'blur' }],
    iconUrl: [{ required: true, message: '图标不能为空', trigger: 'change' }],
    bgColor: [{ required: true, message: '背景颜色不能为空', trigger: 'change' }],
    content: [{ required: true, message: '攻略内容不能为空', trigger: 'blur' }],
    sortNum: [{ required: true, message: '排序号不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询考试攻略配置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listExamStrategy(queryParams.value);
  examStrategyList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  examStrategyFormRef.value?.resetFields();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ExamStrategyVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加考试攻略配置';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ExamStrategyVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getExamStrategy(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改考试攻略配置';
};

/** 提交按钮 */
const submitForm = () => {
  examStrategyFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateExamStrategy(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addExamStrategy(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 状态变更处理 */
const handleStatusChange = async (row: ExamStrategyVO) => {
  // 检查是否为有效的数据行，避免在数据初始化时触发
  if (!row.id) {
    return
  }
  
  const action = row.status === '0' ? '停用' : '启用'
  const result = await ElMessageBox.confirm(
    `确认要${action}'${row.title}'吗？`,
    '系统提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).catch(() => false)

  if (result) {
    // 直接使用当前开关的状态值发送给后台
    await updateExamStrategy({ ...row, status: row.status })
    await getList()
  } else {
    // 如果用户取消，需要恢复开关状态
    row.status = row.status === '0' ? '1' : '0'
  }
}

/** 删除按钮操作 */
const handleDelete = async (row?: ExamStrategyVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除考试攻略配置编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delExamStrategy(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/examStrategy/export',
    {
      ...queryParams.value
    },
    `examStrategy_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
