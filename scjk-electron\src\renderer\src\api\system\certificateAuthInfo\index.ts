import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CertificateAuthInfoVO, CertificateAuthInfoForm, CertificateAuthInfoQuery } from '@/api/system/certificateAuthInfo/types';

/**
 * 查询证书授权信息列表
 * @param query
 * @returns {*}
 */

export const listCertificateAuthInfo = (query?: CertificateAuthInfoQuery): AxiosPromise<CertificateAuthInfoVO[]> => {
  return request({
    url: '/system/certificateAuthInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询证书授权信息详细
 * @param id
 */
export const getCertificateAuthInfo = (id: string | number): AxiosPromise<CertificateAuthInfoVO> => {
  return request({
    url: '/system/certificateAuthInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增证书授权信息
 * @param data
 */
export const addCertificateAuthInfo = (data: CertificateAuthInfoForm) => {
  return request({
    url: '/system/certificateAuthInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改证书授权信息
 * @param data
 */
export const updateCertificateAuthInfo = (data: CertificateAuthInfoForm) => {
  return request({
    url: '/system/certificateAuthInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除证书授权信息
 * @param id
 */
export const delCertificateAuthInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/certificateAuthInfo/' + id,
    method: 'delete'
  });
};
export const genCertCode = () => {
  return request({
    url: '/system/certificateAuthInfo/genCertCode',
    method: 'get'
  });
};
