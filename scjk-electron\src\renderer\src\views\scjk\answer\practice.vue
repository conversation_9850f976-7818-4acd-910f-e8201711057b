<template>
  <div class="p-2">
    <practice
      ref="practiceRef"
      :info="info"
      :list="list"
      error
      :highlight-multi="highlightMulti"
      @submit="submitAnswer"
      @back="goBack"
      @review="doWrong"
    ></practice>
  </div>
</template>

<script setup name="AnswerPractice" lang="ts">
import useUserStore from '@/store/modules/user';
import { answerByGroup, addBatch } from '@/api/scjk/answer';
import Practice from '@/components/Scjk/Practice/index.vue';
import { PracticeInfoVO, PracticeQuestionVO } from '@/api/scjk/practice/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const name = ref<string>(null);
const carType = ref<string | number>(null);
const carTypeName = ref<string>(null);
const subject = ref<string | number>(null);
const subjectName = ref<string>(null);
const highlightMulti = ref<boolean>(false);
const practiceRef = ref();

const info = computed(() => {
  return {
    avatar: useUserStore().avatar,
    nickname: useUserStore().nickname,
    sex: useUserStore().sex,
    carTypeName: carTypeName.value,
    subjectName: subjectName.value,
    time: 45
  } as PracticeInfoVO;
});
const list = ref<PracticeQuestionVO>(null);

/**
 * 加载错题
 */
const loadQuestions = () => {
  if (
    name.value === undefined ||
    name.value === null ||
    name.value === '' ||
    carType.value === undefined ||
    carType.value === null ||
    carType.value === '' ||
    subject.value === undefined ||
    subject.value === null ||
    subject.value === ''
  ) {
    proxy?.$modal.msgError('未知错题分组!');
    return;
  }

  answerByGroup(name.value, carType.value, subject.value).then((res) => {
    // 如果没有错题，回到列表页
    if (res.data.length < 1) {
      proxy?.$modal.msgError('没有错题!');
      proxy?.$router.push({
        path: '/answerLog'
      });
      return;
    }

    list.value = res.data;
  });
};

/**
 * 提交
 * @param questionVOs
 */
const submitAnswer = (questionVOs: Array<PracticeQuestionVO>) => {
  practiceRef.value.closeFullscreen();
  addBatch(questionVOs).then((res) => {});
};

/**
 * 返回
 */
const goBack = () => {
  proxy?.$tab.closePage(proxy?.$route);
  proxy?.$router.push({ path: '/answerLog' });
};

/**
 * 错题
 */
const doWrong = () => {
  loadQuestions();
};

onMounted(() => {
  name.value = proxy?.$route.query.name;
  carType.value = proxy?.$route.query.carType;
  carTypeName.value = proxy?.$route.query.carTypeName;
  subject.value = proxy?.$route.query.subject;
  subjectName.value = proxy?.$route.query.subjectName;
  highlightMulti.value = subjectName.value.includes('科目三');

  loadQuestions();
});
</script>
<style lang="scss" scoped>
.exam-container {
  padding: 10px;
  min-height: calc(100vh - 300px);
  width: 100%;
  position: relative;
  overflow: hidden;
}
</style>
